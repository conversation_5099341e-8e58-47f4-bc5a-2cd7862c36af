using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using HappyWechat.Application.Options;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified;

/// <summary>
/// 简化的Redis队列服务实现
/// 直接队列处理，无延时队列复杂性
/// </summary>
public class SimplifiedQueueService : ISimplifiedQueueService
{
    private readonly IDatabase _database;
    private readonly ILogger<SimplifiedQueueService> _logger;
    private readonly RedisQueueOptions _options;
    private readonly JsonSerializerOptions _jsonOptions;

    // 速率限制缓存 - 记录每个微信账户的最后操作时间
    private readonly Dictionary<Guid, DateTime> _lastOperationTimes = new();
    private readonly object _rateLimitLock = new();

    public SimplifiedQueueService(
        IServiceProvider serviceProvider,
        ILogger<SimplifiedQueueService> logger,
        IOptions<RedisQueueOptions> options)
    {
        var connectionMultiplexer = serviceProvider.GetRequiredService<IConnectionMultiplexer>();
        _database = connectionMultiplexer.GetDatabase(3); // 使用数据库3作为消息队列数据库
        _logger = logger;
        _options = options.Value;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };

        // 🔧 注释冗余的队列服务初始化日志 - 减少日志噪音
        // _logger.LogInformation("🚀 简化队列服务已初始化 - KeyPrefix: {KeyPrefix}", _options.KeyPrefix);
    }

    public async Task<string> EnqueueAsync<T>(Guid wxManagerId, string queueType, T data, CancellationToken cancellationToken = default) where T : class
    {
        return await EnqueueAsync(wxManagerId, queueType, data, 0, 3, null, cancellationToken);
    }

    public async Task<string> EnqueueAsync<T>(Guid wxManagerId, string queueType, T data, int priority = 0, int maxRetryCount = 3, Dictionary<string, object>? properties = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            // 应用速率限制
            await ApplyRateLimitAsync(wxManagerId);

            var message = new SimplifiedQueueMessage<T>
            {
                Id = Guid.NewGuid().ToString(),
                WxManagerId = wxManagerId,
                QueueType = queueType,
                Data = data,
                CreatedAt = DateTime.UtcNow,
                Priority = priority,
                MaxRetryCount = maxRetryCount,
                Properties = properties ?? new Dictionary<string, object>(),
                RetryCount = 0,
                Status = MessageStatus.Pending
            };

            var queueName = GenerateQueueName(wxManagerId, queueType);
            var json = JsonSerializer.Serialize(message, _jsonOptions);

            // 序列化完成

            // 根据优先级决定入队方式
            if (priority > 0)
            {
                await _database.ListLeftPushAsync(queueName, json); // 高优先级从左边入队
            }
            else
            {
                await _database.ListRightPushAsync(queueName, json); // 普通优先级从右边入队
            }

            // 设置队列过期时间，确保队列不会无限期存在
            await _database.KeyExpireAsync(queueName, TimeSpan.FromSeconds(_options.DefaultQueueTtl));

            // 🔧 注释冗余的入队日志 - 减少日志噪音，保留错误日志
            // _logger.LogInformation("📝 消息已入队 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, MessageId: {MessageId}, Priority: {Priority}, QueueName: {QueueName}",
            //     wxManagerId, queueType, message.Id, priority, queueName);
            
            return message.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 消息入队失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            throw;
        }
    }

    public async Task<string> EnqueueDelayedAsync<T>(Guid wxManagerId, string queueType, T data, int delayMs, int priority = 0, int maxRetryCount = 3, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            await ApplyRateLimitAsync(wxManagerId);

            var executeAt = DateTimeOffset.UtcNow.AddMilliseconds(delayMs).ToUnixTimeMilliseconds();
            var message = new SimplifiedQueueMessage<T>
            {
                Id = Guid.NewGuid().ToString(),
                WxManagerId = wxManagerId,
                QueueType = queueType,
                Data = data,
                CreatedAt = DateTime.UtcNow,
                DelayUntil = executeAt,
                Priority = priority,
                MaxRetryCount = maxRetryCount,
                Status = MessageStatus.Delayed
            };

            var delayedQueueName = GenerateDelayedQueueName(wxManagerId);
            var json = JsonSerializer.Serialize(message, _jsonOptions);
            
            // 使用 SortedSet 存储延时消息，以执行时间为排序依据
            await _database.SortedSetAddAsync(delayedQueueName, json, executeAt);
            
            _logger.LogInformation("⏰ 延时消息已入队 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, MessageId: {MessageId}, DelayMs: {DelayMs}", 
                wxManagerId, queueType, message.Id, delayMs);
            
            return message.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 延时消息入队失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            throw;
        }
    }

    public async Task<SimplifiedQueueMessage<T>?> DequeueAsync<T>(Guid wxManagerId, string queueType, int timeoutSeconds = 10, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var queueName = GenerateQueueName(wxManagerId, queueType);
            
            // 如果有超时设置，使用阻塞出队
            RedisValue result;
            if (timeoutSeconds > 0)
            {
                // 使用阻塞式右弹出
                var keyValuePair = await _database.ListRightPopLeftPushAsync(queueName, queueName + ":processing");
                result = keyValuePair;
            }
            else
            {
                result = await _database.ListRightPopAsync(queueName);
            }
            
            if (!result.HasValue)
                return null;

            var message = JsonSerializer.Deserialize<SimplifiedQueueMessage<T>>(result!, _jsonOptions);
            
            if (message != null)
            {
                message.Status = MessageStatus.Processing;
                message.ProcessingStartTime = DateTime.UtcNow;
                message.LastUpdated = DateTime.UtcNow;
                
                // 🔧 注释冗余的出队日志 - 减少日志噪音
                // _logger.LogDebug("📤 消息已出队 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, MessageId: {MessageId}",
                //     wxManagerId, queueType, message.Id);
            }
            
            return message;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 消息出队失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            throw;
        }
    }

    public async Task<List<SimplifiedQueueMessage<T>>> DequeueBatchAsync<T>(Guid wxManagerId, string queueType, int batchSize = 10, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var results = new List<SimplifiedQueueMessage<T>>();
            var queueName = GenerateQueueName(wxManagerId, queueType);
            
            for (int i = 0; i < batchSize; i++)
            {
                var result = await _database.ListRightPopAsync(queueName);
                if (!result.HasValue)
                    break;

                var message = JsonSerializer.Deserialize<SimplifiedQueueMessage<T>>(result!, _jsonOptions);
                if (message != null)
                {
                    results.Add(message);
                }
                else
                {
                    _logger.LogError("❌ 反序列化失败 - JSON: {Json}", result.ToString());
                }
            }
            
            if (results.Count > 0)
            {
                // 🔧 注释冗余的批量出队日志 - 减少日志噪音
                // _logger.LogInformation("📦 批量出队 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, Count: {Count}",
                //     wxManagerId, queueType, results.Count);
            }
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 批量出队失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            throw;
        }
    }

    public async Task<long> GetQueueLengthAsync(Guid wxManagerId, string queueType, CancellationToken cancellationToken = default)
    {
        try
        {
            var queueName = GenerateQueueName(wxManagerId, queueType);
            return await _database.ListLengthAsync(queueName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取队列长度失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            return 0;
        }
    }

    public async Task ClearQueueAsync(Guid wxManagerId, string queueType, CancellationToken cancellationToken = default)
    {
        try
        {
            var queueName = GenerateQueueName(wxManagerId, queueType);
            await _database.KeyDeleteAsync(queueName);
            
            _logger.LogInformation("🧹 队列已清空 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 清空队列失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            throw;
        }
    }

    public async Task<Dictionary<string, long>> GetAllQueueStatusAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new Dictionary<string, long>();
            var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
            var pattern = $"{_options.KeyPrefix}:queue:*";
            
            await Task.Run(() =>
            {
                foreach (var key in server.Keys(pattern: pattern, pageSize: 100))
                {
                    var length = _database.ListLength(key);
                    if (length > 0)
                    {
                        result[key.ToString()] = length;
                    }
                }
            }, cancellationToken);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取队列状态失败");
            return new Dictionary<string, long>();
        }
    }

    /// <summary>
    /// 应用速率限制
    /// </summary>
    private async Task ApplyRateLimitAsync(Guid wxManagerId)
    {
        lock (_rateLimitLock)
        {
            if (_lastOperationTimes.TryGetValue(wxManagerId, out var lastTime))
            {
                var timeSinceLastOperation = DateTime.UtcNow - lastTime;
                var minInterval = TimeSpan.FromMilliseconds(500); // 默认500ms间隔
                
                if (timeSinceLastOperation < minInterval)
                {
                    var delayNeeded = minInterval - timeSinceLastOperation;
                    Thread.Sleep(delayNeeded); // 简单的同步等待
                }
            }
            
            _lastOperationTimes[wxManagerId] = DateTime.UtcNow;
        }
    }

    public async Task AckAsync(Guid wxManagerId, string queueType, string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("✅ 消息已确认 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, MessageId: {MessageId}", 
                wxManagerId, queueType, messageId);
            
            var statusKey = $"{_options.KeyPrefix}:msg_status:{messageId}";
            await _database.StringSetAsync(statusKey, "completed", TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 确认消息失败 - MessageId: {MessageId}", messageId);
            throw;
        }
    }

    public async Task NackAsync(Guid wxManagerId, string queueType, string messageId, string reason = "", CancellationToken cancellationToken = default)
    {
        try
        {
            var deadLetterQueueName = GenerateDeadLetterQueueName(wxManagerId);
            var deadLetterMessage = new
            {
                MessageId = messageId,
                QueueType = queueType,
                Reason = reason,
                RejectedAt = DateTime.UtcNow
            };
            
            var json = JsonSerializer.Serialize(deadLetterMessage, _jsonOptions);
            await _database.ListLeftPushAsync(deadLetterQueueName, json);
            
            _logger.LogWarning("☠️ 消息已拒绝并移入死信队列 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, MessageId: {MessageId}, Reason: {Reason}", 
                wxManagerId, queueType, messageId, reason);
                
            var statusKey = $"{_options.KeyPrefix}:msg_status:{messageId}";
            await _database.StringSetAsync(statusKey, "dead_letter", TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 拒绝消息失败 - MessageId: {MessageId}", messageId);
            throw;
        }
    }

    public async Task RequeueAsync<T>(Guid wxManagerId, string queueType, SimplifiedQueueMessage<T> message, int delayMs = 0, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            message.RetryCount++;
            message.LastUpdated = DateTime.UtcNow;
            message.Status = message.CanRetry ? MessageStatus.Retried : MessageStatus.DeadLetter;
            
            if (!message.CanRetry)
            {
                await NackAsync(wxManagerId, queueType, message.Id, "超过最大重试次数", cancellationToken);
                return;
            }
            
            if (delayMs > 0)
            {
                message.DelayUntil = DateTimeOffset.UtcNow.AddMilliseconds(delayMs).ToUnixTimeMilliseconds();
                message.Status = MessageStatus.Delayed;
                
                var delayedQueueName = GenerateDelayedQueueName(wxManagerId);
                var json = JsonSerializer.Serialize(message, _jsonOptions);
                await _database.SortedSetAddAsync(delayedQueueName, json, message.DelayUntil.Value);
            }
            else
            {
                var queueName = GenerateQueueName(wxManagerId, queueType);
                var json = JsonSerializer.Serialize(message, _jsonOptions);
                await _database.ListLeftPushAsync(queueName, json);

                // 设置队列过期时间
                await _database.KeyExpireAsync(queueName, TimeSpan.FromSeconds(_options.DefaultQueueTtl));
            }
            
            _logger.LogInformation("🔄 消息已重新入队 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, MessageId: {MessageId}, RetryCount: {RetryCount}", 
                wxManagerId, queueType, message.Id, message.RetryCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 重新入队失败 - MessageId: {MessageId}", message.Id);
            throw;
        }
    }

    public async Task DeleteQueueAsync(Guid wxManagerId, string queueType, CancellationToken cancellationToken = default)
    {
        try
        {
            var queueName = GenerateQueueName(wxManagerId, queueType);
            await _database.KeyDeleteAsync(queueName);
            
            _logger.LogInformation("🗑️ 队列已删除 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 删除队列失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            throw;
        }
    }

    public async Task<SimplifiedQueueStatistics?> GetQueueStatisticsAsync(Guid wxManagerId, string queueType, CancellationToken cancellationToken = default)
    {
        try
        {
            var queueName = GenerateQueueName(wxManagerId, queueType);
            var deadLetterQueueName = GenerateDeadLetterQueueName(wxManagerId);
            var delayedQueueName = GenerateDelayedQueueName(wxManagerId);
            
            var length = await _database.ListLengthAsync(queueName);
            var deadLetterCount = await _database.ListLengthAsync(deadLetterQueueName);
            var delayedCount = await _database.SortedSetLengthAsync(delayedQueueName);
            
            var statsKey = $"{_options.KeyPrefix}:stats:{wxManagerId}:{queueType}";
            var statsJson = await _database.StringGetAsync(statsKey);
            
            var stats = new SimplifiedQueueStatistics
            {
                QueueName = queueName,
                WxManagerId = wxManagerId,
                QueueType = queueType,
                Length = length,
                DeadLetterCount = deadLetterCount,
                DelayedCount = delayedCount,
                Status = QueueStatus.Active,
                LastUpdated = DateTime.UtcNow
            };
            
            if (statsJson.HasValue)
            {
                var existingStats = JsonSerializer.Deserialize<SimplifiedQueueStatistics>(statsJson!, _jsonOptions);
                if (existingStats != null)
                {
                    stats.TotalProcessed = existingStats.TotalProcessed;
                    stats.TotalFailed = existingStats.TotalFailed;
                    stats.AverageProcessingTimeMs = existingStats.AverageProcessingTimeMs;
                }
            }
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取队列统计失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, queueType);
            return null;
        }
    }

    public async Task<List<string>> GetAllQueueNamesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new List<string>();
            var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
            var pattern = $"{_options.KeyPrefix}:queue:*";

            await Task.Run(() =>
            {
                foreach (var key in server.Keys(pattern: pattern, pageSize: 100))
                {
                    result.Add(key.ToString());
                }
            }, cancellationToken);

            _logger.LogDebug("🔍 Redis Keys扫描完成 - Pattern: {Pattern}, 找到队列数量: {Count}", pattern, result.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取队列名称失败");
            return new List<string>();
        }
    }

    /// <summary>
    /// 检查特定队列是否存在且有消息
    /// </summary>
    public async Task<bool> QueueExistsAsync(string queueName)
    {
        try
        {
            var exists = await _database.KeyExistsAsync(queueName);
            if (exists)
            {
                var length = await _database.ListLengthAsync(queueName);
                _logger.LogDebug("🔍 队列存在检查 - QueueName: {QueueName}, 存在: {Exists}, 长度: {Length}",
                    queueName, exists, length);
                return length > 0;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查队列存在失败 - QueueName: {QueueName}", queueName);
            return false;
        }
    }

    public string GenerateQueueName(Guid wxManagerId, string queueType)
    {
        return $"{_options.KeyPrefix}:queue:{wxManagerId}:{queueType}";
    }

    public string GenerateDelayedQueueName(Guid wxManagerId)
    {
        return $"{_options.KeyPrefix}:delayed:{wxManagerId}";
    }

    public string GenerateDeadLetterQueueName(Guid wxManagerId)
    {
        return $"{_options.KeyPrefix}:dead_letter:{wxManagerId}";
    }

    public async Task<bool> SetKeyAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.StringSetAsync(key, value, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 设置键值失败 - Key: {Key}", key);
            return false;
        }
    }

    public async Task<string?> GetKeyAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _database.StringGetAsync(key);
            return result.HasValue ? result.ToString() : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取键值失败 - Key: {Key}", key);
            return null;
        }
    }

    public async Task<bool> DeleteKeyAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyDeleteAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 删除键失败 - Key: {Key}", key);
            return false;
        }
    }

    public IDatabase GetDatabase()
    {
        return _database;
    }

    public async Task<SimplifiedQueueStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new SimplifiedQueueStatistics
            {
                QueueName = "global",
                QueueType = "global",
                Length = 0,
                ProcessingCount = 0,
                DeadLetterCount = 0,
                DelayedCount = 0,
                TotalProcessed = 0,
                TotalFailed = 0,
                AverageProcessingTimeMs = 0,
                Status = QueueStatus.Active,
                LastUpdated = DateTime.UtcNow
            };

            // Get basic queue stats - this is a simplified implementation
            // In a real scenario, you'd collect stats from all queues
            var allQueues = await GetAllQueueStatusAsync(cancellationToken);
            foreach (var queue in allQueues)
            {
                stats.Length += queue.Value;
            }

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取统计信息失败");
            return new SimplifiedQueueStatistics
            {
                Status = QueueStatus.Error,
                ErrorMessage = ex.Message,
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// 获取标准化队列名称
    /// </summary>
    private string GetQueueName(Guid wxManagerId, string queueType)
    {
        return GenerateQueueName(wxManagerId, queueType);
    }
}