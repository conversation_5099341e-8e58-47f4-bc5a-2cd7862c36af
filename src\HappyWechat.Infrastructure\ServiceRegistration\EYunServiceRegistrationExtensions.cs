using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.EYun;

namespace HappyWechat.Infrastructure.ServiceRegistration;

/// <summary>
/// EYun服务注册扩展 - 统一管理所有EYun相关服务的依赖注入
/// </summary>
public static class EYunServiceRegistrationExtensions
{
    /// <summary>
    /// 注册EYun服务群组
    /// </summary>
    public static IServiceCollection AddEYunServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // 🔧 注释冗余的EYun服务注册开始日志 - 减少日志噪音
        // Console.WriteLine("🔧 开始注册EYun服务群组...");

        // 1. 注册核心基础服务
        services.AddEYunCoreServices(configuration);

        // 2. 注册专门化Wrapper服务
        services.AddEYunWrapperServices();

        // 3. 注册诊断和辅助服务
        services.AddEYunAuxiliaryServices();

        // 🔧 提升为Warning级别 - 重要的服务注册完成状态
        Console.WriteLine("✅ EYun服务群组注册完成");
        return services;
    }
    
    /// <summary>
    /// 注册EYun核心基础服务
    /// </summary>
    private static IServiceCollection AddEYunCoreServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // 注册标准HttpClientFactory - 避免与类型化HttpClient冲突
        services.AddHttpClient();

        // 注册简化的EYun配置提供者 - 直接从IConfiguration读取，避免生命周期冲突
        services.AddSingleton<IEYunConfigurationManager>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var logger = provider.GetRequiredService<ILogger<SimpleEYunConfigurationManager>>();
            return new SimpleEYunConfigurationManager(configuration, logger);
        });

        // HTTP客户端工厂 - Singleton生命周期用于性能优化
        services.AddSingleton<IEYunHttpClientFactory, EYunHttpClientFactory>();

        // 核心Wrapper服务 - Scoped确保请求隔离
        services.AddScoped<IEYunWrapper, EYunWrapper>();

        // 🔧 注释冗余的核心基础服务注册成功日志 - 减少日志噪音
        // Console.WriteLine("✅ EYun核心基础服务注册成功");
        return services;
    }
    
    /// <summary>
    /// 注册EYun专门化Wrapper服务
    /// </summary>
    private static IServiceCollection AddEYunWrapperServices(this IServiceCollection services)
    {
        // 消息相关服务
        services.AddScoped<IEYunMessageWrapper, EYunMessageWrapper>();
        
        // 群组管理服务
        services.AddScoped<IEYunGroupWrapper, EYunGroupWrapper>();
        
        // 好友管理服务
        services.AddScoped<IEYunFriendWrapper, EYunFriendWrapper>();
        
        // 朋友圈管理服务
        services.AddScoped<IEYunMomentsWrapper, EYunMomentsWrapper>();
        
        // 🔧 注释冗余的专门化Wrapper服务注册成功日志 - 减少日志噪音
        // Console.WriteLine("✅ EYun专门化Wrapper服务注册成功");
        return services;
    }
    
    /// <summary>
    /// 注册EYun诊断和辅助服务
    /// </summary>
    private static IServiceCollection AddEYunAuxiliaryServices(this IServiceCollection services)
    {
        // 诊断服务 - Scoped按需使用
        services.AddScoped<IEYunDiagnosticService, EYunDiagnosticService>();
        
        // 🔧 注释冗余的诊断和辅助服务注册成功日志 - 减少日志噪音
        // Console.WriteLine("✅ EYun诊断和辅助服务注册成功");
        return services;
    }
}