using System.Text.Json;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using HappyWechat.Infrastructure.Configuration.Models;

namespace HappyWechat.Infrastructure.Configuration;

/// <summary>
/// 配置变化通知器接口
/// </summary>
public interface IConfigurationChangeNotifier
{
    /// <summary>
    /// 通知配置变化
    /// </summary>
    /// <param name="configType">配置类型</param>
    /// <param name="configKey">配置键</param>
    Task NotifyConfigurationChangedAsync(string configType, string configKey);

    /// <summary>
    /// 通知配置变化（增强版）
    /// </summary>
    /// <param name="configType">配置类型</param>
    /// <param name="configKey">配置键</param>
    /// <param name="affectedWcIds">受影响的微信账号ID列表</param>
    /// <param name="changeDetails">变更详情</param>
    Task NotifyConfigurationChangedAsync(string configType, string configKey, List<string>? affectedWcIds = null, object? changeDetails = null);

    /// <summary>
    /// 批量通知配置变化
    /// </summary>
    /// <param name="changes">配置变更列表</param>
    Task NotifyBatchConfigurationChangedAsync(List<ConfigurationChangeEvent> changes);

    /// <summary>
    /// 订阅配置变化监听器
    /// </summary>
    /// <param name="listener">监听器</param>
    void Subscribe(IConfigurationChangeListener listener);

    /// <summary>
    /// 开始监听配置变化
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StartListeningAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 基于Redis的配置变化通知器
/// </summary>
public class RedisConfigurationChangeNotifier : IConfigurationChangeNotifier
{
    private readonly IDatabase _redis;
    private readonly ISubscriber _subscriber;
    private readonly List<IConfigurationChangeListener> _listeners = new();
    private readonly ILogger<RedisConfigurationChangeNotifier> _logger;
    private readonly object _lockObject = new();

    public RedisConfigurationChangeNotifier(
        IDatabase redis,
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<RedisConfigurationChangeNotifier> logger)
    {
        _redis = redis;
        _subscriber = connectionMultiplexer.GetSubscriber();
        _logger = logger;
    }

    /// <summary>
    /// 通知配置变化
    /// </summary>
    public async Task NotifyConfigurationChangedAsync(string configType, string configKey)
    {
        await NotifyConfigurationChangedAsync(configType, configKey, null, null);
    }

    public async Task NotifyConfigurationChangedAsync(string configType, string configKey, List<string>? affectedWcIds = null, object? changeDetails = null)
    {
        try
        {
            var changeEvent = new ConfigurationChangeEvent
            {
                ConfigType = configType,
                ConfigKey = configKey,
                Timestamp = DateTime.UtcNow,
                ChangeId = Guid.NewGuid().ToString(),
                Source = Environment.MachineName,
                AffectedWcIds = affectedWcIds ?? new List<string>(),
                ChangeDetails = changeDetails
            };

            var message = JsonSerializer.Serialize(changeEvent);
            await _subscriber.PublishAsync("config:changed", message);

            // 🔧 注释冗余的配置变化通知已发送日志 - 减少日志噪音，每次配置更新都会重复出现
            // _logger.LogInformation("🔄 配置变化通知已发送 - Type: {ConfigType}, Key: {ConfigKey}, ChangeId: {ChangeId}, AffectedAccounts: {AffectedCount}",
            //     configType, configKey, changeEvent.ChangeId, affectedWcIds?.Count ?? 0);

            // 同时通知本地监听器
            await NotifyLocalListenersAsync(changeEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发送配置变化通知失败 - Type: {ConfigType}, Key: {ConfigKey}",
                configType, configKey);
        }
    }

    public async Task NotifyBatchConfigurationChangedAsync(List<ConfigurationChangeEvent> changes)
    {
        try
        {
            var batchEvent = new
            {
                BatchId = Guid.NewGuid().ToString(),
                Timestamp = DateTime.UtcNow,
                Changes = changes
            };

            var message = JsonSerializer.Serialize(batchEvent);
            await _subscriber.PublishAsync("config:batch_changed", message);

            _logger.LogInformation("🔄 批量配置变化通知已发送 - BatchId: {BatchId}, ChangeCount: {ChangeCount}",
                batchEvent.BatchId, changes.Count);

            // 同时通知本地监听器
            foreach (var change in changes)
            {
                await NotifyLocalListenersAsync(change);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发送批量配置变化通知失败 - ChangeCount: {ChangeCount}", changes.Count);
        }
    }

    /// <summary>
    /// 订阅配置变化监听器
    /// </summary>
    public void Subscribe(IConfigurationChangeListener listener)
    {
        lock (_lockObject)
        {
            _listeners.Add(listener);
            _logger.LogDebug("配置变化监听器已注册 - Listener: {ListenerType}", listener.GetType().Name);
        }
    }

    /// <summary>
    /// 开始监听配置变化
    /// </summary>
    public async Task StartListeningAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _subscriber.SubscribeAsync("config:changed", async (channel, message) =>
            {
                try
                {
                    var changeEvent = JsonSerializer.Deserialize<ConfigurationChangeEvent>(message);
                    if (changeEvent != null)
                    {
                        _logger.LogDebug("收到配置变化通知 - Type: {ConfigType}, Key: {ConfigKey}, ChangeId: {ChangeId}, Source: {Source}",
                            changeEvent.ConfigType, changeEvent.ConfigKey, changeEvent.ChangeId, changeEvent.Source);

                        // 避免处理自己发送的通知
                        if (changeEvent.Source != Environment.MachineName)
                        {
                            await NotifyLocalListenersAsync(changeEvent);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理配置变化通知异常 - Message: {Message}", message);
                }
            });

            _logger.LogDebug("配置变化监听器已启动");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动配置变化监听器失败");
            throw;
        }
    }

    /// <summary>
    /// 通知本地监听器
    /// </summary>
    private async Task NotifyLocalListenersAsync(ConfigurationChangeEvent changeEvent)
    {
        List<IConfigurationChangeListener> currentListeners;
        lock (_lockObject)
        {
            currentListeners = new List<IConfigurationChangeListener>(_listeners);
        }

        if (currentListeners.Count == 0)
        {
            return;
        }

        var tasks = currentListeners.Select(async listener =>
        {
            try
            {
                var notificationEvent = new ConfigurationChangeNotificationEvent
                {
                    Key = changeEvent.ConfigKey,
                    Reason = "Configuration changed",
                    Timestamp = changeEvent.Timestamp
                };
                await listener.OnConfigurationChangedAsync(notificationEvent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "通知配置变化监听器失败 - Listener: {ListenerType}, ChangeId: {ChangeId}",
                    listener.GetType().Name, changeEvent.ChangeId);
            }
        });

        await Task.WhenAll(tasks);

        _logger.LogDebug("已通知 {Count} 个配置变化监听器 - ChangeId: {ChangeId}",
            currentListeners.Count, changeEvent.ChangeId);
    }
}
