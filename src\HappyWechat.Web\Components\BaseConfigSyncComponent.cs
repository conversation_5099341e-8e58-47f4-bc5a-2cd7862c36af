using Microsoft.AspNetCore.Components;
using HappyWechat.Web.Services.Interfaces;
using HappyWechat.Infrastructure.Notifications;

namespace HappyWechat.Web.Components;

/// <summary>
/// 基础配置同步组件
/// </summary>
public abstract class BaseConfigSyncComponent : ComponentBase
{
    [Inject] protected ILogger<BaseConfigSyncComponent> Logger { get; set; } = default!;
    [Inject] protected HappyWechat.Web.Services.Interfaces.IUnifiedDataManager DataManager { get; set; } = default!;
    [Inject] protected HappyWechat.Web.Services.Interfaces.IUnifiedSignalRNotificationService NotificationService { get; set; } = default!;

    /// <summary>
    /// 是否正在同步
    /// </summary>
    protected bool IsSyncing { get; set; }

    /// <summary>
    /// 同步状态消息
    /// </summary>
    protected string SyncStatusMessage { get; set; } = string.Empty;

    /// <summary>
    /// 最后同步时间
    /// </summary>
    protected DateTime? LastSyncTime { get; set; }

    /// <summary>
    /// 执行同步操作
    /// </summary>
    protected virtual async Task SyncConfigAsync()
    {
        try
        {
            IsSyncing = true;
            SyncStatusMessage = "正在同步配置...";
            StateHasChanged();

            await OnSyncConfigAsync();

            LastSyncTime = DateTime.Now;
            SyncStatusMessage = "同步完成";
            Logger.LogInformation("配置同步完成");
        }
        catch (Exception ex)
        {
            SyncStatusMessage = $"同步失败: {ex.Message}";
            Logger.LogError(ex, "配置同步失败");
        }
        finally
        {
            IsSyncing = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 子类实现具体的同步逻辑
    /// </summary>
    protected abstract Task OnSyncConfigAsync();

    /// <summary>
    /// 获取同步状态CSS类
    /// </summary>
    protected string GetSyncStatusCssClass()
    {
        if (IsSyncing) return "text-warning";
        if (SyncStatusMessage.Contains("失败")) return "text-danger";
        if (SyncStatusMessage.Contains("完成")) return "text-success";
        return "text-muted";
    }

    /// <summary>
    /// 格式化最后同步时间
    /// </summary>
    protected string FormatLastSyncTime()
    {
        if (!LastSyncTime.HasValue) return "从未同步";
        
        var timeSpan = DateTime.Now - LastSyncTime.Value;
        if (timeSpan.TotalMinutes < 1) return "刚刚";
        if (timeSpan.TotalHours < 1) return $"{(int)timeSpan.TotalMinutes}分钟前";
        if (timeSpan.TotalDays < 1) return $"{(int)timeSpan.TotalHours}小时前";
        return LastSyncTime.Value.ToString("yyyy-MM-dd HH:mm");
    }

    /// <summary>
    /// 通知配置变更
    /// </summary>
    protected async Task NotifyConfigurationChangedAsync(string configType, string configKey, string? operation, object configData)
    {
        try
        {
            // 🔧 注释冗余的通知配置变更日志 - 减少日志噪音，每次配置更新都会重复出现
            // Logger.LogInformation("通知配置变更 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}, Operation: {Operation}",
            //     configType, configKey, operation ?? "Update");

            var changeData = new
            {
                ConfigType = configType,
                ConfigKey = configKey,
                Operation = operation ?? "Update",
                NewValue = configData,
                BroadcastToAll = true,
                ChangedAt = DateTime.UtcNow
            };

            // 暂时使用基本通知方法，实际实现需要根据具体需求调整
            // await NotificationService.SendConfigurationChangedAsync(changeData);
            // 🔧 注释冗余的配置变更通知发送成功日志 - 减少日志噪音
            // Logger.LogInformation("配置变更通知发送成功");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "发送配置变更通知失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}",
                configType, configKey);
        }
    }
}
