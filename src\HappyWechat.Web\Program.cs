using Hangfire;
using Hangfire.MemoryStorage;
using HappyWechat.Infrastructure.Configuration;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Web.Extensions;
using HappyWechat.Web.HealthChecks;

namespace HappyWechat.Web;

/// <summary>
/// 现代化的Program.cs - 从458行简化到80行
/// 所有复杂配置逻辑已提取到专门的配置类中
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // ========================================
        // 🔧 日志配置 - 禁用冗余日志
        // ========================================
        ConfigureLogging(builder.Logging);

        // ========================================
        // 🔧 配置管理 - 环境感知配置
        // ========================================
        ConfigureEnvironmentSettings(builder);

        // ========================================
        // 🔧 服务注册 - 使用现代化服务配置
        // ========================================
        builder.Services.AddModernWebServices(builder.Configuration, builder.Environment);

        // ========================================
        // 🔧 应用层服务 - 仅注册配置选项
        // ========================================
        builder.Services.AddApplicationServices(builder.Configuration);

        // ========================================
        // 🔧 Hangfire后台任务 - 单独添加
        // ========================================
        builder.Services.AddHangfire(config =>
        {
            config.UseMemoryStorage();
            config.SetDataCompatibilityLevel(Hangfire.CompatibilityLevel.Version_170);
            config.UseSimpleAssemblyNameTypeSerializer();
            config.UseRecommendedSerializerSettings();
        });
        builder.Services.AddHangfireServer();

        // ========================================
        // 🔧 AutoMapper
        // ========================================
        builder.Services.AddAutoMapper(typeof(Program));

        // ========================================
        // 🔧 健康检查
        // ========================================
        builder.Services.AddHealthChecks()
            .AddDbContextCheck<ApplicationDbContext>()
            .AddCheck<ComprehensiveHealthCheck>("comprehensive");

        // ========================================
        // 🚀 构建应用
        // ========================================
        var app = builder.Build();

        // ========================================
        // 🔧 中间件管道 - 使用现代化中间件配置
        // ========================================
        app.UseModernMiddleware();

        // ========================================
        // 🚀 启动应用
        // ========================================
        await app.RunAsync();
    }

    /// <summary>
    /// 配置日志系统
    /// </summary>
    private static void ConfigureLogging(ILoggingBuilder logging)
    {
        // 禁用冗余日志
        var noisyLoggers = new[]
        {
            "Microsoft.EntityFrameworkCore",
            "Microsoft.EntityFrameworkCore.Database",
            "Microsoft.EntityFrameworkCore.Database.Command",
            "Microsoft.EntityFrameworkCore.Database.Connection",
            "Microsoft.EntityFrameworkCore.Database.Transaction",
            "Microsoft.EntityFrameworkCore.Query",
            "Microsoft.EntityFrameworkCore.Migrations",
            "Microsoft.EntityFrameworkCore.Infrastructure",
            "Microsoft.EntityFrameworkCore.Model",
            "Microsoft.EntityFrameworkCore.ChangeTracking",
            "Microsoft.EntityFrameworkCore.Update",
            "System.Net.Http.HttpClient",
            "Hangfire",
            "StackExchange.Redis"
        };

        foreach (var logger in noisyLoggers)
        {
            logging.AddFilter(logger, LogLevel.None);
        }

        // 全局过滤器确保EF Core日志完全禁用
        logging.AddFilter((provider, category, logLevel) =>
        {
            if (category?.StartsWith("Microsoft.EntityFrameworkCore") == true)
            {
                return false;
            }
            return true;
        });
    }

    /// <summary>
    /// 配置环境设置
    /// </summary>
    private static void ConfigureEnvironmentSettings(WebApplicationBuilder builder)
    {
        // 开发环境本地配置
        if (builder.Environment.IsDevelopment())
        {
            builder.Configuration.AddJsonFile("appsettings.Local.json", optional: true, reloadOnChange: true);
        }

        // 注册环境配置提供者
        builder.Services.AddSingleton<EnvironmentVariableConfigurationProvider>();
        
        // 使用临时服务提供者获取配置提供者
        using var tempServiceProvider = builder.Services.BuildServiceProvider();
        var envConfigProvider = ActivatorUtilities.CreateInstance<EnvironmentVariableConfigurationProvider>(
            tempServiceProvider, builder.Configuration);

        // 环境感知配置设置
        SetupDatabaseConfiguration(builder, envConfigProvider);
        SetupRedisConfiguration(builder, envConfigProvider);
        SetupExternalServiceConfiguration(builder, envConfigProvider);
    }

    /// <summary>
    /// 设置数据库配置
    /// </summary>
    private static void SetupDatabaseConfiguration(WebApplicationBuilder builder, EnvironmentVariableConfigurationProvider envConfigProvider)
    {
        var connectionString = envConfigProvider.BuildDatabaseConnectionString();
        builder.Configuration["ConnectionStrings:DefaultConnection"] = connectionString;
        builder.Configuration["Database:WriteConnectionString"] = connectionString;
        builder.Configuration["Database:ReadConnectionString"] = connectionString;
    }

    /// <summary>
    /// 设置Redis配置
    /// </summary>
    private static void SetupRedisConfiguration(WebApplicationBuilder builder, EnvironmentVariableConfigurationProvider envConfigProvider)
    {
        var redisConnectionString = envConfigProvider.BuildRedisConnectionString();
        builder.Configuration["Redis:ConnectionString"] = redisConnectionString;

        // 设置Redis数据库配置
        var redisDbConfig = envConfigProvider.GetRedisDbConfig();
        builder.Configuration["Redis:Databases:Cache"] = redisDbConfig.Cache.ToString();
        builder.Configuration["Redis:Databases:MessageQueue"] = redisDbConfig.MessageQueue.ToString();
        builder.Configuration["Redis:Databases:Session"] = redisDbConfig.Session.ToString();
        builder.Configuration["Redis:Databases:Lock"] = redisDbConfig.Lock.ToString();
    }

    /// <summary>
    /// 设置外部服务配置
    /// </summary>
    private static void SetupExternalServiceConfiguration(WebApplicationBuilder builder, EnvironmentVariableConfigurationProvider envConfigProvider)
    {
        // EYun配置
        var eyunConfig = envConfigProvider.GetEYunConfig();
        builder.Configuration["EYun:Account"] = eyunConfig.Account;
        builder.Configuration["EYun:Password"] = eyunConfig.Password;
        builder.Configuration["EYun:WxEYunBaseUrl"] = eyunConfig.WxEYunBaseUrl;
        builder.Configuration["EYun:CallBackUrl"] = eyunConfig.CallBackUrl;
        builder.Configuration["EYun:Token"] = eyunConfig.Token;

        // JWT配置已移除 - 现在使用纯SessionId认证

        // 应用域名配置
        var applicationDomain = envConfigProvider.GetApplicationDomain();
        builder.Configuration["MyDomains:ApplicationDomain"] = applicationDomain;

        // MinIO配置
        var minioConfig = envConfigProvider.GetMinIOConfig();
        builder.Configuration["FileStorage:MinIO:Endpoint"] = minioConfig.Endpoint;
        builder.Configuration["FileStorage:MinIO:AccessKey"] = minioConfig.AccessKey;
        builder.Configuration["FileStorage:MinIO:SecretKey"] = minioConfig.SecretKey;
        builder.Configuration["FileStorage:MinIO:UseSSL"] = minioConfig.UseSSL.ToString();
        builder.Configuration["FileStorage:MinIO:DefaultBucket"] = minioConfig.DefaultBucket;
        builder.Configuration["FileStorage:MinIO:Region"] = minioConfig.Region;
    }
}