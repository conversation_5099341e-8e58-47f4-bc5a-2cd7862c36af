using System.ComponentModel.DataAnnotations;

namespace HappyWechat.Application.DTOs.Requests.Commands;

public class BatchSetContactAiAgentCommand
{
    [Required]
    public List<Guid> ContactIds { get; set; } = new();
    
    [Required]
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// AI机器人名称，空字符串表示清除AI配置
    /// </summary>
    public string AiAgentName { get; set; } = "";
    
    /// <summary>
    /// 是否启用AI自动回复
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// 自定义提示词
    /// </summary>
    public string? CustomPrompt { get; set; }
    
    /// <summary>
    /// 回复延迟时间（秒）
    /// </summary>
    public int ReplyDelaySeconds { get; set; } = 1;
    
    /// <summary>
    /// 是否异步处理（批量操作推荐使用异步）
    /// </summary>
    public bool IsAsync { get; set; } = true;
}