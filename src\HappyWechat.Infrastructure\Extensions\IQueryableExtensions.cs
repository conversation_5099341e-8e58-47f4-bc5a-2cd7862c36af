﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Query;

namespace HappyWechat.Infrastructure.Extensions;

public static class IQueryableExtensions
{
    /// <summary>
    /// 应用分页逻辑 - 使用1基索引的页码（已排序的查询）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="orderedQuery">已排序的查询对象</param>
    /// <param name="page">页码（从1开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页后的查询对象</returns>
    public static IQueryable<T> ApplyPagination<T>(this IOrderedQueryable<T> orderedQuery, int page, int pageSize)
    {
        // 验证参数
        if (page < 1) page = 1;
        if (pageSize < 1) pageSize = 10;

        // 计算跳过的记录数：(页码 - 1) * 页大小
        var skip = (page - 1) * pageSize;

        return orderedQuery.Skip(skip).Take(pageSize);
    }

    /// <summary>
    /// 应用分页逻辑 - 使用1基索引的页码（带默认排序）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TKey">排序键类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="page">页码（从1开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="orderBy">排序表达式</param>
    /// <param name="ascending">是否升序排列，默认true</param>
    /// <returns>分页后的查询对象</returns>
    public static IQueryable<T> ApplyPagination<T, TKey>(this IQueryable<T> query, int page, int pageSize,
        Expression<Func<T, TKey>> orderBy, bool ascending = true)
    {
        // 验证参数
        if (page < 1) page = 1;
        if (pageSize < 1) pageSize = 10;

        // 计算跳过的记录数：(页码 - 1) * 页大小
        var skip = (page - 1) * pageSize;

        // 🔧 修复：先排序再分页，解决EF Core警告
        var orderedQuery = ascending ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
        return orderedQuery.Skip(skip).Take(pageSize);
    }

    /// <summary>
    /// 应用分页逻辑 - 兼容性方法（自动添加默认排序）
    /// 注意：此方法仅用于向后兼容，建议使用带排序的重载
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="page">页码（从1开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页后的查询对象</returns>
    [Obsolete("建议使用带排序的ApplyPagination重载以确保查询结果一致性")]
    public static IQueryable<T> ApplyPagination<T>(this IQueryable<T> query, int page, int pageSize)
    {
        // 验证参数
        if (page < 1) page = 1;
        if (pageSize < 1) pageSize = 10;

        // 计算跳过的记录数：(页码 - 1) * 页大小
        var skip = (page - 1) * pageSize;

        // 🔧 修复：尝试添加默认排序以避免EF Core警告
        // 如果查询已经是有序的，直接使用；否则尝试按第一个属性排序
        if (query is IOrderedQueryable<T>)
        {
            return query.Skip(skip).Take(pageSize);
        }

        // 为了向后兼容，添加一个基本排序（如果实体有Id属性）
        var entityType = typeof(T);
        var idProperty = entityType.GetProperty("Id");
        if (idProperty != null)
        {
            var parameter = Expression.Parameter(entityType, "x");
            var property = Expression.Property(parameter, idProperty);
            var lambda = Expression.Lambda(property, parameter);

            var orderByMethod = typeof(Queryable).GetMethods()
                .First(m => m.Name == "OrderBy" && m.GetParameters().Length == 2);
            var genericOrderBy = orderByMethod.MakeGenericMethod(entityType, idProperty.PropertyType);

            var orderedQuery = (IQueryable<T>)genericOrderBy.Invoke(null, new object[] { query, lambda });
            return orderedQuery.Skip(skip).Take(pageSize);
        }

        // 如果没有Id属性，使用原始方法（会产生警告，但保持兼容性）
        return query.Skip(skip).Take(pageSize);
    }

    /// <summary>
    /// 应用分页逻辑 - 支持IIncludableQueryable（通用IQueryable处理）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TProperty">Include属性类型</typeparam>
    /// <param name="includableQuery">包含Include的查询对象</param>
    /// <param name="page">页码（从1开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页后的查询对象</returns>
    public static IQueryable<T> ApplyPagination<T, TProperty>(this IIncludableQueryable<T, TProperty> includableQuery, int page, int pageSize)
        where T : class
    {
        // 验证参数
        if (page < 1) page = 1;
        if (pageSize < 1) pageSize = 10;

        // 计算跳过的记录数：(页码 - 1) * 页大小
        var skip = (page - 1) * pageSize;

        // 🔧 修复：将IIncludableQueryable转换为IQueryable进行分页
        return ((IQueryable<T>)includableQuery).Skip(skip).Take(pageSize);
    }
}