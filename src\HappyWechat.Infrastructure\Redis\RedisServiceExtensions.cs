using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using StackExchange.Redis.Extensions.Core.Abstractions;
using StackExchange.Redis.Extensions.Core.Configuration;
using StackExchange.Redis.Extensions.Core.Implementations;
using StackExchange.Redis.Extensions.Newtonsoft;
using HappyWechat.Application.Options;
using HappyWechat.Infrastructure.Caching.Interfaces;
using HappyWechat.Infrastructure.Caching;
using HappyWechat.Infrastructure.Logging;

namespace HappyWechat.Infrastructure.Redis;

/// <summary>
/// Redis服务注册扩展方法
/// </summary>
public static class RedisServiceExtensions
{
    /// <summary>
    /// 添加Redis服务
    /// </summary>
    public static IServiceCollection AddRedisServices(this IServiceCollection services, IConfiguration configuration)
    {
        try
        {
            // 获取Redis配置
            var redisOptions = configuration.GetSection(RedisOptions.SectionName).Get<RedisOptions>() ?? new RedisOptions();

            // 获取混合缓存配置
            var hybridCacheOptions = configuration.GetSection(HappyWechat.Application.Options.HybridCacheOptions.SectionName).Get<HappyWechat.Application.Options.HybridCacheOptions>() ?? new HappyWechat.Application.Options.HybridCacheOptions();

            // 注册配置选项
            services.Configure<RedisOptions>(configuration.GetSection(RedisOptions.SectionName));
            services.Configure<HappyWechat.Application.Options.HybridCacheOptions>(configuration.GetSection(HappyWechat.Application.Options.HybridCacheOptions.SectionName));

            // 尝试创建Redis连接
            IConnectionMultiplexer? redis = null;
            try
            {
                var connectionString = redisOptions.ConnectionString;
                
                // 增强Redis连接配置，修复超时问题
                var configOptions = ConfigurationOptions.Parse(connectionString);
                configOptions.AbortOnConnectFail = false;
                configOptions.ConnectTimeout = 10000; // 10秒连接超时
                configOptions.SyncTimeout = 10000;    // 10秒同步操作超时
                configOptions.AsyncTimeout = 10000;   // 10秒异步操作超时
                configOptions.CommandMap = CommandMap.Default;
                configOptions.ReconnectRetryPolicy = new ExponentialRetry(5000); // 指数退避重试
                configOptions.ConnectRetry = 3;       // 连接重试3次
                configOptions.KeepAlive = 60;         // 60秒心跳保活
                configOptions.DefaultDatabase = redisOptions.Database;

                redis = ConnectionMultiplexer.Connect(configOptions);
                
                // 在ConnectionMultiplexer上添加连接事件日志
                redis.ConnectionFailed += (sender, args) =>
                {
                    Console.WriteLine($"Redis连接失败: {args.EndPoint} - {args.Exception?.Message}");
                };
                redis.ConnectionRestored += (sender, args) =>
                {
                    Console.WriteLine($"Redis连接恢复: {args.EndPoint}");
                };
                redis.ErrorMessage += (sender, args) =>
                {
                    Console.WriteLine($"Redis错误: {args.EndPoint} - {args.Message}");
                };

                // 注册Redis连接为单例
                services.AddSingleton<IConnectionMultiplexer>(redis);

                // 注册多个Redis数据库实例
                services.AddSingleton<IDatabase>(provider =>
                {
                    var multiplexer = provider.GetRequiredService<IConnectionMultiplexer>();
                    return multiplexer.GetDatabase(redisOptions.Database);
                });

                // 注册缓存专用数据库
                services.AddKeyedSingleton<IDatabase>("Cache", (provider, key) =>
                {
                    var multiplexer = provider.GetRequiredService<IConnectionMultiplexer>();
                    var databases = configuration.GetSection("Redis:Databases").Get<Dictionary<string, int>>() ?? new();
                    var cacheDb = databases.ContainsKey("Cache") ? databases["Cache"] : 2;
                    return multiplexer.GetDatabase(cacheDb);
                });

                // 注册消息队列专用数据库
                services.AddKeyedSingleton<IDatabase>("MessageQueue", (provider, key) =>
                {
                    var multiplexer = provider.GetRequiredService<IConnectionMultiplexer>();
                    var databases = configuration.GetSection("Redis:Databases").Get<Dictionary<string, int>>() ?? new();
                    var queueDb = databases.ContainsKey("MessageQueue") ? databases["MessageQueue"] : 3;
                    return multiplexer.GetDatabase(queueDb);
                });

                // 注册Redis Extensions，使用连接字符串配置
                services.AddStackExchangeRedisExtensions<NewtonsoftSerializer>(new RedisConfiguration
                {
                    Name = "HappyWechatRedis", // 🔧 修复：添加Redis配置名称，解决警告
                    ConnectionString = configOptions.ToString(),
                    Database = redisOptions.Database,
                    KeyPrefix = redisOptions.KeyPrefix ?? ""
                });

                // 脱敏输出Redis连接信息
                var maskedConnectionString = SensitiveDataMasker.MaskRedisConnectionString(redisOptions.ConnectionString);
                // 🔧 注释冗余的Redis连接成功日志 - 降低日志噪音
                // Console.WriteLine($"Redis连接成功: {maskedConnectionString}");
            }
            catch (Exception redisEx)
            {
                var maskedConnectionString = SensitiveDataMasker.MaskRedisConnectionString(redisOptions.ConnectionString);
                Console.WriteLine($"Redis连接失败，将使用纯内存缓存 - 目标: {maskedConnectionString}, 错误: {redisEx.Message}");

                // Redis不可用时注册null，统一缓存服务会自动降级
                services.AddSingleton<IConnectionMultiplexer>(provider => null!);
                services.AddSingleton<IDatabase>(provider => null!);
                services.AddSingleton<IRedisClient>(provider => null!);
                services.AddSingleton<IRedisDatabase>(provider => null!);
            }

            // 注册统一Redis缓存服务
            services.AddSingleton<IUnifiedRedisCacheService, UnifiedRedisCacheService>();

            // Console.WriteLine("统一缓存服务注册成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Redis服务注册失败: {ex.Message}");

            // 确保基本服务可用
            services.Configure<RedisOptions>(configuration.GetSection(RedisOptions.SectionName));
            services.Configure<HappyWechat.Application.Options.HybridCacheOptions>(configuration.GetSection(HappyWechat.Application.Options.HybridCacheOptions.SectionName));
            services.AddSingleton<IConnectionMultiplexer>(provider => null!);
            services.AddSingleton<IDatabase>(provider => null!);
            services.AddSingleton<IRedisClient>(provider => null!);
            services.AddSingleton<IRedisDatabase>(provider => null!);
            services.AddSingleton<IUnifiedRedisCacheService, UnifiedRedisCacheService>();
        }

        return services;
    }


}
