using HappyWechat.Application.Commons;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Queries;

/// <summary>
/// 素材查询参数
/// </summary>
public class MaterialQuery
{
    /// <summary>
    /// 分页查询参数
    /// </summary>
    public PageQuery PageQuery { get; set; } = new();
    
    /// <summary>
    /// 素材类型过滤
    /// </summary>
    public MaterialType? Type { get; set; }
    
    /// <summary>
    /// 分类ID过滤
    /// </summary>
    public Guid? CategoryId { get; set; }
    
    /// <summary>
    /// 关键词搜索（搜索名称、描述、内容）
    /// </summary>
    public string? Keyword { get; set; }
    
    /// <summary>
    /// 标签过滤
    /// </summary>
    public List<string> Tags { get; set; } = new();
    
    /// <summary>
    /// 创建时间范围 - 开始时间
    /// </summary>
    public DateTime? CreatedFrom { get; set; }
    
    /// <summary>
    /// 创建时间范围 - 结束时间
    /// </summary>
    public DateTime? CreatedTo { get; set; }
    
    /// <summary>
    /// 排序字段
    /// </summary>
    public MaterialSortField SortField { get; set; } = MaterialSortField.CreatedAt;
    
    /// <summary>
    /// 排序方向
    /// </summary>
    public SortDirection SortDirection { get; set; } = SortDirection.Descending;
}

/// <summary>
/// 素材排序字段
/// </summary>
public enum MaterialSortField
{
    /// <summary>
    /// 创建时间
    /// </summary>
    CreatedAt,
    
    /// <summary>
    /// 更新时间
    /// </summary>
    UpdatedAt,
    
    /// <summary>
    /// 名称
    /// </summary>
    Name,
    
    /// <summary>
    /// 使用次数
    /// </summary>
    UseCount,
    
    /// <summary>
    /// 最后使用时间
    /// </summary>
    LastUsedAt,
    
    /// <summary>
    /// 文件大小
    /// </summary>
    FileSize
}

/// <summary>
/// 排序方向
/// </summary>
public enum SortDirection
{
    /// <summary>
    /// 升序
    /// </summary>
    Ascending,
    
    /// <summary>
    /// 降序
    /// </summary>
    Descending
}
