using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// SignalR Redis会话认证处理器
/// </summary>
public class SignalRRedisAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    private readonly IRedisAuthenticationService _authService;
    private readonly ILogger<SignalRRedisAuthenticationHandler> _logger;

    public SignalRRedisAuthenticationHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        IRedisAuthenticationService authService)
        : base(options, logger, encoder)
    {
        _authService = authService;
        _logger = logger.CreateLogger<SignalRRedisAuthenticationHandler>();
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            // 只处理SignalR Hub路径
            if (!Request.Path.StartsWithSegments("/hubs"))
            {
                return AuthenticateResult.NoResult();
            }

            // 统一使用RedisAuthenticationService获取SessionId
            // 这会按优先级从 access_token 查询参数、Authorization头、Cookie 中获取
            var sessionId = _authService.GetSessionIdFromContext(Context);
            
            // 特殊处理：SignalR连接建立时，SessionId通常通过access_token查询参数传递
            if (string.IsNullOrEmpty(sessionId))
            {
                sessionId = Request.Query["access_token"].FirstOrDefault();
                _logger.LogDebug("🔍 从access_token查询参数获取SessionId - SessionId: {SessionId}", 
                    string.IsNullOrEmpty(sessionId) ? "null" : sessionId.Substring(0, 8) + "...");
            }

            if (string.IsNullOrEmpty(sessionId))
            {
                _logger.LogDebug("🔍 SignalR认证失败：未找到会话ID - Path: {Path}", Request.Path);
                return AuthenticateResult.Fail("未找到会话ID");
            }

            // 验证会话并获取认证状态
            var authState = await _authService.GetAuthenticationStateAsync(sessionId);
            if (authState?.User?.Identity?.IsAuthenticated != true)
            {
                _logger.LogDebug("🔍 SignalR认证失败：会话无效 - SessionId: {SessionId}", sessionId.Substring(0, 8) + "...");
                return AuthenticateResult.Fail("会话无效");
            }

            // 从认证状态中获取Claims
            var claims = authState.User.Claims.ToList();

            // 确保有会话ID claim
            if (!claims.Any(c => c.Type == "session_id"))
            {
                claims.Add(new Claim("session_id", sessionId));
            }

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            var userId = claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? "Unknown";
            _logger.LogDebug("✅ SignalR认证成功 - UserId: {UserId}, SessionId: {SessionId}",
                userId, sessionId.Substring(0, 8) + "...");

            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ SignalR认证处理异常 - Path: {Path}", Request.Path);
            return AuthenticateResult.Fail($"认证处理异常: {ex.Message}");
        }
    }
}

/// <summary>
/// SignalR Redis认证方案名称
/// </summary>
public static class SignalRRedisAuthenticationDefaults
{
    public const string AuthenticationScheme = "SignalRRedis";
}
