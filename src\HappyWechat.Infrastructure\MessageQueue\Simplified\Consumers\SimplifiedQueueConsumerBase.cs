using System.Text.Json;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers;

/// <summary>
/// 简化队列消费者基类 - 替代旧架构的所有消费者
/// 统一处理所有消息类型的消费逻辑
/// </summary>
/// <typeparam name="T">消息数据类型</typeparam>
public abstract class SimplifiedQueueConsumerBase<T> : BackgroundService where T : class
{
    protected readonly IServiceProvider _serviceProvider;
    protected readonly ILogger _logger;
    protected readonly string _queueType;
    protected readonly int _batchSize;
    protected readonly int _maxConcurrency;
    protected readonly int _delayMs;

    private readonly SemaphoreSlim _semaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    protected SimplifiedQueueConsumerBase(
        IServiceProvider serviceProvider,
        ILogger logger,
        string queueType,
        int batchSize = 10,
        int maxConcurrency = 5,
        int delayMs = 1000)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _queueType = queueType;
        _batchSize = batchSize;
        _maxConcurrency = maxConcurrency;
        _delayMs = delayMs;
        _semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        _cancellationTokenSource = new CancellationTokenSource();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // 🔧 注释冗余的消费者启动日志 - 减少日志噪音
        // _logger.LogInformation("🚀 简化队列消费者已启动 - QueueType: {QueueType}", _queueType);

        try
        {
            // 等待一小段时间确保所有服务都已初始化
            await Task.Delay(2000, stoppingToken);
            // 🔧 注释冗余的消费者开始处理日志 - 减少日志噪音
            // _logger.LogInformation("✅ 简化队列消费者开始处理 - QueueType: {QueueType}", _queueType);

            // 检查Redis连接状态
            await CheckRedisConnectionAsync();

            while (!stoppingToken.IsCancellationRequested)
            {
                await ProcessAllQueuesAsync(stoppingToken);
                await Task.Delay(_delayMs, stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("🛑 简化队列消费者已停止 - QueueType: {QueueType}", _queueType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 简化队列消费者异常 - QueueType: {QueueType}", _queueType);

            // 记录详细的异常信息
            _logger.LogError("异常详情: {ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
            if (ex.InnerException != null)
            {
                _logger.LogError("内部异常: {InnerExceptionType} - {InnerMessage}",
                    ex.InnerException.GetType().Name, ex.InnerException.Message);
            }

            // 重新抛出异常以便系统知道服务失败
            throw;
        }
    }

    /// <summary>
    /// 处理所有微信管理器的队列
    /// </summary>
    private async Task ProcessAllQueuesAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();

            // 尝试获取队列服务
            var queueService = scope.ServiceProvider.GetRequiredService<ISimplifiedQueueService>();
            _logger.LogDebug("✅ 成功获取队列服务 - QueueType: {QueueType}", _queueType);

            // 获取所有队列名称
            var allQueueNames = await queueService.GetAllQueueNamesAsync(cancellationToken);
            _logger.LogDebug("📋 获取到队列名称数量: {Count} - QueueType: {QueueType}", allQueueNames.Count, _queueType);

            var relevantQueues = allQueueNames
                .Where(name => name.Contains($":queue:") && name.EndsWith($":{_queueType}"))
                .ToList();

            _logger.LogDebug("🎯 相关队列数量: {Count} - QueueType: {QueueType}", relevantQueues.Count, _queueType);

            if (allQueueNames.Count > 0)
            {
                _logger.LogDebug("🔍 所有队列名称: {QueueNames} - QueueType: {QueueType}",
                    string.Join(", ", allQueueNames), _queueType);
            }

            if (relevantQueues.Count > 0)
            {
                _logger.LogInformation("✅ 匹配的队列: {RelevantQueues} - QueueType: {QueueType}",
                    string.Join(", ", relevantQueues), _queueType);
            }

            // 如果通过Keys扫描没有找到队列，尝试直接检查已知的队列
            if (relevantQueues.Count == 0)
            {
                await CheckKnownQueuesAsync(queueService, cancellationToken);
            }

            if (relevantQueues.Count == 0)
            {
                return;
            }

            // 并发处理所有相关队列
            var tasks = relevantQueues.Select(queueName => ProcessQueueAsync(queueName, cancellationToken));
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理队列失败 - QueueType: {QueueType}", _queueType);
            _logger.LogError("异常详情: {ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
            if (ex.InnerException != null)
            {
                _logger.LogError("内部异常: {InnerExceptionType} - {InnerMessage}",
                    ex.InnerException.GetType().Name, ex.InnerException.Message);
            }
        }
    }

    /// <summary>
    /// 检查已知的队列（当Keys扫描失败时使用）
    /// </summary>
    private async Task CheckKnownQueuesAsync(ISimplifiedQueueService queueService, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("🔍 开始检查已知队列 - QueueType: {QueueType}", _queueType);

            // 这里可以添加已知的WxManagerId列表，或者从数据库获取
            // 暂时使用一个示例ID进行测试
            var testWxManagerId = Guid.Parse("980a47bc-1452-446c-8e78-72f7bb0c5144");
            var testQueueName = queueService.GenerateQueueName(testWxManagerId, _queueType);

            _logger.LogDebug("🔍 检查测试队列 - QueueName: {QueueName}", testQueueName);

            var exists = await queueService.QueueExistsAsync(testQueueName);
            if (exists)
            {
                _logger.LogInformation("✅ 找到活跃队列 - QueueName: {QueueName}", testQueueName);
                await ProcessQueueAsync(testQueueName, cancellationToken);
            }
            else
            {
                _logger.LogDebug("❌ 测试队列不存在或为空 - QueueName: {QueueName}", testQueueName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查已知队列失败 - QueueType: {QueueType}", _queueType);
        }
    }

    /// <summary>
    /// 检查Redis连接状态
    /// </summary>
    private async Task CheckRedisConnectionAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var queueService = scope.ServiceProvider.GetRequiredService<ISimplifiedQueueService>();

            // 尝试获取所有队列名称来测试Redis连接
            var queueNames = await queueService.GetAllQueueNamesAsync(CancellationToken.None);
            // 🔧 注释冗余的Redis连接检查日志 - 减少日志噪音
            // _logger.LogInformation("🔗 Redis连接检查成功 - QueueType: {QueueType}, 发现队列数量: {Count}",
            //     _queueType, queueNames.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Redis连接检查失败 - QueueType: {QueueType}", _queueType);
            _logger.LogError("Redis连接异常详情: {ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
            throw; // 重新抛出异常，阻止消费者启动
        }
    }

    /// <summary>
    /// 处理单个队列
    /// </summary>
    private async Task ProcessQueueAsync(string queueName, CancellationToken cancellationToken)
    {
        // 从队列名称中提取 WxManagerId
        var parts = queueName.Split(':');
        if (parts.Length < 4 || !Guid.TryParse(parts[2], out var wxManagerId))
        {
            return;
        }

        await ProcessWxManagerQueueAsync(wxManagerId, cancellationToken);
    }

    /// <summary>
    /// 处理指定微信管理器的队列消息
    /// </summary>
    private async Task ProcessWxManagerQueueAsync(Guid wxManagerId, CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var queueService = scope.ServiceProvider.GetRequiredService<ISimplifiedQueueService>();

            // 批量获取消息
            var messages = await queueService.DequeueBatchAsync<T>(wxManagerId, _queueType, _batchSize, cancellationToken);
            
            if (messages.Count == 0)
            {
                return;
            }

            // 🔧 注释冗余的获取消息日志 - 减少日志噪音
            // _logger.LogInformation("📦 获取到 {Count} 条消息 - WxManagerId: {WxManagerId}, QueueType: {QueueType}",
            //     messages.Count, wxManagerId, _queueType);

            // 并发处理消息
            var tasks = messages.Select(message => ProcessMessageWithSemaphoreAsync(message, queueService, cancellationToken));
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理微信管理器队列失败 - WxManagerId: {WxManagerId}, QueueType: {QueueType}", 
                wxManagerId, _queueType);
        }
    }

    /// <summary>
    /// 使用信号量控制并发处理消息
    /// </summary>
    private async Task ProcessMessageWithSemaphoreAsync(SimplifiedQueueMessage<T> message, ISimplifiedQueueService queueService, CancellationToken cancellationToken)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            await ProcessSingleMessageAsync(message, queueService, cancellationToken);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 处理单条消息
    /// </summary>
    private async Task ProcessSingleMessageAsync(SimplifiedQueueMessage<T> message, ISimplifiedQueueService queueService, CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogDebug("🔄 开始处理消息 - MessageId: {MessageId}, WxManagerId: {WxManagerId}", 
                message.Id, message.WxManagerId);

            // 调用子类实现的具体处理逻辑
            var success = await ProcessMessageAsync(message, cancellationToken);

            var processingTime = DateTime.UtcNow - startTime;

            if (success)
            {
                await queueService.AckAsync(message.WxManagerId, message.QueueType, message.Id, cancellationToken);
                
                _logger.LogDebug("✅ 消息处理成功 - MessageId: {MessageId}, ProcessingTime: {ProcessingTime}ms", 
                    message.Id, processingTime.TotalMilliseconds);
            }
            else
            {
                await HandleMessageFailureAsync(message, queueService, "处理失败", cancellationToken);
            }
        }
        catch (Exception ex)
        {
            var processingTime = DateTime.UtcNow - startTime;
            
            _logger.LogError(ex, "❌ 消息处理异常 - MessageId: {MessageId}, ProcessingTime: {ProcessingTime}ms", 
                message.Id, processingTime.TotalMilliseconds);

            await HandleMessageFailureAsync(message, queueService, ex.Message, cancellationToken);
        }
    }

    /// <summary>
    /// 处理消息失败的情况
    /// </summary>
    private async Task HandleMessageFailureAsync(SimplifiedQueueMessage<T> message, ISimplifiedQueueService queueService, string reason, CancellationToken cancellationToken)
    {
        try
        {
            if (message.CanRetry)
            {
                // 指数退避重试延时
                var retryDelay = (int)(Math.Pow(2, message.RetryCount) * 1000); // 1s, 2s, 4s, 8s...
                retryDelay = Math.Min(retryDelay, 300000); // 最大5分钟
                
                await queueService.RequeueAsync(message.WxManagerId, message.QueueType, message, retryDelay, cancellationToken);
                
                _logger.LogWarning("⚠️ 消息将重试 - MessageId: {MessageId}, RetryCount: {RetryCount}, DelayMs: {DelayMs}", 
                    message.Id, message.RetryCount, retryDelay);
            }
            else
            {
                await queueService.NackAsync(message.WxManagerId, message.QueueType, message.Id, reason, cancellationToken);
                
                _logger.LogError("💀 消息已进入死信队列 - MessageId: {MessageId}, Reason: {Reason}", 
                    message.Id, reason);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理消息失败后的异常 - MessageId: {MessageId}", message.Id);
        }
    }

    /// <summary>
    /// 子类需要实现的消息处理逻辑
    /// </summary>
    /// <param name="message">要处理的消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理是否成功</returns>
    protected abstract Task<bool> ProcessMessageAsync(SimplifiedQueueMessage<T> message, CancellationToken cancellationToken);

    public override void Dispose()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        _semaphore?.Dispose();
        base.Dispose();
    }
}