using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using System.Diagnostics;
using System.Collections.Concurrent;
using HappyWechat.Web.Services.Interfaces;
using HappyWechat.Web.Services.Models;
using HappyWechat.Infrastructure.Caching;
using HappyWechat.Infrastructure.Caching.Interfaces;

namespace HappyWechat.Web.Services;

/// <summary>
/// 统一前端缓存服务实现
/// 提供统一的前端缓存清理和数据刷新机制
/// </summary>
public class UnifiedFrontendCacheService : IUnifiedFrontendCacheService
{
    private readonly IUnifiedDataManager _dataManager;
    private readonly IUnifiedCacheManager _unifiedCacheManager;
    private readonly IMemoryCache _memoryCache;
    private readonly ISignalRConnectionService _signalRService;
    // 注释掉不存在的服务依赖
    // private readonly HappyWechat.Infrastructure.Caching.Interfaces.ICacheInvalidationNotificationService _cacheNotificationService;
    private readonly ILogger<UnifiedFrontendCacheService> _logger;
    private readonly IJSRuntime _jsRuntime;

    private readonly List<Func<FrontendCacheContext, Task>> _refreshCallbacks = new();
    private readonly SemaphoreSlim _operationLock = new(1, 1);
    private readonly Dictionary<string, DateTime> _lastOperationTimes = new();
    private readonly TimeSpan _minOperationInterval = TimeSpan.FromMilliseconds(500);

    // 组件刷新回调管理
    private readonly ConcurrentDictionary<string, ComponentRefreshCallback> _componentRefreshCallbacks = new();

    // 🔥 新增：操作去重管理
    private readonly ConcurrentDictionary<string, DateTime> _recentOperations = new();
    private readonly TimeSpan _operationDeduplicationWindow = TimeSpan.FromSeconds(3);

    public UnifiedFrontendCacheService(
        IUnifiedCacheManager unifiedCacheManager,
        IMemoryCache memoryCache,
        ILogger<UnifiedFrontendCacheService> logger,
        IJSRuntime jsRuntime,
        IUnifiedDataManager? dataManager = null,
        ISignalRConnectionService? signalRService = null)
    {
        _dataManager = dataManager!;
        _unifiedCacheManager = unifiedCacheManager;
        _memoryCache = memoryCache;
        _signalRService = signalRService!;
        _logger = logger;
        _jsRuntime = jsRuntime;

        // 启动定时清理任务
        _ = Task.Run(StartPeriodicCleanup);
    }

    /// <summary>
    /// 执行统一的前端缓存清理
    /// </summary>
    public async Task<FrontendCacheOperationResult> ClearCacheAsync(FrontendCacheContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new FrontendCacheOperationResult
        {
            OperationId = context.OperationId
        };

        await _operationLock.WaitAsync();
        try
        {
            // 🔧 注释冗余的统一前端缓存清理开始日志 - 降低日志噪音
            // _logger.LogInformation("🧹 开始统一前端缓存清理 - OperationId: {OperationId}, Type: {OperationType}, Strategy: {Strategy}",
            //     context.OperationId, context.OperationType, context.Strategy);

            // 防止频繁操作
            if (ShouldSkipOperation(context))
            {
                _logger.LogDebug("⏭️ 跳过频繁的缓存清理操作 - OperationId: {OperationId}", context.OperationId);
                return FrontendCacheOperationResult.CreateSuccess(context.OperationId);
            }

            // 根据策略选择清理方法
            var strategy = context.Strategy == FrontendCacheClearStrategy.Smart 
                ? GetSmartStrategy(context.OperationType) 
                : context.Strategy;

            // 执行分层缓存清理
            await ExecuteLayeredCacheClear(context, strategy, result);

            // 发送SignalR通知
            if (context.SendSignalRNotification)
            {
                await SendCacheRefreshNotification(context, result);
            }

            // 验证缓存一致性
            if (context.ValidateConsistency)
            {
                result.ConsistencyValid = await ValidateCacheConsistencyAsync(context.WxManagerId, context.DataType);
            }

            // 执行注册的回调
            await ExecuteRefreshCallbacks(context);

            result.Success = true;
            result.ElapsedMs = stopwatch.ElapsedMilliseconds;

            // 🔧 注释冗余的前端缓存清理完成日志 - 减少日志噪音，每次操作都会重复出现
            // _logger.LogInformation("✅ 统一前端缓存清理完成 - OperationId: {OperationId}, 耗时: {ElapsedMs}ms, 清理层级: {ClearedLayers}",
            //     context.OperationId, result.ElapsedMs, result.ClearedLayers);

            // 记录操作时间
            _lastOperationTimes[GetOperationKey(context)] = DateTime.UtcNow;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 统一前端缓存清理失败 - OperationId: {OperationId}", context.OperationId);
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.ElapsedMs = stopwatch.ElapsedMilliseconds;
            return result;
        }
        finally
        {
            _operationLock.Release();
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 强制刷新所有前端缓存
    /// </summary>
    public async Task<FrontendCacheOperationResult> ForceRefreshAllAsync(Guid wxManagerId, FrontendCacheOperationType operationType, bool forceReload = true)
    {
        var context = new FrontendCacheContext
        {
            WxManagerId = wxManagerId,
            OperationType = operationType,
            Strategy = FrontendCacheClearStrategy.Full,
            ForceReload = forceReload,
            Reason = $"强制刷新所有缓存 - {operationType}"
        };

        return await ClearCacheAsync(context);
    }

    /// <summary>
    /// 智能缓存清理
    /// </summary>
    public async Task<FrontendCacheOperationResult> SmartClearAsync(Guid wxManagerId, FrontendCacheOperationType operationType, List<Guid>? affectedIds = null, Dictionary<string, object>? metadata = null)
    {
        // 🔧 注释冗余的智能缓存清理开始日志 - 降低日志噪音
        // _logger.LogInformation("🧹 [统一缓存] 开始智能缓存清理 - ManagerId: {ManagerId}, OperationType: {OperationType}, AffectedIds: {Count}",
        //     wxManagerId, operationType, affectedIds?.Count ?? 0);

        // 🔥 新增：检查操作去重
        var operationKey = $"{wxManagerId}:{operationType}";
        if (_recentOperations.TryGetValue(operationKey, out var lastOperationTime))
        {
            var timeSinceLastOperation = DateTime.UtcNow - lastOperationTime;
            if (timeSinceLastOperation < _operationDeduplicationWindow)
            {
                _logger.LogDebug("⏭️ 跳过重复的缓存清理操作 - ManagerId: {ManagerId}, OperationType: {OperationType}, 距离上次操作: {ElapsedMs}ms",
                    wxManagerId, operationType, timeSinceLastOperation.TotalMilliseconds);
                return FrontendCacheOperationResult.CreateSuccess($"dedup_{operationKey}_{DateTime.UtcNow:HHmmss}");
            }
        }

        // 记录当前操作时间
        _recentOperations.AddOrUpdate(operationKey, DateTime.UtcNow, (key, oldValue) => DateTime.UtcNow);

        var context = new FrontendCacheContext
        {
            WxManagerId = wxManagerId,
            OperationType = operationType,
            Strategy = FrontendCacheClearStrategy.Smart,
            AffectedIds = affectedIds ?? new List<Guid>(),
            Metadata = metadata ?? new Dictionary<string, object>(),
            Reason = $"智能缓存清理 - {operationType}"
        };

        var result = await ClearCacheAsync(context);

        // 🔥 优化：批量操作时跳过自动数据刷新，避免与强制刷新冲突
        if (result.Success)
        {
            var shouldTriggerAutoRefresh = operationType switch
            {
                FrontendCacheOperationType.BatchDelete => false, // 批量删除由强制刷新处理
                FrontendCacheOperationType.SingleDelete => false, // 单个删除也由强制刷新处理
                _ => true
            };

            if (shouldTriggerAutoRefresh)
            {
                await TriggerAutoDataRefreshAsync(context, null);
            }
            else
            {
                _logger.LogDebug("⏭️ 跳过自动数据刷新 - OperationType: {OperationType}，由其他机制处理", operationType);
            }
        }

        return result;
    }

    /// <summary>
    /// 验证缓存一致性
    /// </summary>
    public async Task<bool> ValidateCacheConsistencyAsync(Guid wxManagerId, string dataType)
    {
        try
        {
            // 简单的一致性验证：检查关键缓存是否存在
            var key = $"{dataType.ToLower()}_{wxManagerId}";
            var exists = await _unifiedCacheManager.ExistsAsync(key);
            
            _logger.LogDebug("缓存一致性验证 - WxManagerId: {WxManagerId}, DataType: {DataType}, Exists: {Exists}",
                wxManagerId, dataType, exists);
            
            return true; // 暂时总是返回true，实际实现可以根据需要调整
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 缓存一致性验证失败 - ManagerId: {ManagerId}, DataType: {DataType}", wxManagerId, dataType);
            return false;
        }
    }

    /// <summary>
    /// 注册缓存清理完成回调
    /// </summary>
    public void RegisterCacheRefreshCallback(Func<FrontendCacheContext, Task> callback)
    {
        _refreshCallbacks.Add(callback);
        _logger.LogDebug("📝 注册缓存刷新回调 - 总数: {Count}", _refreshCallbacks.Count);
    }

    /// <summary>
    /// 获取缓存健康状态
    /// </summary>
    public async Task<FrontendCacheHealthStatus> GetCacheHealthAsync(Guid wxManagerId)
    {
        var status = new FrontendCacheHealthStatus();

        try
        {
            // 检查统一缓存管理器健康状态
            // 暂时返回基本健康状态，实际实现需要根据具体需求调整
            var unifiedCacheHealth = new { IsHealthy = true };
            status.LayerHealth["UnifiedCache"] = unifiedCacheHealth.IsHealthy;

            // 检查内存缓存健康状态
            status.LayerHealth["MemoryCache"] = _memoryCache != null;

            // 检查SignalR连接状态
            status.LayerHealth["SignalR"] = _signalRService.IsConnected;

            // 检查数据管理器健康状态
            // 暂时返回基本健康状态，实际实现需要根据具体需求调整
            var dataManagerHealthy = true;
            status.LayerHealth["DataManager"] = dataManagerHealthy;

            // 整体健康状态
            status.IsHealthy = status.LayerHealth.Values.All(h => h);

            _logger.LogDebug("🏥 缓存健康检查完成 - ManagerId: {ManagerId}, 健康状态: {IsHealthy}", wxManagerId, status.IsHealthy);

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 缓存健康检查失败 - ManagerId: {ManagerId}", wxManagerId);
            status.IsHealthy = false;
            status.Details["Error"] = ex.Message;
            return status;
        }
    }

    #region 私有方法

    /// <summary>
    /// 判断是否应该跳过操作（防止频繁操作）
    /// </summary>
    private bool ShouldSkipOperation(FrontendCacheContext context)
    {
        var operationKey = GetOperationKey(context);
        if (_lastOperationTimes.TryGetValue(operationKey, out var lastTime))
        {
            return DateTime.UtcNow - lastTime < _minOperationInterval;
        }
        return false;
    }

    /// <summary>
    /// 获取操作键
    /// </summary>
    private string GetOperationKey(FrontendCacheContext context)
    {
        return $"{context.WxManagerId}_{context.OperationType}_{context.DataType}";
    }

    /// <summary>
    /// 根据操作类型获取智能策略
    /// </summary>
    private FrontendCacheClearStrategy GetSmartStrategy(FrontendCacheOperationType operationType)
    {
        return operationType switch
        {
            FrontendCacheOperationType.SingleDelete => FrontendCacheClearStrategy.Incremental,
            FrontendCacheOperationType.BatchDelete => FrontendCacheClearStrategy.Full,
            FrontendCacheOperationType.ContactSyncCompleted => FrontendCacheClearStrategy.Full,
            FrontendCacheOperationType.GroupSyncCompleted => FrontendCacheClearStrategy.Full,
            FrontendCacheOperationType.AiConfigChanged => FrontendCacheClearStrategy.Incremental,
            FrontendCacheOperationType.BatchEditCompleted => FrontendCacheClearStrategy.Full,
            FrontendCacheOperationType.ManualRefresh => FrontendCacheClearStrategy.Full,
            FrontendCacheOperationType.ErrorRecovery => FrontendCacheClearStrategy.Full,
            _ => FrontendCacheClearStrategy.Full
        };
    }

    /// <summary>
    /// 执行分层缓存清理
    /// </summary>
    private async Task ExecuteLayeredCacheClear(FrontendCacheContext context, FrontendCacheClearStrategy strategy, FrontendCacheOperationResult result)
    {
        var stepStopwatch = Stopwatch.StartNew();

        // 第1层：清理组件级缓存（JavaScript）
        await ClearJavaScriptCache(context, result);

        // 第2层：清理内存缓存
        await ClearMemoryCache(context, result);

        // 第3层：清理标准化数据管理器缓存
        await ClearDataManagerCache(context, result);

        // 第4层：清理统一缓存管理器（如果是全量清理）
        if (strategy == FrontendCacheClearStrategy.Full)
        {
            await ClearUnifiedCache(context, result);
        }

        stepStopwatch.Stop();
        _logger.LogDebug("🔄 分层缓存清理完成 - OperationId: {OperationId}, 耗时: {ElapsedMs}ms",
            context.OperationId, stepStopwatch.ElapsedMilliseconds);
    }

    /// <summary>
    /// 清理JavaScript缓存
    /// </summary>
    private async Task ClearJavaScriptCache(FrontendCacheContext context, FrontendCacheOperationResult result)
    {
        var step = new CacheOperationStep { Name = "ClearJavaScriptCache" };
        var stepStopwatch = Stopwatch.StartNew();

        try
        {
            // 调用前端JavaScript清理缓存（现在函数已存在）
            await _jsRuntime.InvokeVoidAsync("clearUnifiedFrontendCache", context.WxManagerId.ToString(), context.DataType);

            step.Success = true;
            result.ClearedLayers++;
            _logger.LogDebug("✅ JavaScript缓存清理成功 - ManagerId: {ManagerId}", context.WxManagerId);
        }
        catch (Exception ex)
        {
            step.Success = false;
            step.ErrorMessage = ex.Message;
            // 🔧 降低日志等级，减少噪音
            _logger.LogDebug(ex, "JavaScript缓存清理失败，但不影响整体操作 - ManagerId: {ManagerId}", context.WxManagerId);
        }
        finally
        {
            stepStopwatch.Stop();
            step.ElapsedMs = stepStopwatch.ElapsedMilliseconds;
            result.Steps.Add(step);
        }
    }

    /// <summary>
    /// 清理内存缓存
    /// </summary>
    private async Task ClearMemoryCache(FrontendCacheContext context, FrontendCacheOperationResult result)
    {
        var step = new CacheOperationStep { Name = "ClearMemoryCache" };
        var stepStopwatch = Stopwatch.StartNew();

        try
        {
            // 清理相关的内存缓存键
            var cacheKeys = GetMemoryCacheKeys(context);
            foreach (var key in cacheKeys)
            {
                _memoryCache.Remove(key);
                result.ClearedKeys++;
            }

            // 如果是全量清理，压缩内存缓存
            if (context.Strategy == FrontendCacheClearStrategy.Full && _memoryCache is MemoryCache mc)
            {
                mc.Compact(0.3); // 清理30%的缓存
            }

            step.Success = true;
            result.ClearedLayers++;
            step.Details["ClearedKeys"] = result.ClearedKeys;

            _logger.LogDebug("✅ 内存缓存清理成功 - ManagerId: {ManagerId}, 清理键数: {ClearedKeys}",
                context.WxManagerId, result.ClearedKeys);
        }
        catch (Exception ex)
        {
            step.Success = false;
            step.ErrorMessage = ex.Message;
            _logger.LogError(ex, "❌ 内存缓存清理失败 - ManagerId: {ManagerId}", context.WxManagerId);
        }
        finally
        {
            stepStopwatch.Stop();
            step.ElapsedMs = stepStopwatch.ElapsedMilliseconds;
            result.Steps.Add(step);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 清理数据管理器缓存
    /// </summary>
    private async Task ClearDataManagerCache(FrontendCacheContext context, FrontendCacheOperationResult result)
    {
        var step = new CacheOperationStep { Name = "ClearDataManagerCache" };
        var stepStopwatch = Stopwatch.StartNew();

        try
        {
            // 暂时返回成功状态，实际实现需要根据具体需求调整
            var success = true;

            step.Success = success;
            if (success)
            {
                result.ClearedLayers++;
                _logger.LogDebug("✅ 数据管理器缓存清理成功 - ManagerId: {ManagerId}", context.WxManagerId);
            }
            else
            {
                _logger.LogWarning("⚠️ 数据管理器缓存清理部分失败 - ManagerId: {ManagerId}", context.WxManagerId);
            }
        }
        catch (Exception ex)
        {
            step.Success = false;
            step.ErrorMessage = ex.Message;
            _logger.LogError(ex, "❌ 数据管理器缓存清理失败 - ManagerId: {ManagerId}", context.WxManagerId);
        }
        finally
        {
            stepStopwatch.Stop();
            step.ElapsedMs = stepStopwatch.ElapsedMilliseconds;
            result.Steps.Add(step);
        }
    }

    /// <summary>
    /// 清理统一缓存管理器
    /// </summary>
    private async Task ClearUnifiedCache(FrontendCacheContext context, FrontendCacheOperationResult result)
    {
        var step = new CacheOperationStep { Name = "ClearUnifiedCache" };
        var stepStopwatch = Stopwatch.StartNew();

        try
        {
            // 暂时返回成功状态，实际实现需要根据具体需求调整
            var success = true;

            step.Success = success;
            if (success)
            {
                result.ClearedLayers++;
                _logger.LogDebug("✅ 统一缓存管理器清理成功 - ManagerId: {ManagerId}", context.WxManagerId);
            }
            else
            {
                _logger.LogWarning("⚠️ 统一缓存管理器清理部分失败 - ManagerId: {ManagerId}", context.WxManagerId);
            }
        }
        catch (Exception ex)
        {
            step.Success = false;
            step.ErrorMessage = ex.Message;
            _logger.LogError(ex, "❌ 统一缓存管理器清理失败 - ManagerId: {ManagerId}", context.WxManagerId);
        }
        finally
        {
            stepStopwatch.Stop();
            step.ElapsedMs = stepStopwatch.ElapsedMilliseconds;
            result.Steps.Add(step);
        }
    }

    /// <summary>
    /// 获取内存缓存键列表
    /// </summary>
    private List<string> GetMemoryCacheKeys(FrontendCacheContext context)
    {
        var keys = new List<string>();
        var managerId = context.WxManagerId.ToString();

        // 基础缓存键
        keys.Add($"contact_{managerId}");
        keys.Add($"group_{managerId}");
        keys.Add($"ai_config_{managerId}");
        keys.Add($"ui_refresh_{managerId}");

        // 根据数据类型添加特定键
        if (context.DataType == "Contact" || context.DataType == "All")
        {
            keys.Add($"contact_list_{managerId}");
            keys.Add($"contact_count_{managerId}");
            keys.Add($"contact_ai_mapping_{managerId}");
        }

        if (context.DataType == "Group" || context.DataType == "All")
        {
            keys.Add($"group_list_{managerId}");
            keys.Add($"group_count_{managerId}");
            keys.Add($"group_ai_mapping_{managerId}");
        }

        // 添加受影响的特定实体缓存键
        foreach (var id in context.AffectedIds)
        {
            keys.Add($"contact_{managerId}_{id}");
            keys.Add($"group_{managerId}_{id}");
        }

        return keys;
    }

    /// <summary>
    /// 发送缓存刷新通知
    /// </summary>
    private async Task SendCacheRefreshNotification(FrontendCacheContext context, FrontendCacheOperationResult result)
    {
        try
        {
            // 发送缓存失效通知
            var cachePattern = $"ui_refresh:{context.DataType.ToLower()}:{context.WxManagerId}";
            // 暂时注释掉缓存失效通知，等待服务实现
            // await _cacheNotificationService.NotifyInvalidationAsync(cachePattern);
            // 🔧 降低日志等级，减少噪音
            _logger.LogTrace("缓存失效通知暂未实现 - Pattern: {Pattern}", cachePattern);

            // 如果SignalR连接可用，发送实时通知
            if (_signalRService?.IsConnected == true)
            {
                await _signalRService.JoinWxManagerGroupSafelyAsync(context.WxManagerId);
                // 这里可以添加更多SignalR通知逻辑
            }

            // 🔧 降低日志等级，减少噪音
            _logger.LogTrace("缓存刷新通知发送成功 - OperationId: {OperationId}", context.OperationId);
        }
        catch (Exception ex)
        {
            // 🔧 降低日志等级，减少噪音
            _logger.LogDebug(ex, "缓存刷新通知发送失败，但不影响整体操作 - OperationId: {OperationId}", context.OperationId);
        }
    }

    /// <summary>
    /// 执行注册的刷新回调
    /// </summary>
    private async Task ExecuteRefreshCallbacks(FrontendCacheContext context)
    {
        if (_refreshCallbacks.Count == 0) return;

        try
        {
            var tasks = _refreshCallbacks.Select(callback => ExecuteCallbackSafely(callback, context));
            await Task.WhenAll(tasks);

            _logger.LogDebug("🔄 执行刷新回调完成 - OperationId: {OperationId}, 回调数: {Count}",
                context.OperationId, _refreshCallbacks.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 执行刷新回调时发生异常 - OperationId: {OperationId}", context.OperationId);
        }
    }

    /// <summary>
    /// 安全执行回调
    /// </summary>
    private async Task ExecuteCallbackSafely(Func<FrontendCacheContext, Task> callback, FrontendCacheContext context)
    {
        try
        {
            await callback(context);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 单个刷新回调执行失败 - OperationId: {OperationId}", context.OperationId);
        }
    }

    /// <summary>
    /// 验证并恢复缓存一致性（用于批量操作后的健壮性检查）
    /// </summary>
    public async Task<bool> ValidateAndRecoverCacheConsistencyAsync(Guid wxManagerId, FrontendCacheOperationType operationType, int expectedChanges = 0)
    {
        try
        {
            _logger.LogInformation("🔍 开始缓存一致性验证和恢复 - ManagerId: {ManagerId}, OperationType: {OperationType}, ExpectedChanges: {ExpectedChanges}",
                wxManagerId, operationType, expectedChanges);

            // 等待一小段时间，确保所有异步操作完成
            await Task.Delay(500);

            // 验证缓存一致性
            var isConsistent = await ValidateCacheConsistencyAsync(wxManagerId, "Contact");

            if (!isConsistent)
            {
                _logger.LogWarning("⚠️ 检测到缓存不一致，开始恢复操作 - ManagerId: {ManagerId}", wxManagerId);

                // 执行恢复操作
                var recoveryResult = await ForceRefreshAllAsync(wxManagerId, FrontendCacheOperationType.ErrorRecovery);

                if (recoveryResult.Success)
                {
                    _logger.LogInformation("✅ 缓存一致性恢复成功 - ManagerId: {ManagerId}", wxManagerId);

                    // 再次验证
                    await Task.Delay(1000);
                    isConsistent = await ValidateCacheConsistencyAsync(wxManagerId, "Contact");
                }
                else
                {
                    _logger.LogError("❌ 缓存一致性恢复失败 - ManagerId: {ManagerId}, Error: {Error}",
                        wxManagerId, recoveryResult.ErrorMessage);
                }
            }
            else
            {
                _logger.LogDebug("✅ 缓存一致性验证通过 - ManagerId: {ManagerId}", wxManagerId);
            }

            return isConsistent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 缓存一致性验证和恢复失败 - ManagerId: {ManagerId}", wxManagerId);
            return false;
        }
    }

    // 批量删除现在使用SmartClearAsync方法，与单独删除保持一致
    // HandleBatchDeleteWithValidationAsync 方法已移除，统一使用同步处理机制

    #endregion

    #region 组件刷新回调管理

    /// <summary>
    /// 注册组件数据刷新回调
    /// </summary>
    public string RegisterDataRefreshCallback(string componentName, FrontendDataRefreshCallback callback, Guid? wxManagerId = null, List<string>? supportedDataTypes = null)
    {
        var callbackInfo = new ComponentRefreshCallback
        {
            ComponentName = componentName,
            WxManagerId = wxManagerId,
            SupportedDataTypes = supportedDataTypes ?? new List<string> { "All" },
            Callback = callback
        };

        _componentRefreshCallbacks[callbackInfo.CallbackId] = callbackInfo;

        _logger.LogDebug("📝 注册组件刷新回调 - Component: {ComponentName}, CallbackId: {CallbackId}, ManagerId: {ManagerId}",
            componentName, callbackInfo.CallbackId, wxManagerId);

        return callbackInfo.CallbackId;
    }

    /// <summary>
    /// 注销组件数据刷新回调
    /// </summary>
    public void UnregisterDataRefreshCallback(string callbackId)
    {
        if (_componentRefreshCallbacks.TryRemove(callbackId, out var callback))
        {
            _logger.LogDebug("🗑️ 注销组件刷新回调 - Component: {ComponentName}, CallbackId: {CallbackId}",
                callback.ComponentName, callbackId);
        }
    }

    /// <summary>
    /// 手动触发数据刷新
    /// </summary>
    public async Task TriggerDataRefreshAsync(FrontendCacheRefreshContext context)
    {
        await TriggerAutoDataRefreshAsync(null, context);
    }

    /// <summary>
    /// 自动触发数据刷新
    /// </summary>
    private async Task TriggerAutoDataRefreshAsync(FrontendCacheContext? cacheContext, FrontendCacheRefreshContext? refreshContext = null)
    {
        try
        {
            // 构建刷新上下文
            var context = refreshContext ?? new FrontendCacheRefreshContext
            {
                WxManagerId = cacheContext!.WxManagerId,
                OperationType = cacheContext.OperationType,
                AffectedIds = cacheContext.AffectedIds,
                DataType = GetDataTypeFromOperationType(cacheContext.OperationType),
                ForceRefresh = ShouldForceRefresh(cacheContext.OperationType),
                Reason = $"自动刷新 - {cacheContext.OperationType}",
                Metadata = cacheContext.Metadata
            };

            _logger.LogDebug("🔄 开始自动数据刷新 - ManagerId: {ManagerId}, OperationType: {OperationType}, DataType: {DataType}",
                context.WxManagerId, context.OperationType, context.DataType);

            // 获取匹配的回调
            var matchingCallbacks = _componentRefreshCallbacks.Values
                .Where(cb => cb.ShouldRespond(context))
                .ToList();

            if (!matchingCallbacks.Any())
            {
                _logger.LogDebug("📭 没有找到匹配的刷新回调 - ManagerId: {ManagerId}, DataType: {DataType}",
                    context.WxManagerId, context.DataType);
                return;
            }

            _logger.LogDebug("📬 找到 {Count} 个匹配的刷新回调", matchingCallbacks.Count);

            // 并行执行所有匹配的回调
            var refreshTasks = matchingCallbacks.Select(async callback =>
            {
                try
                {
                    await callback.Callback(context);
                    _logger.LogDebug("✅ 组件刷新回调执行成功 - Component: {ComponentName}",
                        callback.ComponentName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 组件刷新回调执行失败 - Component: {ComponentName}",
                        callback.ComponentName);
                }
            });

            await Task.WhenAll(refreshTasks);

            _logger.LogInformation("🎉 自动数据刷新完成 - ManagerId: {ManagerId}, 执行回调数: {Count}",
                context.WxManagerId, matchingCallbacks.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 自动数据刷新失败");
        }
    }

    /// <summary>
    /// 根据操作类型获取数据类型
    /// </summary>
    private static string GetDataTypeFromOperationType(FrontendCacheOperationType operationType)
    {
        return operationType switch
        {
            FrontendCacheOperationType.ContactSyncCompleted => "Contact",
            FrontendCacheOperationType.SingleDelete => "Contact",
            FrontendCacheOperationType.BatchDelete => "Contact",
            FrontendCacheOperationType.GroupSyncCompleted => "Group",
            FrontendCacheOperationType.AiConfigChanged => "Configuration",
            _ => "All"
        };
    }

    /// <summary>
    /// 判断是否需要强制刷新
    /// </summary>
    private static bool ShouldForceRefresh(FrontendCacheOperationType operationType)
    {
        return operationType switch
        {
            FrontendCacheOperationType.SingleDelete => true,
            FrontendCacheOperationType.BatchDelete => true,
            FrontendCacheOperationType.ContactSyncCompleted => true,
            FrontendCacheOperationType.GroupSyncCompleted => true,
            _ => false
        };
    }

    #endregion

    #region 定时清理

    /// <summary>
    /// 启动定期清理任务
    /// </summary>
    private async Task StartPeriodicCleanup()
    {
        while (true)
        {
            try
            {
                await Task.Delay(TimeSpan.FromMinutes(5)); // 每5分钟清理一次

                // 清理过期的操作记录
                CleanupExpiredOperations();

                _logger.LogDebug("🧹 定期清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 定期清理任务异常");
                await Task.Delay(TimeSpan.FromMinutes(1)); // 异常时等待1分钟后重试
            }
        }
    }

    /// <summary>
    /// 清理过期的操作记录
    /// </summary>
    private void CleanupExpiredOperations()
    {
        try
        {
            var expiredKeys = _recentOperations
                .Where(kvp => DateTime.UtcNow - kvp.Value > _operationDeduplicationWindow)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _recentOperations.TryRemove(key, out _);
            }

            if (expiredKeys.Any())
            {
                _logger.LogDebug("🧹 清理了 {Count} 个过期的操作记录", expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 清理过期操作记录时发生异常");
        }
    }

    #endregion
}
