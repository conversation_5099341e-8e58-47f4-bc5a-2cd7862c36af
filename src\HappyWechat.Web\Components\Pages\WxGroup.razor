﻿@page "/wxgroup"
@using HappyWechat.Application.DTOs.Responses
@using HappyWechat.Application.DTOs.Requests.Commands
@using HappyWechat.Application.DTOs.Requests.Queries
@using HappyWechat.Application.Interfaces
@using HappyWechat.Application.Commands.Wx
@using HappyWechat.Domain.ValueObjects.Enums
@using HappyWechat.Web.Services.Interfaces
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.JSInterop
@using System.Security.Claims
@using HappyWechat.Web.VOs
@using HappyWechat.Application.Commons
@using HappyWechat.Application.DTOs.AiAgent
@using HappyWechat.Web.Components.Common
@using HappyWechat.Web.Services
@using HappyWechat.Infrastructure.Configuration
@using HappyWechat.Infrastructure.MessageProcessing
@inherits BaseWxPageComponent
@inject IWxService WxService
@inject IWxGroupService WxGroupService
@inject HappyWechat.Web.Services.Interfaces.IUnifiedDataManager UnifiedDataManager
@inject IUnifiedConfigManager UnifiedConfigManager
@inject IConfigurationEffectivenessTracker ConfigurationEffectivenessTracker
@inject IConfigurationConsistencyService ConfigurationConsistencyService
@inject HappyWechat.Web.Services.ISearchStateService SearchStateService
@inject HappyWechat.Web.Services.Interfaces.IDataSyncManager DataSyncManager
@inject HappyWechat.Web.Services.ISearchDebounceService SearchDebounceService
@inject HappyWechat.Web.Services.GroupSyncDebouncer GroupSyncDebouncer
@inject IUnifiedConfigService UnifiedConfigService


<PageTitle>微信群组管理</PageTitle>

<style>
    .skeleton-table-container {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
    }

    .skeleton-table-header {
        display: flex;
        background: #f5f5f5;
        border-bottom: 1px solid #e0e0e0;
        font-size: 14px;
        font-weight: 500;
        align-items: center;
        padding: 8px 0;
    }

    .skeleton-table-header div {
        padding: 8px 12px;
        text-align: left;
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
    }

    .search-input-enhanced {
        position: relative;
    }

    .search-input-enhanced .search-status {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
    }
</style>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-0">
    @if (!_hasWeChatAccounts)
    {
        <MudAlert Severity="Severity.Warning" Class="ma-4">
            <MudText Typo="Typo.h6">需要先设置微信账户</MudText>
            <MudText Class="mt-2">您还没有任何微信账户。请先前往微信管理页面添加您的微信账户。</MudText>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-2" OnClick="NavigateToWxManage">
                前往微信管理
            </MudButton>
        </MudAlert>
    }
    else if (!_hasLoggedInAccounts)
    {
        <MudAlert Severity="Severity.Info" Class="ma-4">
            <MudText Typo="Typo.h6">微信账户未登录</MudText>
            <MudText Class="mt-2">您的微信账户都未登录。请前往微信管理页面登录您的微信账户。</MudText>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-2" OnClick="NavigateToWxManage">
                前往微信管理
            </MudButton>
        </MudAlert>
    }
    else if (!_hasEnabledAccounts)
    {
        <MudAlert Severity="Severity.Warning" Class="ma-4">
            <MudText Typo="Typo.h6">微信账户已被禁用</MudText>
            <MudText Class="mt-2">您的微信账户都已被禁用。请前往微信管理页面启用您的微信账户，或联系管理员。</MudText>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-2" OnClick="NavigateToWxManage">
                前往微信管理
            </MudButton>
        </MudAlert>
    }
    else
    {
        <!-- 整体卡片容器 -->
        <MudCard Elevation="1" Class="ma-0">
            <!-- 微信账号Tab选择和操作栏 -->
            <MudCardContent Class="pa-0">
                <!-- 顶部标签页区域 -->
                <MudPaper Elevation="0" Class="border-bottom">
                    <MudTabs @bind-ActivePanelIndex="_activeTabIndex"
                             Elevation="0"
                             PanelClass="pa-0"
                             HeaderClass="mb-0"
                             Class="mud-width-full"
                             @bind-ActivePanelIndex:after="OnTabChanged">
                        @if (wxAccounts != null && wxAccounts.Any())
                        {
                            @foreach (var account in wxAccounts.Select((account, index) => new { account, index }))
                            {
                                <MudTabPanel>
                                    <TabContent>
                                        <MudStack AlignItems="AlignItems.Center" Row="true" Spacing="1" Class="tab-content-40px">
                                            <!-- 左侧头像 -->
                                            <MudAvatar Size="Size.Medium">
                                                @if (!string.IsNullOrEmpty(account.account.HeadUrl))
                                                {
                                                    <MudImage Src="@account.account.HeadUrl" />
                                                }
                                                else
                                                {
                                                    <MudIcon Icon="Icons.Material.Filled.Person" />
                                                }
                                            </MudAvatar>
                                            <!-- 右侧信息区域 -->
                                            <MudStack Row="false" Spacing="0" Justify="Justify.Center" AlignItems="AlignItems.Start">
                                                <!-- 上行：微信昵称 -->
                                                <MudText Typo="Typo.body2" Class="mb-0" Color="Color.Primary">@account.account.NickName</MudText>
                                                <!-- 下行：在线状态 -->
                                                <MudChip T="string"
                                                         Color="@GetStatusColor(account.account.WxStatus)"
                                                         Size="Size.Small"
                                                         Class="mt-1">
                                                    @GetStatusText(account.account.WxStatus)
                                                </MudChip>
                                            </MudStack>
                                        </MudStack>
                                    </TabContent>
                                    <ChildContent>
                                        <!-- 操作栏 -->
                                        <MudPaper Elevation="0" Class="operation-toolbar">
                                            <MudGrid AlignItems="AlignItems.Center" Spacing="1" Class="align-center">
                                                <!-- 第一行：搜索和添加群 -->
                                                <!-- 搜索联系人 -->
                                                <MudItem xs="12" sm="6" md="2">
                                                    <div class="search-input-enhanced">
                                                        <MudTextField @bind-Value="_searchedNickName"
                                                                      @oninput="OnSearchInput"
                                                                      Variant="Variant.Outlined"
                                                                      Adornment="Adornment.Start"
                                                                      AdornmentIcon="Icons.Material.Filled.Search"
                                                                      Placeholder="输入群昵称搜索 (防抖动)"
                                                                      Dense="true"
                                                                      Class="@(_isSearching ? "searching" : "")"
                                                                      Disabled="_isSearching"
                                                                      Immediate="false" />
                                                        <div class="search-status">
                                                            @if (_isSearching)
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Color="Color.Primary" Indeterminate="true" />
                                                            }
                                                            else if (!string.IsNullOrEmpty(_searchedNickName) && Elements.Any())
                                                            {
                                                                <MudIcon Icon="Icons.Material.Filled.Check" Color="Color.Success" Size="Size.Small" />
                                                            }
                                                        </div>
                                                    </div>
                                                </MudItem>

                                                <!-- 搜索按钮 -->
                                                <MudItem xs="12" sm="6" md="1">
                                                    <MudButton Variant="Variant.Outlined"
                                                               Color="Color.Primary"
                                                               OnClick="SearchGroups"
                                                               Size="Size.Small"
                                                               FullWidth="true">
                                                        搜索
                                                    </MudButton>
                                                </MudItem>

                                                <!-- 添加群组 -->
                                                <MudItem xs="12" sm="6" md="3">
                                                    <MudTextField @bind-Value="manualGroupId"
                                                                  @onkeypress="OnGroupIdKeyPress"
                                                                  Variant="Variant.Outlined"
                                                                  Adornment="Adornment.Start"
                                                                  AdornmentIcon="Icons.Material.Filled.GroupAdd"
                                                                  Placeholder="请输入群组ID添加群组"
                                                                  HelperText="请输入群组ID添加群组"
                                                                  Immediate="true"
                                                                  Validation="@(new Func<string, string?>(ValidateGroupId))"
                                                                  Dense="true" />
                                                </MudItem>

                                                <MudItem xs="6" sm="3" md="1" Class="d-flex align-center">
                                                    <MudButton Variant="Variant.Filled"
                                                               Color="Color.Primary"
                                                               Size="Size.Small"
                                                               OnClick="AddGroupManually"
                                                               Disabled="@(string.IsNullOrEmpty(manualGroupId?.Trim()) || !HasSelectedAccount || _isAddingGroup)"
                                                               StartIcon="@(_isAddingGroup ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.GroupAdd)"
                                                               FullWidth="true">
                                                        @if (_isAddingGroup)
                                                        {
                                                            <span>添加中</span>
                                                        }
                                                        else
                                                        {
                                                            <span>添加</span>
                                                        }
                                                    </MudButton>
                                                </MudItem>

                                                <!-- 右侧操作按钮 -->
                                                <MudItem xs="12" md="5" Class="d-flex justify-end align-center">
                                                    <MudStack Row="true" Spacing="2" AlignItems="AlignItems.Center">
                                                        <!-- 选中状态显示 -->
                                                        @if (_selectedGroupIds.Count > 0)
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                                                已选中 @_selectedGroupIds.Count 个群组
                                                            </MudChip>
                                                            <MudButton Variant="Variant.Text"
                                                                       Color="Color.Warning"
                                                                       Size="Size.Small"
                                                                       OnClick="ClearSelection">
                                                                清空选择
                                                            </MudButton>
                                                        }

                                                        <MudButton Variant="Variant.Filled"
                                                                   Color="Color.Success"
                                                                   Size="Size.Medium"
                                                                   OnClick="GetGroupsForCurrentAccount"
                                                                   Disabled="@(_isLoadingGroups || !HasSelectedAccount)">
                                                            @if (_isLoadingGroups)
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                                                <span>获取中...</span>
                                                            }
                                                            else
                                                            {
                                                                <span>获取群组列表</span>
                                                            }
                                                        </MudButton>

                                                        <MudButton Variant="Variant.Outlined"
                                                                   Color="Color.Secondary"
                                                                   Size="Size.Medium"
                                                                   OnClick="ShowBatchEditDialog"
                                                                   Disabled="@(_selectedGroupIds.Count == 0 || _deletingGroupIds.Count > 0 || _isBatchDeleting)">
                                                            @if (_isBatchDeleting)
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                                <span class="ml-1">批量处理中...</span>
                                                            }
                                                            else
                                                            {
                                                                <span>批量编辑 (@_selectedGroupIds.Count)</span>
                                                            }
                                                        </MudButton>
                                                    </MudStack>
                                                </MudItem>
                                            </MudGrid>
                                        </MudPaper>



                                        <!-- 搜索状态提示 -->
                                        @if (_isSearching)
                                        {
                                            <MudPaper Elevation="0" Class="pa-2">
                                                <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                                    <MudProgressCircular Color="Color.Primary" Size="Size.Small" Indeterminate="true" />
                                                    <MudText Typo="Typo.body2" Class="mud-text-secondary">正在搜索微信群...</MudText>
                                                </MudStack>
                                            </MudPaper>
                                        }

                                        <!-- 群组列表 -->
                                        <MudPaper Elevation="0" Class="pa-0">
                                            @if (_isLoading || _isSearching)
                                            {
                                                <!-- 骨架屏加载 -->
                                                <div class="skeleton-table-container">
                                                    <div class="skeleton-table-header">
                                                        <div style="width: 50px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;"></div>
                                                        <div style="width: 80px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">群头像</div>
                                                        <div style="width: 140px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">群ID</div>
                                                        <div style="width: 160px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">群昵称</div>
                                                        <div style="width: 100px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">群主</div>
                                                        <div style="width: 120px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">群成员数</div>
                                                        <div style="width: 200px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">机器人配置</div>
                                                        <div style="width: 120px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">模式</div>
                                                        <div style="width: 100px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">@@回复模式</div>
                                                        <div style="width: 160px; height: 40px; background: #f5f5f5;">操作</div>
                                                    </div>
                                                    <SkeletonLoader Type="SkeletonLoader.SkeletonType.GroupList" Count="@(_pageSize)" />
                                                </div>
                                            }
                                            else
                                            {
                                                <MudTable Items="@Elements"
                                                      Hover="true"
                                                      Striped="true"
                                                      Loading="false"
                                                      Dense="true"
                                                      Class="compact-table fixed-layout-table">
                                            <HeaderContent>
                                                <MudTh Style="width: 50px;">
                                                    <MudCheckBox @bind-Value="_isAllSelected"
                                                                 Indeterminate="_isIndeterminate"
                                                                 TriState="true"
                                                                 Size="Size.Small" />
                                                </MudTh>
                                                <MudTh Style="width: 80px;">群头像</MudTh>
                                                <MudTh Style="width: 140px;">群id</MudTh>
                                                <MudTh Style="width: 160px;">群昵称</MudTh>
                                                <MudTh Style="width: 100px;">群主</MudTh>
                                                <MudTh Style="width: 120px;">群成员数</MudTh>
                                                <MudTh Style="width: 200px;">机器人配置</MudTh>
                                                <MudTh Style="width: 120px;">模式</MudTh>
                                                <MudTh Style="width: 100px;">@@回复模式</MudTh>
                                                <MudTh Style="width: 160px;">操作</MudTh>
                                            </HeaderContent>
                                            <RowTemplate>
                                                <MudTd Class="compact-row">
                                                    <MudCheckBox Value="@_selectedGroupIds.Contains(context.Id)"
                                                                 ValueChanged="@((bool value) => OnGroupSelectionChanged(context.Id, value))"
                                                                 Size="Size.Small" />
                                                </MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudAvatar Size="Size.Medium">
                                                        @if (!string.IsNullOrEmpty(context.SmallHeadImgUrl))
                                                        {
                                                            <MudImage Src="@context.SmallHeadImgUrl" />
                                                        }
                                                        else
                                                        {
                                                            <MudIcon Icon="Icons.Material.Filled.Group" />
                                                        }
                                                    </MudAvatar>
                                                </MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@context.ChatRoomId</MudText></MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@context.NickName</MudText></MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@(context.IsOwner ? "群主" : "成员")</MudText></MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@context.MemberCount</MudText></MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                        <MudSelect T="string" Value="@GetSelectedAiAgentName(context)"
                                                                   ValueChanged="@((string value) => OnAiAgentChanged(context, value))"
                                                                   Dense="true" Variant="Variant.Outlined" Placeholder="选择AI机器人"
                                                                   Class="compact-select" Style="min-width: 120px;">
                                                            <MudSelectItem Value="@("")">
                                                                <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                    <MudIcon Icon="Icons.Material.Filled.Clear" Size="Size.Small" Color="Color.Error" />
                                                                    <MudText>未配置</MudText>
                                                                </MudStack>
                                                            </MudSelectItem>
                                                            @if (aiAgents != null)
                                                            {
                                                                @foreach (var agent in aiAgents.Where(a => a.IsEnabled))
                                                                {
                                                                    <MudSelectItem Value="@agent.Name">
                                                                        <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                            <MudIcon Icon="Icons.Material.Filled.SmartToy" Size="Size.Small" Color="Color.Primary" />
                                                                            <MudText>@agent.Name</MudText>
                                                                        </MudStack>
                                                                    </MudSelectItem>
                                                                }
                                                            }
                                                        </MudSelect>

                                                        @* 配置状态指示器 *@
                                                        @{
                                                            var hasAiAgent = groupAiAgentMapping.ContainsKey(context.Id);
                                                            var agentName = hasAiAgent ? groupAiAgentMapping[context.Id] : "";
                                                            var isAutoReply = context.IsAiEnabled;
                                                        }

                                                        @if (hasAiAgent && isAutoReply)
                                                        {
                                                            <MudTooltip Text="@($"已配置AI机器人: {agentName} (自动回复)")">
                                                                <MudIcon Icon="Icons.Material.Filled.CheckCircle" Size="Size.Small" Color="Color.Success" />
                                                            </MudTooltip>
                                                        }
                                                        else if (hasAiAgent && !isAutoReply)
                                                        {
                                                            <MudTooltip Text="@($"已配置AI机器人: {agentName} (人工回复)")">
                                                                <MudIcon Icon="Icons.Material.Filled.SmartToy" Size="Size.Small" Color="Color.Info" />
                                                            </MudTooltip>
                                                        }
                                                        else if (isAutoReply)
                                                        {
                                                            <MudTooltip Text="已启用自动回复但未配置AI机器人">
                                                                <MudIcon Icon="Icons.Material.Filled.Warning" Size="Size.Small" Color="Color.Warning" />
                                                            </MudTooltip>
                                                        }
                                                        else
                                                        {
                                                            <MudTooltip Text="未配置AI机器人 (人工回复)">
                                                                <MudIcon Icon="Icons.Material.Filled.Person" Size="Size.Small" Color="Color.Default" />
                                                            </MudTooltip>
                                                        }
                                                    </MudStack>
                                                </MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudSelect T="string" Value="@(GetReplyMode(context))"
                                                               ValueChanged="@((string mode) => OnReplyModeChanged(context, mode))"
                                                               Dense="true" Variant="Variant.Outlined"
                                                               Class="compact-select">
                                                        <MudSelectItem Value="@("自动回复")">
                                                            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                <MudIcon Icon="Icons.Material.Filled.SmartToy" Size="Size.Small" Color="Color.Success" />
                                                                <MudText>自动回复</MudText>
                                                            </MudStack>
                                                        </MudSelectItem>
                                                        <MudSelectItem Value="@("人工回复")">
                                                            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                <MudIcon Icon="Icons.Material.Filled.Person" Size="Size.Small" Color="Color.Default" />
                                                                <MudText>人工回复</MudText>
                                                            </MudStack>
                                                        </MudSelectItem>
                                                    </MudSelect>
                                                </MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudSelect T="string" Value="@(GetMentionMode(context))"
                                                               ValueChanged="@((string value) => OnMentionModeChanged(context, value))"
                                                               Dense="true" Variant="Variant.Outlined"
                                                               Class="compact-select">
                                                        <MudSelectItem Value="@("仅@后回复")">
                                                            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                <MudIcon Icon="Icons.Material.Filled.AlternateEmail" Size="Size.Small" Color="Color.Primary" />
                                                                <MudText>仅@后回复</MudText>
                                                            </MudStack>
                                                        </MudSelectItem>
                                                        <MudSelectItem Value="@("回复所有消息")">
                                                            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                <MudIcon Icon="Icons.Material.Filled.ChatBubbleOutline" Size="Size.Small" Color="Color.Success" />
                                                                <MudText>回复所有消息</MudText>
                                                            </MudStack>
                                                        </MudSelectItem>
                                                    </MudSelect>
                                                </MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudStack Row="true" Spacing="1">

                                                        <MudButton Variant="Variant.Text"
                                                                   Color="Color.Error"
                                                                   Size="Size.Small"
                                                                   OnClick="() => DeleteGroup(context)"
                                                                   Disabled="@(_deletingGroupIds.Contains(context.Id) || _isBatchDeleting)">
                                                            @if (_deletingGroupIds.Contains(context.Id))
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                                <span class="ml-1">删除中</span>
                                                            }
                                                            else
                                                            {
                                                                <span>删除</span>
                                                            }
                                                        </MudButton>
                                                        <MudButton Variant="Variant.Text"
                                                                   Color="Color.Info"
                                                                   Size="Size.Small"
                                                                   OnClick="() => SendGroupMessage(context)">
                                                            私信
                                                        </MudButton>
                                                    </MudStack>
                                                </MudTd>
                                            </RowTemplate>
                                        </MudTable>
                                            }
                                        </MudPaper>

                                        <!-- 分页信息和分页控件 -->
                                        <MudPaper Elevation="0" Class="pa-2 border-top">
                                            <MudGrid AlignItems="AlignItems.Center">
                                                <MudItem xs="6">
                                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                                            显示 1 至 @Math.Min(_pageSize, Elements.Count()) 条, 共 @Elements.Count() 条
                                                        </MudText>
                                                        @if (_selectedGroupIds.Count > 0)
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                                                已选中: @_selectedGroupIds.Count
                                                            </MudChip>
                                                        }
                                                        @if (!string.IsNullOrEmpty(_searchedNickName))
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                                                搜索: @_searchedNickName
                                                            </MudChip>
                                                        }
                                                    </MudStack>
                                                </MudItem>
                                                <MudItem xs="6" Class="d-flex justify-end">
                                                    @if (_totalPages > 1)
                                                    {
                                                        <MudPagination Count="@_totalPages"
                                                                       Selected="@_page"
                                                                       SelectedChanged="OnPageChanged"
                                                                       Size="Size.Small" />
                                                    }
                                                </MudItem>
                                            </MudGrid>
                                        </MudPaper>
                                    </ChildContent>
                                </MudTabPanel>
                            }
                        }
                    </MudTabs>
                </MudPaper>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>



<style>
    .border-bottom {
        border-bottom: 1px solid var(--mud-palette-divider);
    }

    .border-top {
        border-top: 1px solid var(--mud-palette-divider);
    }

    .align-center {
        align-items: center;
    }

    /* 紧凑表格样式 - 重点控制行高 */
    .compact-table {
        --mud-table-row-height: 28px !important;
    }

    /* 固定表格布局样式 */
    .fixed-layout-table {
        table-layout: fixed !important;
        width: 100% !important;
    }

    .fixed-layout-table .mud-table {
        table-layout: fixed !important;
        width: 100% !important;
    }

    .compact-table .mud-table-row {
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
    }

    .compact-table .mud-table-cell {
        padding: 3px 8px !important;
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
        vertical-align: middle !important;
        line-height: 1.2 !important;
    }

    .compact-row {
        padding: 2px 6px !important;
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
        vertical-align: middle !important;
        line-height: 1.2 !important;
    }

    .compact-row .mud-typography {
        line-height: 1.2 !important;
        margin: 0 !important;
    }

    .compact-select {
        min-height: 24px !important;
        max-height: 24px !important;
    }

    .compact-select .mud-input {
        min-height: 24px !important;
        max-height: 24px !important;
        padding: 2px 8px !important;
        line-height: 1.2 !important;
    }

    .compact-select .mud-input-control {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    .compact-select .mud-select {
        min-height: 24px !important;
        max-height: 24px !important;
    }

    /* 强制所有表格内容垂直居中且紧凑 */
    .compact-table td {
        padding: 2px 6px !important;
        height: 28px !important;
        vertical-align: middle !important;
    }

    /* 表格标题文字水平居中 */
    .compact-table .mud-table-head .mud-table-cell {
        text-align: center !important;
    }

    .compact-table .mud-avatar {
        margin: 0 !important;
    }

    .compact-table .mud-icon-button {
        margin: 0 !important;
        padding: 2px !important;
    }

    /* 紧凑操作栏样式 */
    .compact-toolbar {
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
        padding: 2px 8px !important;
        display: flex !important;
        align-items: center !important;
    }

    .compact-toolbar-grid {
        height: 24px !important;
        min-height: 24px !important;
    }

    .compact-toolbar .mud-grid {
        height: 24px !important;
        align-items: center !important;
    }

    .compact-toolbar .mud-grid-item {
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
    }

    .compact-toolbar .mud-input {
        height: 24px !important;
        min-height: 24px !important;
    }

    .compact-toolbar .mud-input-control {
        height: 24px !important;
        min-height: 24px !important;
    }

    .compact-toolbar .mud-input-control .mud-input {
        height: 24px !important;
        min-height: 24px !important;
        padding: 2px 8px !important;
        font-size: 12px !important;
    }

    .compact-toolbar .mud-button {
        height: 24px !important;
        min-height: 24px !important;
        padding: 2px 8px !important;
        font-size: 12px !important;
    }

    .compact-toolbar .mud-button-root {
        height: 24px !important;
        min-height: 24px !important;
    }

    /* Tab区域样式优化 */
    .mud-tabs .mud-tab-panel {
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
        padding: 0 !important;
    }

    .mud-tabs .mud-tabs-panels {
        border-top: 1px solid #e0e0e0;
        background-color: #fafafa;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* 确保Tab内容区域完全铺满 */
    .mud-tabs .mud-tabs-panels .mud-tab-panel {
        padding: 0 !important;
        margin: 0 !important;
    }

    /* 操作栏24px高度样式 */
    .operation-toolbar {
        height: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        padding: 0 8px !important;
        display: flex !important;
        align-items: center !important;
        overflow: hidden !important;
    }

    .operation-toolbar .mud-grid {
        height: 30px !important;
        min-height: 30px !important;
        align-items: center !important;
        margin: 0 !important;
    }

    .operation-toolbar .mud-grid-item {
        height: 30px !important;
        min-height: 30px !important;
        display: flex !important;
        align-items: center !important;
        padding: 0 2px !important;
    }

    .operation-toolbar .mud-input-control {
        height: 26px !important;
        min-height: 26px !important;
        margin: 0 !important;
    }

    .operation-toolbar .mud-input {
        height: 26px !important;
        min-height: 26px !important;
        padding: 1px 6px !important;
        font-size: 11px !important;
        line-height: 1.2 !important;
    }

    .operation-toolbar .mud-input-outlined .mud-input-outlined-border {
        border-width: 1px !important;
    }

    .operation-toolbar .mud-input-label {
        font-size: 10px !important;
        line-height: 1.2 !important;
    }

    .operation-toolbar .mud-button {
        height: 26px !important;
        min-height: 26px !important;
        padding: 1px 6px !important;
        font-size: 11px !important;
        line-height: 1.2 !important;
    }

    .operation-toolbar .mud-button-root {
        height: 26px !important;
        min-height: 26px !important;
    }

    .operation-toolbar .mud-icon {
        font-size: 14px !important;
    }

    .operation-toolbar .mud-progress-circular {
        width: 20px !important;
        height: 20px !important;
    }

    /* Tab按钮内容40px高度样式 */
    .tab-content-40px {
        height: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        padding: 4px 8px !important;
        display: flex !important;
        align-items: center !important;
        overflow: hidden !important;
    }

    .tab-content-40px .mud-avatar {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
        min-height: 32px !important;
    }

    .tab-content-40px .mud-avatar .mud-avatar-img,
    .tab-content-40px .mud-avatar .mud-icon {
        width: 32px !important;
        height: 32px !important;
        font-size: 20px !important;
    }

    .tab-content-40px .mud-stack {
        height: 32px !important;
        min-height: 32px !important;
        justify-content: center !important;
    }

    .tab-content-40px .mud-text {
        font-size: 12px !important;
        line-height: 1.2 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .tab-content-40px .mud-chip {
        height: 16px !important;
        min-height: 16px !important;
        font-size: 10px !important;
        padding: 0 4px !important;
        margin: 1px 0 !important;
    }

    .tab-content-40px .mud-chip .mud-chip-content {
        padding: 0 !important;
        line-height: 1.2 !important;
    }

    /* MudTabPanel 圆角样式 */
    .mud-tabs {
        border-radius: 12px !important;
        overflow: hidden !important;
    }

    .mud-tabs .mud-tabs-toolbar {
        border-radius: 12px 12px 0 0 !important;
    }

    .mud-tabs .mud-tabs-panels {
        border-radius: 0 0 12px 12px !important;
    }

    .mud-tabs .mud-tab-panel {
        border-radius: 0 0 12px 12px !important;
    }

    /* 确保卡片容器也有圆角 */
    .mud-card {
        border-radius: 12px !important;
        overflow: hidden !important;
    }

    /* 特定组件不使用圆角 */
    .operation-toolbar {
        border-radius: 0 !important;
    }

    .mud-paper.pa-0 {
        border-radius: 0 !important;
    }

    .mud-paper.border-top {
        border-radius: 0 !important;
    }

    /* MudItem 内的组件不使用圆角 */
    .d-flex.justify-end.align-center .mud-paper {
        border-radius: 0 !important;
    }

    /* 按钮文字水平垂直居中 */
    .mud-button {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
    }

    .mud-button .mud-button-label {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
    }

    .mud-button span {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* 搜索状态样式 */
    .searching {
        position: relative;
    }

    .searching::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 8px;
        width: 12px;
        height: 12px;
        border: 2px solid #1976d2;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 0.8s linear infinite;
        transform: translateY(-50%);
    }

    /* 旋转动画 */
    @@keyframes spin {
        to {
            transform: translateY(-50%) rotate(360deg);
        }
    }

    /* 复选框样式 */
    .compact-table .mud-checkbox {
        margin: 0 !important;
        padding: 0 !important;
    }

    .compact-table .mud-checkbox .mud-button-root {
        min-height: 20px !important;
        min-width: 20px !important;
        padding: 0 !important;
    }

    .compact-table .mud-checkbox .mud-checkbox-icons {
        font-size: 16px !important;
    }
</style>


@code {
    private bool _hover = true;
    private HappyWechat.Web.VOs.WxGroupVo? selectedItem;
    private string _searchedNickName = "";
    private IEnumerable<HappyWechat.Web.VOs.WxGroupVo> Elements = new List<HappyWechat.Web.VOs.WxGroupVo>();
    private int _page = 1;
    private int _pageSize = 10;
    private int _totalPages = 0;
    // 移除重复的字段声明，已在BaseWxPageComponent中定义

    // 账户状态属性
    private bool _hasWeChatAccounts => wxAccounts?.Any() == true;
    private bool _hasLoggedInAccounts => wxAccounts?.Any(u => u.WxStatus == WxStatus.AlreadyLogIn) == true;
    private bool _hasEnabledAccounts => wxAccounts?.Any(u => u.WxStatus == WxStatus.AlreadyLogIn && u.IsEnabled) == true;

    // 搜索相关字段
    private bool _isSearching = false;
    private string _lastSearchValue = "";
    private const string SEARCH_KEY = "group_search";

    /// <summary>
    /// 搜索输入事件 - 防抖动处理
    /// </summary>
    private async Task OnSearchInput(ChangeEventArgs e)
    {
        var inputValue = e.Value?.ToString() ?? "";
        _searchedNickName = inputValue;

        // 设置搜索状态
        SearchStateService.SetSearching(SEARCH_KEY, true);

        // 如果输入为空，立即显示所有结果
        if (string.IsNullOrWhiteSpace(inputValue))
        {
            _lastSearchValue = inputValue;
            await SearchDebounceService.DebounceAsync(SEARCH_KEY, async () =>
            {
                SearchStateService.SetSearching(SEARCH_KEY, false);
                await ExecuteSearchImmediately();
                await InvokeAsync(StateHasChanged);
            }, 100); // 空搜索延迟短一些
            return;
        }

        // 防抖动搜索
        await SearchDebounceService.DebounceAsync(SEARCH_KEY, async () =>
        {
            // 验证搜索值是否仍然有效
            if (_searchedNickName == inputValue && _lastSearchValue != inputValue)
            {
                _lastSearchValue = inputValue;
                SearchStateService.SetSearching(SEARCH_KEY, false);
                await ExecuteSearchImmediately();
                await InvokeAsync(StateHasChanged);
            }
        }, 500); // 500ms 防抖动延迟
    }

    // 手动添加群组相关字段
    private string manualGroupId = "";
    private bool _isAddingGroup = false;

    // 多选相关字段
    private HashSet<Guid> _selectedGroupIds = new();
    private bool _isAllSelectedValue = false;
    private bool _isAllSelected
    {
        get => _isAllSelectedValue;
        set
        {
            if (_isAllSelectedValue != value)
            {
                _isAllSelectedValue = value;
                OnSelectAllChanged(value);
            }
        }
    }
    private bool _isIndeterminate = false;

    // 删除操作相关字段
    private HashSet<Guid> _deletingGroupIds = new();
    private bool _isBatchDeleting = false;

    // 群组同步进度
    private GroupSyncProgressDto? syncProgress;
    private bool _isLoadingGroups = false; // 群组获取状态

    // 群组AI机器人相关字段
    private Dictionary<Guid, string> groupAiAgentMapping = new(); // 群组ID -> AI机器人名称
    private bool _isLoadingAiConfig = false; // AI配置加载状态
    private Dictionary<Guid, PageResponse<HappyWechat.Web.VOs.WxGroupVo>> _tabDataCache = new(); // Tab数据缓存

    // SignalR相关字段 - 使用新的DotNetObjectReference类型
    private DotNetObjectReference<WxGroup>? _dotNetRefGroup;

    // 🔧 添加方法执行锁，防止重复调用
    private readonly Dictionary<string, bool> _methodExecutionLocks = new();

    /// <summary>
    /// 在DOM完全渲染后设置组件引用，确保JavaScript环境就绪（参照联系人页面）
    /// </summary>
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            // 初始化SignalR连接
            await InitializeSignalRAsync();

            // 注册群组特定的SignalR回调
            await RegisterPageSpecificCallbacks();
        }
    }

    /// <summary>
    /// 初始化SignalR连接（参照联系人页面）
    /// </summary>
    private async Task InitializeSignalRAsync()
    {
        try
        {
            // 🔧 注释冗余的SignalR连接日志 - 降低日志噪音
            // Logger.LogInformation("🚀 开始初始化群组页面SignalR连接...");

            // 使用群组专用初始化方法
            await JSRuntime.InvokeVoidAsync("initializeGroupSignalR");

            // 🔧 注释冗余的SignalR连接日志 - 降低日志噪音
            // Logger.LogInformation("✅ 群组页面SignalR连接初始化完成");

            // 如果有选中的账户，立即加入组
            if (HasSelectedAccount)
            {
                // 🔧 注释冗余的SignalR连接日志 - 降低日志噪音
                // Logger.LogInformation("🔗 尝试加入微信管理器组 - WxManagerId: {WxManagerId}", SelectedAccount!.Id);
                await JSRuntime.InvokeVoidAsync("joinWxManagerGroup", SelectedAccount.Id.ToString());
                // 🔧 注释冗余的SignalR连接日志 - 降低日志噪音
                // Logger.LogInformation("✅ 已加入微信管理器组");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组页面SignalR连接初始化失败");
        }
    }

    protected override async Task LoadInitialDataAsync()
    {
        // 🔧 注释冗余的页面初始化日志 - 减少日志噪音
        // Logger.LogInformation("🚀 群组页面初始数据加载开始");

        try
        {
            // 检查是否需要强制刷新
            if (HasSelectedAccount && await DataSyncManager.ShouldRefreshUI(SelectedAccount!.Id))
            {
                Logger.LogInformation("🔄 检测到数据变更，清除缓存后加载");
                await DataSyncManager.MarkUIRefreshed(SelectedAccount!.Id);
                // 清除本地缓存
                _tabDataCache.Clear();
            }

            // 立即显示基础数据，AI配置异步加载
            await OnSearchCore();
            // 🔧 注释冗余的页面初始化完成日志 - 减少日志噪音
            // Logger.LogInformation("✅ 群组页面初始数据加载完成");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组页面初始数据加载失败");
            throw;
        }
    }

    protected override async Task LoadCurrentPageDataAsync()
    {
        // 🔧 注释冗余的页面数据加载日志 - 减少日志噪音
        // Logger.LogInformation("🔄 群组页面当前数据加载开始 - TabIndex: {TabIndex}", _activeTabIndex);

        try
        {
            // 🔧 无缓存架构：始终从数据库加载最新数据
            if (HasSelectedAccount)
            {
                // 🔧 注释冗余的无缓存架构日志 - 减少日志噪音
                // Logger.LogInformation("🔄 无缓存架构，直接从数据库加载");
                await DataSyncManager.MarkUIRefreshed(SelectedAccount!.Id);
                await OnSearchCore(useCache: false);
            }

            // 🔧 注释冗余的页面数据加载完成日志 - 减少日志噪音
            // Logger.LogInformation("✅ 群组页面当前数据加载完成");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组页面当前数据加载失败");
            throw;
        }
    }



    // 对外的搜索接口，保持兼容性
    private async Task OnSearch()
    {
        await OnSearchCore();
    }

    // 核心搜索逻辑，无缓存架构
    private async Task OnSearchCore(bool useCache = true)
    {
        if (!_hasEnabledAccounts)
        {
            Logger.LogDebug("⚠️ 没有可用的微信账户，跳过群组数据加载");
            return;
        }

        // 检查是否有选中的微信账户
        if (!HasSelectedAccount)
        {
            Logger.LogDebug("⚠️ 没有选中的微信账户，跳过群组数据加载");
            return;
        }

        // 🔧 注释冗余的群组数据加载开始日志 - 减少日志噪音，每次操作都会重复出现
        // Logger.LogInformation("🔍 开始群组数据加载 - Account: {AccountName}, 无缓存架构", SelectedAccount!.NickName);
        _isLoading = true;

        // 立即更新UI显示加载状态
        await InvokeAsync(StateHasChanged);
        try
        {
            // 标准化搜索参数
            var searchNickName = string.IsNullOrWhiteSpace(_searchedNickName) ? "" : _searchedNickName.Trim();

            // 如果是搜索操作，重置到第一页
            if (!string.IsNullOrEmpty(searchNickName) && _page != 1)
            {
                _page = 1;
            }

            // 🔧 无缓存架构：始终从数据库获取最新数据
            PageResponse<HappyWechat.Web.VOs.WxGroupVo>? resp = null;

            Logger.LogDebug("🌐 直接从数据库获取群组数据");
            // 使用统一的数据管理器获取数据
            var query = new GetGroupListQuery
            {
                WxManagerId = SelectedAccount.Id,
                SearchedNickName = searchNickName ?? "",
                SearchedOwner = "",
                PageQuery = new PageQuery { Page = _page, PageSize = _pageSize }
            };

            // 🔧 完全无缓存架构：直接调用底层API，绕过所有缓存层
            Logger.LogDebug("🔄 直接调用WxApi.getGroupList，完全绕过缓存");

            // 直接调用底层API获取群组数据
            resp = await WxApi.getGroupList(query);

            if (resp != null && resp.Items != null)
            {
                // 🔧 注释冗余的API调用完成日志 - 减少日志噪音，每次操作都会重复出现
                // Logger.LogInformation("✅ 直接API调用完成 - 群组数量: {Count}, 来源: 数据库", resp.Items.Count());
            }

            // 直接使用返回的群组VO对象
            Elements = resp?.Items ?? new List<HappyWechat.Web.VOs.WxGroupVo>();

            _totalPages = resp != null ? (int)Math.Ceiling((double)resp.TotalCount / resp.PageSize) : 0;

            // 🔧 新增：验证数据映射完整性
            if (Elements.Any())
            {
                var sampleGroup = Elements.First();
                Logger.LogDebug("✅ 数据映射验证 - 示例群组: {GroupName}, IsAiEnabled: {IsAiEnabled}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, AiAgentName: '{AiAgentName}'",
                    sampleGroup.NickName, sampleGroup.IsAiEnabled, sampleGroup.OnlyReplyWhenMentioned, sampleGroup.AiAgentName);
            }

            // 🔧 注释冗余的群组数据加载完成日志 - 减少日志噪音
            // Logger.LogInformation("📊 群组数据加载完成 - Elements: {Count}, TotalPages: {TotalPages}", Elements.Count(), _totalPages);

            // 更新UI显示数据
            await InvokeAsync(StateHasChanged);

            // 异步加载群组的AI配置（不阻塞主界面）
            await LoadGroupsAiConfigAsync();

            // 更新选择状态
            UpdateSelectAllState();

            // 🔧 确保重置按钮加载状态（数据加载完成后）
            if (_isLoadingGroups)
            {
                _isLoadingGroups = false;
                Logger.LogInformation("🔄 数据加载完成，重置群组同步按钮状态");
            }

            // 最终更新UI
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组数据加载失败 - Account: {AccountName}", SelectedAccount?.NickName ?? "Unknown");

            if (ex.Message.Contains("微信未登录") || ex.Message.Contains("微信已注销") || ex.Message.Contains("会话已过期"))
            {
                // 重新加载微信账户状态
                await LoadWxAccountsAsync();
                await InvokeAsync(StateHasChanged);

                // 显示详细的错误信息
                await DialogService.ShowMessageBox("微信登录状态异常",
                    $"错误详情: {ex.Message}\\n\\n请检查:\\n1. 微信是否正常登录\\n2. 用户身份是否有效\\n3. 数据库连接是否正常");
                return;
            }

            // 显示详细的错误信息用于调试
            var errorDetails = $"错误类型: {ex.GetType().Name}\\n错误消息: {ex.Message}";
            if (ex.InnerException != null)
            {
                errorDetails += $"\\n内部错误: {ex.InnerException.Message}";
            }

            await DialogService.ShowMessageBox("搜索群组失败", errorDetails);
        }
        finally
        {
            _isLoading = false;

            // 确保加载状态更新立即反映到UI
            await InvokeAsync(StateHasChanged);

            Logger.LogDebug("🏁 群组数据加载流程结束 - isLoading: {IsLoading}", _isLoading);
        }
    }

    // 异步加载群组的AI配置（不阻塞主界面）
    private async Task LoadGroupsAiConfigAsync()
    {
        if (Elements == null || !Elements.Any())
            return;

        // 显示加载指示器
        _isLoadingAiConfig = true;
        StateHasChanged();

        // 使用后台任务异步加载AI配置
        _ = Task.Run(async () =>
        {
            try
            {
                var groupIds = Elements.Select(g => g.Id).ToList();

                // 🔧 无缓存架构：直接从数据库获取最新AI配置数据
                var aiConfigs = await WxApi.GetGroupsAiConfig(groupIds);

                // 批量更新UI
                await InvokeAsync(() =>
                {
                    UpdateAiConfigMapping(aiConfigs);
                    _isLoadingAiConfig = false;
                    StateHasChanged();
                });
            }
            catch (Exception ex)
            {
                // 静默处理AI配置加载失败，不影响主功能
                await InvokeAsync(() =>
                {
                    _isLoadingAiConfig = false;
                    StateHasChanged();
                });
            }
        });
    }

    // 更新AI配置映射的辅助方法
    private void UpdateAiConfigMapping(List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto> aiConfigs)
    {
        // 清空现有映射
        groupAiAgentMapping.Clear();

        // 更新AI配置映射
        foreach (var config in aiConfigs)
        {
            var group = Elements.FirstOrDefault(g => g.Id == config.GroupId);
            if (group != null)
            {
                if (!string.IsNullOrEmpty(config.AiAgentName))
                {
                    groupAiAgentMapping[group.Id] = config.AiAgentName;
                }
                group.IsAiEnabled = config.IsEnabled;

                group.OnlyReplyWhenMentioned = config.OnlyReplyWhenMentioned;
                group.AiAgentName = config.AiAgentName;
            }
        }
    }

    // 获取群组选中的AI机器人名称
    private string GetSelectedAiAgentName(HappyWechat.Web.VOs.WxGroupVo group)
    {
        return groupAiAgentMapping.TryGetValue(group.Id, out var agentName) ? agentName : "";
    }

    // 获取群组回复模式
    private string GetReplyMode(HappyWechat.Web.VOs.WxGroupVo group)
    {
        return group.IsAiEnabled ? "自动回复" : "人工回复";
    }

    // 处理回复模式变更（统一与联系人页面的逻辑）
    private async Task OnReplyModeChanged(HappyWechat.Web.VOs.WxGroupVo group, string mode)
    {
        try
        {
            var enabled = mode == "自动回复";

            // 友好提示：如果设置为自动回复但没有配置AI机器人
            if (enabled && !groupAiAgentMapping.ContainsKey(group.Id))
            {
                Snackbar.Add($"提示：群组 {group.NickName} 未配置AI机器人，建议先配置AI机器人以获得更好的自动回复效果", Severity.Info);
            }

            // 独立设置自动回复状态，不影响其他配置项
            await OnAutoReplyToggle(group, enabled);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"设置回复模式失败: {ex.Message}", Severity.Error);
            // 强制刷新数据恢复正确状态
            await ReloadGroupDataFromDatabase();
        }
    }

    // 带重试机制的群组AI配置加载方法
    private async Task LoadGroupsAiConfigWithRetryAsync()
    {
        if (Elements == null || !Elements.Any())
            return;

        const int maxRetries = 2;
        const int retryDelayMs = 200;

        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                var groupIds = Elements.Select(g => g.Id).ToList();
                Logger.LogDebug("🔄 开始加载群组AI配置（尝试 {Attempt}/{MaxRetries}） - 群组数量: {Count}",
                    attempt + 1, maxRetries + 1, groupIds.Count);

                var aiConfigs = await WxApi.GetGroupsAiConfig(groupIds);

                // 同步更新UI状态
                await InvokeAsync(() =>
                {
                    UpdateGroupAiConfigMapping(aiConfigs);
                    Logger.LogDebug("✅ 群组AI配置加载完成（尝试 {Attempt}） - 配置数量: {Count}",
                        attempt + 1, aiConfigs?.Count ?? 0);
                });

                // 成功加载，退出重试循环
                break;
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "⚠️ 群组AI配置加载失败（尝试 {Attempt}/{MaxRetries}）",
                    attempt + 1, maxRetries + 1);

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries)
                {
                    await Task.Delay(retryDelayMs);
                }
            }
        }
    }

    // 更新群组AI配置映射
    private void UpdateGroupAiConfigMapping(List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>? aiConfigs)
    {
        if (aiConfigs == null || Elements == null) return;

        // 清空现有映射
        groupAiAgentMapping.Clear();

        // 重建映射和更新群组状态
        foreach (var config in aiConfigs)
        {
            var group = Elements.FirstOrDefault(g => g.Id == config.Id);
            if (group != null)
            {
                // 🔧 修复：无条件更新所有群组的基础配置，确保UI与数据库一致
                group.IsAiEnabled = config.IsEnabled;
                group.OnlyReplyWhenMentioned = config.OnlyReplyWhenMentioned;
                group.AiAgentName = config.AiAgentName;

                // 只有当AI代理名称不为空时才添加到映射中
                if (!string.IsNullOrEmpty(config.AiAgentName))
                {
                    groupAiAgentMapping[group.Id] = config.AiAgentName;
                }
            }
        }

        StateHasChanged();
    }

    // AI机器人选择变更事件
    private async Task OnAiAgentChanged(HappyWechat.Web.VOs.WxGroupVo group, string agentName)
    {
        // 🔧 强化防重复调用检查 - 使用TryGetValue避免竞态条件
        var lockKey = $"OnAiAgentChanged_{group.Id}";
        if (_methodExecutionLocks.TryGetValue(lockKey, out var isLocked) && isLocked)
        {
            Logger.LogDebug("⏭️ 跳过重复的AI代理配置更新调用 - GroupId: {GroupId}", group.Id);
            return;
        }

        _methodExecutionLocks[lockKey] = true;
        // 🔧 注释冗余的AI代理配置更新开始日志 - 减少日志噪音
        // Logger.LogInformation("🔄 开始AI代理配置更新 - GroupId: {GroupId}, AgentName: {AgentName}", group.Id, agentName);
        string? trackingId = null;

        try
        {
            // 🔧 注释冗余的群组AI配置更新开始日志 - 减少日志噪音
            // Logger.LogInformation("🔄 开始群组AI配置更新 - Group: {GroupName}, Agent: {AgentName}", group.NickName, agentName);

            // 开始配置生效跟踪
            var effectivenessTracker = ConfigurationEffectivenessTracker;
            if (effectivenessTracker != null)
            {
                trackingId = effectivenessTracker.StartTracking(
                    "GroupAiConfig",
                    $"group_{group.Id}",
                    agentName,
                    new List<string> { SelectedAccount?.WcId ?? "" });

                await effectivenessTracker.UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.Started, "开始群组AI配置更新");
            }

            // 创建设置命令 - 保持当前状态，不自动修改其他配置
            var command = new SetGroupAiAgentCommand
            {
                GroupId = group.Id,
                WxManagerId = group.WxManagerId,
                AiAgentName = agentName,
                IsEnabled = group.IsAiEnabled, // ✅ 保持当前自动回复状态，独立于AI代理设置

                OnlyReplyWhenMentioned = group.OnlyReplyWhenMentioned, // ✅ 保持当前@回复设置，独立于其他配置
                ReplyDelaySeconds = 5,
                ClearAiAgent = string.IsNullOrEmpty(agentName) // 🔴 修复：当选择"未配置"时，明确清除AI代理
            };

            // 更新跟踪状态
            if (effectivenessTracker != null && trackingId != null)
            {
                await effectivenessTracker.UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.DatabaseUpdated, "开始API调用");
            }

            // 调用API保存群组与AI机器人的绑定关系
            var result = await WxApi.SetGroupAiAgent(command);

            if (result)
            {
                // 🔧 无缓存架构：直接重新加载数据库数据，确保UI显示真实状态
                // 🔧 注释冗余的AI代理配置更新成功日志 - 减少日志噪音
                // Logger.LogInformation("🔄 AI代理配置更新成功，重新加载数据库数据 - GroupId: {GroupId}", group.Id);

                await ReloadGroupDataFromDatabase();

                // 🔧 修复：使用统一配置服务实现实时配置刷新
                try
                {
                    if (UnifiedConfigService != null)
                    {
                        await UnifiedConfigService.InvalidateConfigCacheAsync($"group_ai_{group.WxManagerId}_{group.ChatRoomId}");
                        Logger.LogDebug("✅ AI配置缓存已强制刷新 - Group: {GroupId}, ChatRoomId: {ChatRoomId}", group.Id, group.ChatRoomId);
                    }
                    else
                    {
                        Logger.LogDebug("✅ AI配置已更新，将在3秒内自动生效 - Group: {GroupId}, ChatRoomId: {ChatRoomId}", group.Id, group.ChatRoomId);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "强制刷新AI配置缓存失败，将在3秒内自动生效 - Group: {GroupId}, ChatRoomId: {ChatRoomId}", group.Id, group.ChatRoomId);
                }

                // 🔧 注释冗余的AI代理配置更新完成日志 - 减少日志噪音
                // Logger.LogInformation("✅ AI代理配置更新完成 - GroupId: {GroupId}, AgentName: {AgentName}",
                //     group.Id, agentName);

                // 完成跟踪
                if (effectivenessTracker != null && trackingId != null)
                {
                    await effectivenessTracker.CompleteTrackingAsync(trackingId, true, "群组AI代理配置成功");
                }

                Snackbar.Add($"AI机器人配置{(string.IsNullOrEmpty(agentName) ? "移除" : "设置")}成功", Severity.Success);

                // 🔧 注释冗余的群组AI机器人配置完成日志 - 减少日志噪音
                // Logger.LogInformation("✅ 群组AI机器人配置完成 - GroupId: {GroupId}, AiAgentName: {AiAgentName}",
                //     group.Id, agentName);
            }
            else
            {
                Snackbar.Add("AI机器人配置失败", Severity.Error);
                Logger.LogWarning("⚠️ 群组AI机器人配置API调用失败 - GroupId: {GroupId}", group.Id);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组AI配置更新失败 - Group: {GroupName}", group.NickName);
            Snackbar.Add($"AI机器人配置失败: {ex.Message}", Severity.Error);

            // 完成跟踪（失败）
            var effectivenessTracker = ConfigurationEffectivenessTracker;
            if (effectivenessTracker != null && trackingId != null)
            {
                await effectivenessTracker.CompleteTrackingAsync(trackingId, false, ex.Message);
            }

            // 🔧 错误恢复：重新加载数据库数据，确保UI状态正确
            await ReloadGroupDataFromDatabase();
        }
        finally
        {
            // 🔧 释放执行锁
            _methodExecutionLocks[lockKey] = false;
        }
    }



    private string GetMentionMode(HappyWechat.Web.VOs.WxGroupVo group)
    {
        return group.OnlyReplyWhenMentioned ? "仅@后回复" : "回复所有消息";
    }



    private async Task OnMentionModeChanged(HappyWechat.Web.VOs.WxGroupVo group, string mentionMode)
    {
        // 🔧 强化防重复调用检查 - 使用TryGetValue避免竞态条件
        var lockKey = $"OnMentionModeChanged_{group.Id}";
        if (_methodExecutionLocks.TryGetValue(lockKey, out var isLocked) && isLocked)
        {
            Logger.LogDebug("⏭️ 跳过重复的@回复模式配置更新调用 - GroupId: {GroupId}", group.Id);
            return;
        }

        _methodExecutionLocks[lockKey] = true;
        Logger.LogInformation("🔄 开始@回复模式配置更新 - GroupId: {GroupId}, MentionMode: {MentionMode}", group.Id, mentionMode);
        string? trackingId = null;

        try
        {
            var onlyReplyWhenMentioned = mentionMode == "仅@后回复";

            Logger.LogInformation("🔄 开始群组@回复模式配置更新 - Group: {GroupName}, Mode: {Mode}", group.NickName, mentionMode);

            // 开始配置生效跟踪
            var effectivenessTracker = ConfigurationEffectivenessTracker;
            if (effectivenessTracker != null)
            {
                trackingId = effectivenessTracker.StartTracking(
                    "GroupMentionMode",
                    $"group_{group.Id}",
                    onlyReplyWhenMentioned,
                    new List<string> { SelectedAccount?.WcId ?? "" });

                await effectivenessTracker.UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.Started, "开始群组@回复模式配置更新");
            }

            // 独立更新@回复设置，不依赖AI代理配置
            var aiAgentName = groupAiAgentMapping.ContainsKey(group.Id) ? groupAiAgentMapping[group.Id] : null;
            var command = new SetGroupAiAgentCommand
            {
                GroupId = group.Id,
                WxManagerId = group.WxManagerId,
                AiAgentName = aiAgentName, // 保持当前AI代理设置
                IsEnabled = group.IsAiEnabled, // 保持当前自动回复状态

                OnlyReplyWhenMentioned = onlyReplyWhenMentioned,
                ReplyDelaySeconds = 5
            };

            await WxApi.SetGroupAiAgent(command);

            // 🔧 无缓存架构：直接重新加载数据库数据，确保UI显示真实状态
            Logger.LogInformation("🔄 @回复模式配置更新成功，重新加载数据库数据 - GroupId: {GroupId}", group.Id);

            await ReloadGroupDataFromDatabase();

            Logger.LogInformation("✅ @回复模式更新完成 - GroupId: {GroupId}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}",
                group.Id, onlyReplyWhenMentioned);

            Snackbar.Add($"@回复模式已设置为: {mentionMode}", Severity.Success);

            Logger.LogInformation("✅ 群组@回复模式设置完成 - GroupId: {GroupId}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}",
                group.Id, onlyReplyWhenMentioned);

            // 完成配置生效跟踪
            if (effectivenessTracker != null && trackingId != null)
            {
                await effectivenessTracker.CompleteTrackingAsync(trackingId, true, "群组@回复模式配置成功");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 设置群组@回复模式失败 - GroupId: {GroupId}", group.Id);
            Snackbar.Add($"设置@回复模式失败: {ex.Message}", Severity.Error);

            // 完成配置生效跟踪（失败）
            if (ConfigurationEffectivenessTracker != null && trackingId != null)
            {
                await ConfigurationEffectivenessTracker.CompleteTrackingAsync(trackingId, false, ex.Message);
            }

            // 🔧 错误恢复：重新加载数据库数据，确保UI状态正确
            await ReloadGroupDataFromDatabase();
        }
        finally
        {
            // 🔧 释放执行锁
            _methodExecutionLocks[lockKey] = false;
        }
    }

    private async Task OnAutoReplyToggle(HappyWechat.Web.VOs.WxGroupVo group, bool enabled)
    {
        // 🔧 强化防重复调用检查 - 使用TryGetValue避免竞态条件
        var lockKey = $"OnAutoReplyToggle_{group.Id}";
        if (_methodExecutionLocks.TryGetValue(lockKey, out var isLocked) && isLocked)
        {
            Logger.LogDebug("⏭️ 跳过重复的自动回复状态配置更新调用 - GroupId: {GroupId}", group.Id);
            return;
        }

        _methodExecutionLocks[lockKey] = true;

        try
        {
            Logger.LogInformation("🔄 开始自动回复状态配置更新 - GroupId: {GroupId}, Enabled: {Enabled}", group.Id, enabled);

            // 独立更新自动回复状态，保持其他配置不变
            var aiAgentName = groupAiAgentMapping.ContainsKey(group.Id) ? groupAiAgentMapping[group.Id] : null;

            // 构建完整的AI配置命令 - 仅更新自动回复相关设置
            var command = new SetGroupAiAgentCommand
            {
                GroupId = group.Id,
                WxManagerId = group.WxManagerId,
                AiAgentName = aiAgentName, // ✅ 保持当前AI代理设置，独立于自动回复配置
                IsEnabled = enabled,

                OnlyReplyWhenMentioned = group.OnlyReplyWhenMentioned, // ✅ 保持当前@回复设置，独立于自动回复配置
                ReplyDelaySeconds = 5
            };

            // 统一使用SetGroupAiAgent API管理状态
            var result = await WxApi.SetGroupAiAgent(command);

            if (result)
            {
                // 🔧 无缓存架构：直接重新加载数据库数据，确保UI显示真实状态
                Logger.LogInformation("🔄 自动回复状态配置更新成功，重新加载数据库数据 - GroupId: {GroupId}", group.Id);

                await ReloadGroupDataFromDatabase();

                Logger.LogInformation("✅ 自动回复状态更新完成 - GroupId: {GroupId}, IsAiEnabled: {IsAiEnabled}",
                    group.Id, enabled);

                Snackbar.Add($"自动回复已{(enabled ? "开启" : "关闭")}", Severity.Success);
            }
            else
            {
                Snackbar.Add("设置自动回复状态失败", Severity.Error);
                Logger.LogWarning("⚠️ 群组自动回复状态API调用失败 - GroupId: {GroupId}", group.Id);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 设置群组自动回复状态失败 - GroupId: {GroupId}", group.Id);
            Snackbar.Add($"设置自动回复状态失败: {ex.Message}", Severity.Error);

            // 🔧 错误恢复：重新加载数据库数据，确保UI状态正确
            await ReloadGroupDataFromDatabase();
        }
        finally
        {
            // 🔧 释放执行锁
            _methodExecutionLocks[lockKey] = false;
        }
    }



    /// <summary>
    /// 完全无缓存架构：配置变更后直接从数据库重新加载数据
    /// </summary>
    private async Task ReloadGroupDataFromDatabase()
    {
        try
        {
            if (!HasSelectedAccount) return;

            Logger.LogInformation("🔄 直接从数据库重新加载群组数据和AI配置");

            // 🔧 完全无缓存架构：直接调用底层API获取最新数据，包含AI配置
            await OnSearchCore(useCache: false);

            // 🔧 确保重置按钮加载状态
            if (_isLoadingGroups)
            {
                _isLoadingGroups = false;
                await InvokeAsync(StateHasChanged);
                Logger.LogInformation("🔄 重置群组同步按钮状态");
            }

            // 🔧 注释冗余的完全刷新完成日志 - 减少日志噪音
            // Logger.LogInformation("✅ 完全刷新群组数据和AI配置完成 - 群组数量: {Count}", Elements.Count());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 完全刷新群组数据失败");

            // 🔧 即使失败也要重置按钮状态
            if (_isLoadingGroups)
            {
                _isLoadingGroups = false;
                await InvokeAsync(StateHasChanged);
            }
        }
    }



    /// <summary>
    /// 🔍 数据库状态验证 - 直接查询数据库验证数据一致性
    /// 注意：此方法应谨慎使用，避免在数据更新后立即调用导致时序问题
    /// </summary>
    private async Task ValidateDatabaseStateAsync(bool skipIfRecentUpdate = true)
    {
        try
        {
            if (!HasSelectedAccount) return;

            // 🔧 新增：如果最近有数据更新，跳过验证避免时序问题
            if (skipIfRecentUpdate)
            {
                Logger.LogDebug("🔍 跳过数据库状态验证 - 避免时序问题");
                return;
            }

            Logger.LogInformation("🔍 开始数据库状态验证 - WxManagerId: {WxManagerId}", SelectedAccount.Id);

            // 直接从数据库获取群组数据进行对比
            var dbGroups = await UnifiedDataManager.GetGroupDataBundleWithNotificationAsync(SelectedAccount.Id, forceRefresh: true);

            if (dbGroups?.Data == null)
            {
                Logger.LogWarning("⚠️ 无法从数据库获取群组数据");
                return;
            }

            var inconsistencies = new List<string>();

            foreach (var uiGroup in Elements)
            {
                var dbGroup = dbGroups.Data.FirstOrDefault(g => g.Id == uiGroup.Id);
                if (dbGroup == null)
                {
                    inconsistencies.Add($"群组 {uiGroup.NickName} (ID: {uiGroup.Id}) - 数据库中不存在");
                    continue;
                }

                // 检查IsAiEnabled字段一致性
                if (uiGroup.IsAiEnabled != dbGroup.IsAiEnabled)
                {
                    inconsistencies.Add($"群组 {uiGroup.NickName} - IsAiEnabled不一致 (UI: {uiGroup.IsAiEnabled}, DB: {dbGroup.IsAiEnabled})");
                }

                // 检查OnlyReplyWhenMentioned字段一致性
                if (uiGroup.OnlyReplyWhenMentioned != dbGroup.OnlyReplyWhenMentioned)
                {
                    inconsistencies.Add($"群组 {uiGroup.NickName} - OnlyReplyWhenMentioned不一致 (UI: {uiGroup.OnlyReplyWhenMentioned}, DB: {dbGroup.OnlyReplyWhenMentioned})");
                }

                // 检查AiAgentName字段一致性（通过映射获取）
                var uiAgentName = groupAiAgentMapping.ContainsKey(uiGroup.Id) ? groupAiAgentMapping[uiGroup.Id] : null;
                var dbAgentName = groupAiAgentMapping.ContainsKey(dbGroup.Id) ? groupAiAgentMapping[dbGroup.Id] : null;
                if (uiAgentName != dbAgentName)
                {
                    inconsistencies.Add($"群组 {uiGroup.NickName} - AiAgentName不一致 (UI: '{uiAgentName}', DB: '{dbAgentName}')");
                }
            }

            if (inconsistencies.Any())
            {
                Logger.LogError("❌ 发现 {Count} 个UI与数据库不一致的问题:\n{Issues}",
                    inconsistencies.Count, string.Join("\n", inconsistencies));

                // 向用户显示关键不一致问题
                foreach (var issue in inconsistencies.Take(3))
                {
                    Snackbar.Add($"数据不一致: {issue}", Severity.Error);
                }

                // 如果发现不一致，建议强制刷新
                Snackbar.Add("检测到数据不一致，建议刷新页面", Severity.Warning);
            }
            else
            {
                Logger.LogInformation("✅ UI与数据库状态一致性验证通过");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 数据库状态验证失败");
        }
    }

    /// <summary>
    /// 数据完整性检查 - 验证UI状态与数据库状态的一致性
    /// </summary>
    private async Task ValidateDataIntegrityAsync()
    {
        try
        {
            Logger.LogDebug("🔍 开始数据完整性检查");

            var inconsistentGroups = new List<string>();

            foreach (var group in Elements)
            {
                // 检查AI代理映射一致性
                var hasMappedAgent = groupAiAgentMapping.ContainsKey(group.Id);

                // 检查AI配置的逻辑一致性
                if (group.IsAiEnabled && !hasMappedAgent)
                {
                    inconsistentGroups.Add($"群组 {group.NickName} (ID: {group.Id}) - 自动回复开启但未配置AI代理");
                }
            }

            if (inconsistentGroups.Any())
            {
                Logger.LogWarning("⚠️ 发现数据一致性问题:\n{Issues}", string.Join("\n", inconsistentGroups));

                // 可选：向用户显示警告
                if (inconsistentGroups.Count <= 3)
                {
                    foreach (var issue in inconsistentGroups.Take(3))
                    {
                        Snackbar.Add($"数据同步警告: {issue}", Severity.Warning);
                    }
                }
            }
            else
            {
                Logger.LogDebug("✅ 数据完整性检查通过");
            }

            // 🔍 数据库状态验证已移除，避免时序问题导致的误报
            // 注释：ValidateDatabaseStateAsync在数据更新后立即执行可能导致时序问题
            // 因为UI状态更新和数据库状态同步可能存在微小的时间差
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "数据完整性检查失败");
        }
    }

    private async Task GetGroupsForCurrentAccount()
    {
        if (!HasSelectedAccount)
        {
            Snackbar.Add("请先选择微信账号", Severity.Warning);
            return;
        }

        // 防重复点击检查（参照联系人同步模式）
        if (_isLoadingGroups)
        {
            Snackbar.Add("群组同步任务正在进行中，请稍候...", Severity.Warning);
            return;
        }

        try
        {
            // 设置按钮加载状态
            _isLoadingGroups = true;
            await InvokeAsync(StateHasChanged);

            var command = new GetGroupDetailsCommand
            {
                WxManagerId = SelectedAccount!.Id,
                ForceRefresh = false,
                IncludeMembers = true,
                MaxGroupsPerBatch = 50,
                MinIntervalMs = 300,
                MaxIntervalMs = 1500
            };

            var result = await WxApi.GetGroupDetails(command);

            if (result != null)
            {
                Snackbar.Add("群组同步任务已加入队列，正在处理中...", Severity.Info);

                // 加入SignalR组以接收同步完成通知
                await JoinWxManagerGroupAsync(SelectedAccount.Id);

                // 启动智能状态检查机制（防止SignalR通知失败）- 增强版本
                _ = Task.Run(async () =>
                {
                    var checkCount = 0;
                    var maxChecks = 120; // 10分钟内检查120次，每5秒一次
                    var startTime = DateTime.UtcNow;

                    while (_isLoadingGroups && checkCount < maxChecks)
                    {
                        await Task.Delay(5000); // 每5秒检查一次
                        checkCount++;

                        try
                        {
                            Logger.LogDebug("🔍 检查群组同步状态 - 第{Count}次检查，已等待{ElapsedSeconds}秒",
                                checkCount, (DateTime.UtcNow - startTime).TotalSeconds);

                            // 在30秒后开始主动检查同步状态
                            if (checkCount >= 6) // 30秒后
                            {
                                Logger.LogInformation("🔍 主动检查群组同步是否已完成 - WxManagerId: {WxManagerId}", SelectedAccount.Id);

                                // 尝试强制刷新数据，看是否有新的群组数据
                                await InvokeAsync(async () =>
                                {
                                    try
                                    {
                                        await ReloadGroupDataFromDatabase();
                                        Logger.LogInformation("✅ 主动数据刷新完成，检查是否有新数据");
                                    }
                                    catch (Exception ex)
                                    {
                                        Logger.LogWarning(ex, "⚠️ 主动数据刷新失败");
                                    }
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.LogWarning(ex, "⚠️ 群组同步状态检查失败");
                        }
                    }

                    // 如果超时仍在同步状态，强制重置并刷新数据
                    if (_isLoadingGroups)
                    {
                        Logger.LogWarning("⏰ 群组同步超时，强制重置按钮状态并刷新数据 - WxManagerId: {WxManagerId}", SelectedAccount.Id);
                        _isLoadingGroups = false;

                        await InvokeAsync(async () =>
                        {
                            StateHasChanged();
                            Snackbar.Add("同步超时，正在强制刷新数据...", Severity.Warning);

                            // 强制刷新数据
                            try
                            {
                                await ReloadGroupDataFromDatabase();
                                Snackbar.Add("数据已刷新，请检查群组列表", Severity.Info);
                            }
                            catch (Exception ex)
                            {
                                Logger.LogError(ex, "❌ 超时后强制刷新数据失败");
                                Snackbar.Add("数据刷新失败，请手动刷新页面", Severity.Error);
                            }
                        });
                    }
                });
            }
            else
            {
                _isLoadingGroups = false;
                await InvokeAsync(StateHasChanged);
                Snackbar.Add("获取群组失败：服务器返回空结果", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取群组失败 - WxManagerId: {WxManagerId}", SelectedAccount!.Id);
            _isLoadingGroups = false;
            await InvokeAsync(StateHasChanged);

            Snackbar.Add($"获取群组失败: {ex.Message}", Severity.Error);
        }
    }



    private async Task DeleteGroup(HappyWechat.Web.VOs.WxGroupVo group)
    {
        var confirmationContent = $"确定要删除群组吗？\\n\\n群组信息：\\n• 群名称：{group.NickName}\\n• 成员数：{group.MemberCount}\\n\\n注意：此操作不可撤销！";

        bool? result = await DialogService.ShowMessageBox(
            "确认删除群组",
            confirmationContent,
            yesText: "确认删除",
            cancelText: "取消");

        if (result == true)
        {
            // 🛡️ 防止重复删除：检查是否已在删除中
            if (_deletingGroupIds.Contains(group.Id))
            {
                Snackbar.Add($"群组 {group.NickName} 正在删除中，请稍候...", Severity.Warning);
                return;
            }

            _deletingGroupIds.Add(group.Id);
            StateHasChanged();

            try
            {
                Snackbar.Add($"正在删除群组 {group.NickName}...", Severity.Info);

                // 🗑️ 执行群组删除（保持现有逻辑）
                await WxApi.DeleteGroup(group.Id);
                // 🔧 注释冗余的API调用完成日志 - 减少日志噪音，Controller层已有Warning级别日志
                // Logger.LogInformation("✅ 群组删除API调用完成 - GroupId: {GroupId}", group.Id);

                // 如果选中列表中包含这个群组，移除选择
                _selectedGroupIds.Remove(group.Id);
                UpdateSelectAllState();

                // ✨ 立即清除所有缓存并刷新UI - 确保删除后立即更新界面
                Snackbar.Add($"成功删除群组 {group.NickName}", Severity.Success);

                // 清除所有层级的缓存
                await ClearAllCachesForGroup(group.Id);

                // 立即从本地列表中移除该群组（即时UI响应）
                if (Elements != null)
                {
                    var itemToRemove = Elements.FirstOrDefault(g => g.Id == group.Id);
                    if (itemToRemove != null)
                    {
                        var tempList = Elements.ToList();
                        tempList.Remove(itemToRemove);
                        Elements = tempList;

                        // 🎯 立即清除删除状态，防止重复删除
                        _deletingGroupIds.Remove(group.Id);
                        StateHasChanged();
                        Logger.LogDebug("✅ 已从本地列表移除群组并清除删除状态 - GroupId: {GroupId}", group.Id);
                    }
                }

                // 延迟后强制从数据库重新加载（确保数据一致性）
                _ = Task.Run(async () =>
                {
                    await Task.Delay(500); // 给后端一些时间完成删除操作
                    await InvokeAsync(async () =>
                    {
                        await OnSearchCore(useCache: false);
                        Logger.LogDebug("🔄 已完成后台数据重新加载验证");
                    });
                });
                // 🔧 注释冗余的UI刷新完成日志 - 减少日志噪音
                // Logger.LogInformation("✅ 群组删除UI刷新完成 - GroupId: {GroupId}", group.Id);

                // 🔄 后台执行统一缓存管理器的同步处理（不阻塞UI）
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // 🔧 无缓存架构：无需缓存管理操作
                        // 🔧 注释冗余的后台同步完成日志 - 减少日志噪音
                        // Logger.LogInformation("🔄 群组删除后台同步完成 - GroupId: {GroupId}", group.Id);

                        // 无缓存架构：直接刷新UI确保数据一致性
                        await InvokeAsync(async () =>
                        {
                            await OnSearchCore(useCache: false);
                            // 🔧 注释冗余的UI数据刷新日志 - 减少日志噪音
                            // Logger.LogInformation("✅ 群组删除后UI数据已刷新 - GroupId: {GroupId}", group.Id);
                        });
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "❌ 群组删除后台同步异常 - GroupId: {GroupId}", group.Id);
                    }
                });
            }
            catch (Exception ex)
            {
                var errorMessage = ex.Message.Contains("NetworkException")
                    ? "网络连接失败，请检查网络连接后重试"
                    : ex.Message.Contains("UnauthorizedException")
                    ? "权限不足，无法删除该群组"
                    : ex.Message.Contains("NotFoundException")
                    ? "群组不存在或已被删除"
                    : $"删除失败：{ex.Message}";

                await DialogService.ShowMessageBox(
                    "删除失败",
                    errorMessage,
                    yesText: "确定");

                Snackbar.Add($"删除群组 {group.NickName} 失败", Severity.Error);
            }
            finally
            {
                // 🧹 确保删除状态被清理（防止状态泄漏）
                if (_deletingGroupIds.Contains(group.Id))
                {
                    _deletingGroupIds.Remove(group.Id);
                    StateHasChanged();
                    Logger.LogDebug("🧹 Finally块清理删除状态 - GroupId: {GroupId}", group.Id);
                }
            }
        }
    }



    private async Task SendGroupMessage(HappyWechat.Web.VOs.WxGroupVo group)
    {
        // 🔧 验证当前选中的微信账号
        if (!HasSelectedAccount)
        {
            await DialogService.ShowMessageBox("提示", "请先选择微信账号");
            return;
        }

        // 创建一个虚拟的群组联系人对象用于发送消息
        var groupContact = new WxContactVo
        {
            Id = group.Id,
            WxManagerId = group.WxManagerId,
            WcId = group.ChatRoomId,
            NickName = group.NickName,
            SmallHead = group.SmallHeadImgUrl,
            BigHead = group.BigHeadImgUrl,
            ContactType = Domain.ValueObjects.Enums.WxContactType.Group
        };

        var parameters = new DialogParameters
        {
            ["WxContactVo"] = groupContact,
            ["CurrentWxManager"] = SelectedAccount
        };

        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true
        };

        await DialogService.ShowAsync<Components.Wx.WxSendMessage>($"发送消息到群组 - {group.NickName}", parameters, options);
    }

    private async Task SearchGroups()
    {
        await ExecuteSearchImmediately();
    }

    private async Task ExecuteSearchImmediately()
    {
        _isSearching = true;
        StateHasChanged();

        try
        {
            _page = 1;
            await ReloadGroupDataFromDatabase();
        }
        finally
        {
            _isSearching = false;
            StateHasChanged();
        }
    }

    private async Task OnPageChanged(int newPage)
    {
        _page = newPage;
        await OnSearch();
    }

    // 优化的Tab切换处理方法
    private async Task OnTabChanged()
    {
        Logger.LogInformation("🔄 群组Tab切换开始 - NewTabIndex: {TabIndex}", _activeTabIndex);

        try
        {
            // Tab切换处理
            Logger.LogDebug("处理Tab切换逻辑");

            if (HasSelectedAccount)
            {
                // 清空选择状态
                _selectedGroupIds.Clear();
                UpdateSelectAllState();

                // 重置分页，但保持搜索条件
                _page = 1;

                // 立即更新UI反映Tab切换
                await InvokeAsync(StateHasChanged);

                Logger.LogInformation("✅ 群组Tab切换完成 - Account: {AccountName}", SelectedAccount.NickName);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组Tab切换失败");
            await HandleTabChangeFailureAsync(ex);
        }
    }

    // 多选相关方法
    private void OnGroupSelectionChanged(Guid groupId, bool isSelected)
    {
        if (isSelected)
        {
            _selectedGroupIds.Add(groupId);
        }
        else
        {
            _selectedGroupIds.Remove(groupId);
        }

        UpdateSelectAllState();
        StateHasChanged();
    }

    private void OnSelectAllChanged(bool? value)
    {
        if (value == true)
        {
            // 全选当前页面的所有群组
            _selectedGroupIds.Clear();
            foreach (var group in Elements)
            {
                _selectedGroupIds.Add(group.Id);
            }
        }
        else
        {
            // 清空选择
            _selectedGroupIds.Clear();
        }

        UpdateSelectAllState();
        StateHasChanged();
    }

    private void UpdateSelectAllState()
    {
        var currentPageGroupIds = Elements.Select(g => g.Id).ToHashSet();
        var selectedOnCurrentPage = _selectedGroupIds.Where(id => currentPageGroupIds.Contains(id)).Count();
        var totalOnCurrentPage = currentPageGroupIds.Count();

        if (selectedOnCurrentPage == 0)
        {
            _isAllSelectedValue = false;
            _isIndeterminate = false;
        }
        else if (selectedOnCurrentPage == totalOnCurrentPage)
        {
            _isAllSelectedValue = true;
            _isIndeterminate = false;
        }
        else
        {
            _isAllSelectedValue = false;
            _isIndeterminate = true;
        }
    }

    private void ClearSelection()
    {
        _selectedGroupIds.Clear();
        UpdateSelectAllState();
        StateHasChanged();
    }

    private async Task ShowBatchEditDialog()
    {
        if (_selectedGroupIds.Count == 0)
        {
            Snackbar.Add("请先选择要编辑的群组", Severity.Warning);
            return;
        }

        var selectedGroups = GetSelectedGroups();

        var parameters = new DialogParameters
        {
            ["SelectedGroups"] = selectedGroups,
            ["AiAgents"] = aiAgents,
            ["OnBatchOperationCompleted"] = EventCallback.Factory.Create(this, OnBatchOperationCompleted)
        };

        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true,
            CloseOnEscapeKey = false
        };

        await DialogService.ShowAsync<Components.Wx.BatchEditGroupDialog>("批量编辑群组", parameters, options);
    }

    private async Task OnBatchOperationCompleted()
    {
        // 清空选择
        _selectedGroupIds.Clear();
        UpdateSelectAllState();

        // 刷新数据
        await OnSearch();
        StateHasChanged();
    }

    // 获取选中的群组
    private List<HappyWechat.Web.VOs.WxGroupVo> GetSelectedGroups()
    {
        return Elements.Where(g => _selectedGroupIds.Contains(g.Id)).ToList();
    }

    /// <summary>
    /// 注册群组特定的SignalR回调 - 修复重复DotNetObjectReference创建问题
    /// </summary>
    protected async Task RegisterPageSpecificCallbacks()
    {
        // 🔧 修复：统一使用单一的DotNetObjectReference，避免重复创建
        _dotNetRefGroup = DotNetObjectReference.Create(this);
        await JSRuntime.InvokeVoidAsync("setGlobalGroupComponent", _dotNetRefGroup);
        await JSRuntime.InvokeVoidAsync("registerGroupCallbacks");

        // 🔧 修复：使用同一个组件引用，避免重复回调注册
        await JSRuntime.InvokeVoidAsync("registerPageRefresh", "group", _dotNetRefGroup);

        Logger.LogDebug("📡 群组页面SignalR回调注册成功");
    }

    /// <summary>
    /// 简化刷新管理器调用的刷新方法
    /// </summary>
    [JSInvokable]
    public async Task OnSimplifiedRefresh(object refreshData)
    {
        try
        {
            // 🔧 注释冗余的刷新通知日志 - 减少日志噪音
            // Logger.LogInformation("🔄 收到简化刷新通知，开始刷新群组数据...");

            // 重新加载群组数据
            await LoadInitialDataAsync();

            // 通知UI更新
            await InvokeAsync(StateHasChanged);

            // 🔧 注释冗余的数据刷新完成日志 - 减少日志噪音
            // Logger.LogInformation("✅ 群组数据刷新完成");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组数据刷新失败");
        }
    }

    protected async Task<bool> JoinWxManagerGroupAsync(Guid wxManagerId)
    {
        try
        {
            // 基础的SignalR组加入逻辑
            try
            {
                var success = await JSRuntime.InvokeAsync<bool>("joinWxManagerGroup", wxManagerId.ToString());
                if (!success)
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "加入基础SignalR组失败 - ManagerId: {ManagerId}", wxManagerId);
                return false;
            }

            // 群组特定的SignalR组加入
            await JSRuntime.InvokeVoidAsync("eval", $@"
                if (window.groupSyncClient) {{
                    window.groupSyncClient.joinWxManagerGroup('{wxManagerId}').then(function(success) {{
                        if (success) {{
                            console.log('成功加入微信账号组: {wxManagerId}');
                        }} else {{
                            console.warn('加入微信账号组失败: {wxManagerId}');
                        }}
                    }}).catch(function(error) {{
                        console.error('加入微信账号组异常:', error);
                    }});
                }}
            ");

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "加入群组SignalR组失败 - ManagerId: {ManagerId}", wxManagerId);
            return false;
        }
    }

    // SignalR回调方法
    [JSInvokable]
    public async Task OnProgressUpdate(object progressMessage)
    {
        try
        {
            // 简化进度更新，仅记录日志
            Logger.LogDebug("收到群组同步进度更新");
            // 不再更新复杂的进度状态，保持UI简洁
        }
        catch (Exception ex)
        {
            // 处理进度更新失败，静默处理
        }
    }

    [JSInvokable]
    public async Task OnSyncCompleted(object completionData)
    {
        try
        {
            Logger.LogInformation("🎉 [前端] 群组同步完成回调被调用 - 当前账户: {AccountId}", SelectedAccount?.Id);

            var json = System.Text.Json.JsonSerializer.Serialize(completionData);
            Logger.LogInformation("🔍 [前端] 接收到的完成数据: {Data}", json);

            // 解析完成数据（参照联系人同步模式）
            var jsonDoc = System.Text.Json.JsonDocument.Parse(json);
            var root = jsonDoc.RootElement;

            // 获取微信管理器ID
            var wxManagerIdStr = root.TryGetProperty("WxManagerId", out var wxManagerIdProp) ? wxManagerIdProp.GetString() :
                                root.TryGetProperty("wxManagerId", out var wxManagerIdProp2) ? wxManagerIdProp2.GetString() : null;

            Logger.LogInformation("🔍 [前端] 解析的WxManagerId: {WxManagerId}, 当前选中账户: {SelectedAccountId}, 匹配: {IsMatch}",
                wxManagerIdStr, SelectedAccount?.Id,
                Guid.TryParse(wxManagerIdStr, out var tempId) && HasSelectedAccount && tempId == SelectedAccount.Id);

            if (Guid.TryParse(wxManagerIdStr, out var wxManagerId) && HasSelectedAccount && wxManagerId == SelectedAccount.Id)
            {
                Logger.LogInformation("✅ [前端] 开始处理群组同步完成 - WxManagerId: {WxManagerId}", wxManagerId);

                // 重置同步状态
                _isLoadingGroups = false;

                Snackbar.Add("群组同步完成！正在刷新数据...", Severity.Success);

                // 使用统一前端缓存服务处理同步完成
                await HandleGroupSyncCompleted();

                await InvokeAsync(StateHasChanged);

                Logger.LogInformation("✅ [前端] 群组同步完成处理结束");
            }
            else
            {
                Logger.LogWarning("⚠️ [前端] 同步完成通知不匹配当前账户，忽略处理 - 通知WxManagerId: {NotificationWxManagerId}, 当前账户: {CurrentAccountId}",
                    wxManagerIdStr, SelectedAccount?.Id);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ [前端] 处理群组同步完成失败");
            _isLoadingGroups = false; // 确保状态重置
            await InvokeAsync(StateHasChanged);
            Snackbar.Add("数据刷新处理异常，请手动刷新页面", Severity.Error);
        }
    }

    /// <summary>
    /// 处理群组同步完成（参照联系人同步模式）- 添加防抖机制
    /// </summary>
    private async Task HandleGroupSyncCompleted()
    {
        try
        {
            if (HasSelectedAccount)
            {
                // 使用防抖机制避免重复处理
                if (!GroupSyncDebouncer.ShouldExecuteRefresh(SelectedAccount.Id, "sync_completed"))
                {
                    Logger.LogDebug("⏱️ [前端] 防抖跳过群组同步完成处理 - WxManagerId: {WxManagerId}", SelectedAccount.Id);
                    return;
                }

                Logger.LogInformation("🔄 [前端] 群组同步完成处理 - WxManagerId: {WxManagerId} (无缓存架构)", SelectedAccount.Id);

                // 🔧 重置按钮加载状态
                _isLoadingGroups = false;
                await InvokeAsync(StateHasChanged);

                // 🔧 无缓存架构：无需缓存清理，直接刷新数据
                Logger.LogInformation("✅ [前端] 群组同步完成处理成功 - 数据刷新已自动触发");

                    // 使用防抖机制的额外数据刷新
                    if (GroupSyncDebouncer.ShouldExecuteRefresh(SelectedAccount.Id, "force_refresh"))
                    {
                        Logger.LogInformation("🔄 [前端] 额外触发强制数据刷新确保UI更新");
                        await ReloadGroupDataFromDatabase();
                    }
            }
            else
            {
                Logger.LogWarning("⚠️ [前端] 没有选中的账户，无法处理群组同步完成");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ [前端] 处理群组同步完成失败，降级到强制刷新");

            // 🔧 确保重置按钮状态
            _isLoadingGroups = false;
            await InvokeAsync(StateHasChanged);

            // 降级到原有方案（强制执行，忽略防抖）
            if (HasSelectedAccount)
            {
                GroupSyncDebouncer.ForceExecute(SelectedAccount.Id, "force_refresh");
            }
            await ReloadGroupDataFromDatabase();
        }
    }

    [JSInvokable]
    public async Task OnSyncFailed(object failureData)
    {
        try
        {
            Snackbar.Add("群组同步失败", Severity.Error);
            _isLoadingGroups = false;
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception)
        {
            // 处理同步失败，静默处理
            _isLoadingGroups = false;
        }
    }

    // 🔧 移除冗余的SignalR配置变更处理 - 已通过统一数据流模式解决数据一致性问题

    /// <summary>
    /// JavaScript调用的统一缓存清理回调方法
    /// </summary>
    [JSInvokable]
    public async Task OnUnifiedCacheCleared(string wxManagerId, string dataType)
    {
        try
        {
            Console.WriteLine($"🧹 [前端] 群组统一缓存清理完成回调 - ManagerId: {wxManagerId}, DataType: {dataType}");

            // 参数验证和类型转换
            if (string.IsNullOrEmpty(wxManagerId))
            {
                Logger.LogWarning("⚠️ [JavaScript] wxManagerId参数为空，跳过处理");
                return;
            }

            if (!Guid.TryParse(wxManagerId, out var managerGuid))
            {
                Logger.LogWarning("⚠️ [JavaScript] wxManagerId格式无效: {WxManagerId}", wxManagerId);
                return;
            }

            if (HasSelectedAccount && SelectedAccount!.Id == managerGuid)
            {
                Logger.LogInformation("🔄 [JavaScript] 群组统一缓存清理回调触发 - WxManagerId: {WxManagerId}, DataType: {DataType}",
                    managerGuid, dataType);

                await InvokeAsync(async () =>
                {
                    await OnSearchCore(useCache: false);
                    StateHasChanged();
                });

                Logger.LogInformation("✅ [JavaScript] 群组统一缓存清理回调处理完成");
            }
            else
            {
                Logger.LogDebug("🔍 [JavaScript] 管理器ID不匹配，跳过处理 - 当前: {Current}, 请求: {Requested}",
                    HasSelectedAccount ? SelectedAccount!.Id : Guid.Empty, managerGuid);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ [JavaScript] 群组统一缓存清理回调处理失败");
        }
    }

    // 获取状态颜色
    private Color GetStatusColor(Domain.ValueObjects.Enums.WxStatus status)
    {
        return status switch
        {
            Domain.ValueObjects.Enums.WxStatus.AlreadyLogIn => Color.Success,
            Domain.ValueObjects.Enums.WxStatus.NotLogIn => Color.Error,
            _ => Color.Warning
        };
    }

    // 获取状态文本
    private string GetStatusText(Domain.ValueObjects.Enums.WxStatus status)
    {
        return status switch
        {
            Domain.ValueObjects.Enums.WxStatus.AlreadyLogIn => "在线",
            Domain.ValueObjects.Enums.WxStatus.NotLogIn => "离线",
            _ => "未知"
        };
    }


    // 手动添加群组相关方法
    private async Task AddGroupManually()
    {
        if (string.IsNullOrWhiteSpace(manualGroupId?.Trim()) || !HasSelectedAccount)
        {
            return;
        }

        try
        {
            _isAddingGroup = true;
            StateHasChanged();

            // 使用完整的4步群组添加流程
            var command = new CompleteGroupAdditionCommand
            {
                WxManagerId = SelectedAccount!.Id,
                GroupId = manualGroupId.Trim(),
                ForceRefresh = false,
                IncludeMembers = true,
                TimeoutSeconds = 30
            };

            var result = await WxApi.CompleteGroupAddition(command);

            if (result.Success)
            {
                var steps = string.Join(", ", result.CompletedSteps);
                Snackbar.Add($"群组添加成功 - {result.GroupInfo?.NickName} (成员数: {result.MemberCount})", Severity.Success);
                Logger.LogInformation("群组添加成功 - 完成步骤: {Steps}", steps);

                manualGroupId = "";

                // 第四步：刷新整个页面的列表
                await ReloadGroupDataFromDatabase();
            }
            else
            {
                var completedSteps = result.CompletedSteps.Count > 0 ?
                    $" (已完成: {string.Join(", ", result.CompletedSteps)})" : "";
                Snackbar.Add($"群组添加失败: {result.ErrorMessage}{completedSteps}", Severity.Error);
                Logger.LogWarning("群组添加失败 - 错误: {Error}, 已完成步骤: {Steps}",
                    result.ErrorMessage, string.Join(", ", result.CompletedSteps));
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"添加群组异常: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "添加群组异常 - GroupId: {GroupId}", manualGroupId);
        }
        finally
        {
            _isAddingGroup = false;
            StateHasChanged();
        }
    }

    private async Task OnGroupIdKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await AddGroupManually();
        }
    }

    private string? ValidateGroupId(string groupId)
    {
        if (string.IsNullOrWhiteSpace(groupId))
            return "群组ID不能为空";

        if (groupId.Length < 3)
            return "群组ID至少3个字符";

        return null;
    }


    public override void Dispose()
    {
        try
        {
            // 群组特定的清理
            SearchDebounceService?.CancelSearch(SEARCH_KEY);
            JSRuntime.InvokeVoidAsync("cleanupGroupSignalR");
            _dotNetRefGroup?.Dispose();
        }
        catch (Exception ex)
        {
            Logger.LogDebug(ex, "群组特定资源清理失败");
        }

        // 基础资源清理
        _dotNetRefGroup?.Dispose();
    }




    /// <summary>
    /// 强制从数据库重新加载配置，忽略缓存
    /// </summary>
    private async Task ForceReloadConfigurations()
    {
        try
        {
            Logger.LogInformation("开始强制重新加载群组配置");

            // 等待一小段时间确保数据库写入完成
            await Task.Delay(200);

            // 🔧 无缓存架构：直接重新加载数据库数据
            await ReloadGroupDataFromDatabase();

            Logger.LogInformation("强制重新加载群组配置完成");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"强制重新加载配置失败: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "强制重新加载群组配置失败");
        }
        finally
        {
            StateHasChanged();
        }
    }

    /// <summary>
    /// 确保群组删除操作完全生效 - 增强版数据同步机制
    /// </summary>
    private async Task EnsureGroupDeleteOperationCompleted(Guid deletedGroupId, string groupName)
    {
        const int maxRetries = 5;
        const int baseDelayMs = 300;

        try
        {
            Logger.LogInformation("🔍 开始验证群组删除操作完成状态 - GroupId: {GroupId}", deletedGroupId);

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                // 渐进式延迟：第一次300ms，第二次600ms，第三次900ms...
                var delayMs = baseDelayMs * attempt;
                await Task.Delay(delayMs);

                Logger.LogDebug("🔄 第 {Attempt}/{MaxRetries} 次验证群组删除状态 - GroupId: {GroupId}",
                    attempt, maxRetries, deletedGroupId);

                // 强制从数据库重新加载，绕过所有缓存层
                await OnSearchCore(useCache: false);

                // 验证群组是否已从列表中移除
                var stillExists = Elements.Any(g => g.Id == deletedGroupId);

                if (!stillExists)
                {
                    // 删除成功
                    Logger.LogInformation("✅ 群组删除操作验证成功 - GroupId: {GroupId}, 尝试次数: {Attempt}",
                        deletedGroupId, attempt);
                    Snackbar.Add($"成功删除群组 {groupName}", Severity.Success);
                    return;
                }

                if (attempt == maxRetries)
                {
                    // 最后一次尝试仍然失败
                    Logger.LogError("❌ 严重的数据一致性问题：群组删除操作未生效 - GroupId: {GroupId}, 已尝试 {Attempts} 次",
                        deletedGroupId, maxRetries);
                    Snackbar.Add($"删除群组 {groupName} 可能未完全生效，请刷新页面确认", Severity.Warning);

                    // 尝试最后的修复措施：清除所有缓存并强制刷新
                    await ForceFullGroupCacheRefresh();
                }
                else
                {
                    Logger.LogWarning("⚠️ 第 {Attempt} 次验证失败，群组仍存在，将重试 - GroupId: {GroupId}",
                        attempt, deletedGroupId);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 群组删除操作验证过程异常 - GroupId: {GroupId}", deletedGroupId);
            Snackbar.Add($"删除群组 {groupName} 的验证过程出现异常", Severity.Error);
        }
    }

    /// <summary>
    /// 清除群组删除相关的本地缓存 - 无缓存架构
    /// </summary>
    private async Task ClearAllCachesForGroup(Guid groupId)
    {
        try
        {
            // 🔧 注释冗余的缓存清除日志 - 减少日志噪音
            // Logger.LogInformation("🧹 清除群组删除相关的本地缓存 - GroupId: {GroupId}", groupId);

            // 🔧 无缓存架构：只清除本地UI缓存
            _tabDataCache.Clear();
            Logger.LogDebug("✅ 已清除前端本地缓存");

            // 短暂延迟确保UI状态更新
            await Task.Delay(100);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 清除群组本地缓存失败 - GroupId: {GroupId}", groupId);
        }
    }

    /// <summary>
    /// 强制重新加载群组数据 - 无缓存架构
    /// </summary>
    private async Task ForceFullGroupCacheRefresh()
    {
        try
        {
            Logger.LogInformation("🔧 执行强制群组数据重新加载");

            if (HasSelectedAccount)
            {
                // 清除所有本地缓存
                _tabDataCache.Clear();

                // 🔧 无缓存架构：直接重新加载数据库数据
                await OnSearchCore(useCache: false);

                Logger.LogInformation("✅ 强制群组数据重新加载成功");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 强制群组数据重新加载失败");
        }
    }

    /// <summary>
    /// 验证删除后的群组数据一致性
    /// </summary>
    private async Task ValidateGroupDataConsistencyAfterDelete(Guid deletedGroupId)
    {
        try
        {
            // 短暂延迟确保后端处理完成
            await Task.Delay(200);

            // 验证群组是否真的从列表中移除
            var stillExists = Elements.Any(g => g.Id == deletedGroupId);
            if (stillExists)
            {
                Logger.LogWarning("⚠️ 数据一致性问题：已删除的群组仍在列表中 - GroupId: {GroupId}", deletedGroupId);

                // 强制再次刷新
                await ReloadGroupDataFromDatabase();

                // 如果仍然存在，记录错误
                if (Elements.Any(g => g.Id == deletedGroupId))
                {
                    Logger.LogError("❌ 严重的数据一致性问题：删除操作未生效 - GroupId: {GroupId}", deletedGroupId);
                    Snackbar.Add("数据同步异常，请刷新页面", Severity.Warning);
                }
            }
            else
            {
                Logger.LogDebug("✅ 删除后群组数据一致性验证通过 - GroupId: {GroupId}", deletedGroupId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 删除后群组数据一致性验证失败 - GroupId: {GroupId}", deletedGroupId);
        }
    }

    protected void NavigateToWxManage()
    {
        Navigation.NavigateTo("/wxManage");
    }

}