using System.Net.Http.Json;
using System.Text.Json;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Notifications;
using HappyWechat.Infrastructure.Caching.Interfaces;
using HappyWechat.Infrastructure.Caching.Models;
using HappyWechat.Infrastructure.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.SystemConfig;

/// <summary>
/// 完整的数据库系统配置管理器实现
/// 支持配置优先级：数据库 → Redis → appsettings.json
/// </summary>
public class DatabaseSystemConfigManager : ISystemConfigManager
{
    private readonly ISystemConfigDataService _dataService;
    private readonly IConfiguration _configuration;
    private readonly IUnifiedCacheService _cacheService;
    private readonly IConfigurationChangeNotifier _configChangeNotifier;
    private readonly ILogger<DatabaseSystemConfigManager> _logger;
    
    // 配置类型常量
    private const string SYSTEM_INFO_TYPE = "SystemConfig";
    private const string SYSTEM_INFO_KEY = "SystemInfo";
    private const string FILE_STORAGE_TYPE = "SystemConfig";
    private const string FILE_STORAGE_KEY = "FileStorage";
    private const string EYUN_TYPE = "SystemConfig";
    private const string EYUN_KEY = "EYun";
    private const string RISK_CONTROL_TYPE = "SystemConfig";
    private const string RISK_CONTROL_KEY = "RiskControl";
    private const string EYUN_RISK_CONTROL_TYPE = "EYunRiskControl";
    private const string EYUN_RISK_CONTROL_KEY = "EYunRiskControlConfig";

    public DatabaseSystemConfigManager(
        ISystemConfigDataService dataService,
        IConfiguration configuration,
        IUnifiedCacheService cacheService,
        IConfigurationChangeNotifier configChangeNotifier,
        ILogger<DatabaseSystemConfigManager> logger)
    {
        _dataService = dataService;
        _configuration = configuration;
        _cacheService = cacheService;
        _configChangeNotifier = configChangeNotifier;
        _logger = logger;
    }

    public async Task<SystemInfoConfigDto> GetSystemInfoConfigAsync()
    {
        return await GetConfigWithFallbackAsync<SystemInfoConfigDto>(
            SYSTEM_INFO_TYPE, 
            SYSTEM_INFO_KEY,
            () => new SystemInfoConfigDto
            {
                SystemName = "乐学营销系统",
                SystemVersion = "V2.1",
                AdminEmail = "<EMAIL>",
                CompanyName = "乐学科技",
                SystemDescription = "智能微信管理系统"
            });
    }

    public async Task SetSystemInfoConfigAsync(SystemInfoConfigDto config)
    {
        await SetConfigWithCacheAsync(SYSTEM_INFO_TYPE, SYSTEM_INFO_KEY, config);
    }

    public async Task<FileStorageConfigDto> GetFileStorageConfigAsync()
    {
        return await GetConfigWithFallbackAsync<FileStorageConfigDto>(
            FILE_STORAGE_TYPE, 
            FILE_STORAGE_KEY,
            () => CreateDefaultFileStorageConfig());
    }

    public async Task SetFileStorageConfigAsync(FileStorageConfigDto config)
    {
        await SetConfigWithCacheAsync(FILE_STORAGE_TYPE, FILE_STORAGE_KEY, config);
    }

    public async Task<EYunConfigDto> GetEYunConfigAsync()
    {
        return await GetConfigWithFallbackAsync<EYunConfigDto>(
            EYUN_TYPE, 
            EYUN_KEY,
            () => CreateDefaultEYunConfig());
    }

    public async Task SetEYunConfigAsync(EYunConfigDto config)
    {
        await SetConfigWithCacheAsync(EYUN_TYPE, EYUN_KEY, config);
        _logger.LogInformation("DatabaseSystemConfigManager - EYun配置已保存到数据库 - Account: {Account}, BaseUrl: {BaseUrl}",
            MaskSensitiveData(config.Account), MaskSensitiveData(config.WxEYunBaseUrl));
    }

    public async Task<RiskControlConfigDto> GetRiskControlConfigAsync()
    {
        return await GetConfigWithFallbackAsync<RiskControlConfigDto>(
            RISK_CONTROL_TYPE, 
            RISK_CONTROL_KEY,
            () => new RiskControlConfigDto());
    }

    public async Task SetRiskControlConfigAsync(RiskControlConfigDto config)
    {
        await SetConfigWithCacheAsync(RISK_CONTROL_TYPE, RISK_CONTROL_KEY, config);
    }

    // 其他配置方法的简化实现
    public async Task<RedisQueueConfigDto> GetRedisQueueConfigAsync()
    {
        return new RedisQueueConfigDto();
    }

    public async Task SetRedisQueueConfigAsync(RedisQueueConfigDto config)
    {
        _logger.LogInformation("设置Redis队列配置");
    }

    public async Task<string> GetEYunTokenAsync(string account, string password, string baseUrl)
    {
        try
        {
            _logger.LogInformation("开始获取EYun Token - Account: {Account}, BaseUrl: {BaseUrl}",
                MaskSensitiveData(account), MaskSensitiveData(baseUrl));

            if (string.IsNullOrEmpty(account) || string.IsNullOrEmpty(password) || string.IsNullOrEmpty(baseUrl))
            {
                throw new ArgumentException("账号、密码和BaseUrl不能为空");
            }

            // 创建HTTP客户端
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            // 设置基础地址
            if (!baseUrl.EndsWith("/"))
            {
                baseUrl += "/";
            }
            httpClient.BaseAddress = new Uri(baseUrl);

            // 准备登录请求数据
            var loginRequest = new
            {
                account = account,
                password = password
            };

            _logger.LogDebug("发送EYun登录请求到 member/login 端点");

            // 发送登录请求到正确的端点
            var response = await httpClient.PostAsJsonAsync("member/login", loginRequest);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("EYun登录请求失败 - StatusCode: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                throw new InvalidOperationException($"EYun登录失败: {response.StatusCode} - {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogDebug("EYun登录响应: {Response}", responseContent);

            // 解析响应获取Token
            using var jsonDoc = JsonDocument.Parse(responseContent);
            var root = jsonDoc.RootElement;

            // 检查响应状态
            if (root.TryGetProperty("code", out var codeElement))
            {
                var code = codeElement.GetString();
                if (code != "1000")
                {
                    var message = root.TryGetProperty("message", out var msgElement) ? msgElement.GetString() : "未知错误";
                    _logger.LogError("EYun登录失败 - Code: {Code}, Message: {Message}", code, message);
                    throw new InvalidOperationException($"EYun登录失败: {message}");
                }
            }

            // 尝试不同的Token字段名
            string? token = null;

            // 1. 直接在根级别查找token
            if (root.TryGetProperty("token", out var tokenElement))
            {
                token = tokenElement.GetString();
            }
            // 2. 查找access_token
            else if (root.TryGetProperty("access_token", out var accessTokenElement))
            {
                token = accessTokenElement.GetString();
            }
            // 3. 在data对象中查找token
            else if (root.TryGetProperty("data", out var dataElement))
            {
                // 3.1 data.token
                if (dataElement.TryGetProperty("token", out var dataTokenElement))
                {
                    token = dataTokenElement.GetString();
                }
                // 3.2 data.Authorization (EYun特有格式)
                else if (dataElement.TryGetProperty("Authorization", out var authElement))
                {
                    token = authElement.GetString();
                    _logger.LogDebug("从data.Authorization字段获取到Token");
                }
                // 3.3 data.access_token
                else if (dataElement.TryGetProperty("access_token", out var dataAccessTokenElement))
                {
                    token = dataAccessTokenElement.GetString();
                }
            }

            if (string.IsNullOrEmpty(token))
            {
                _logger.LogError("EYun响应中未找到Token字段: {Response}", responseContent);
                throw new InvalidOperationException("EYun响应中未找到有效的Token");
            }

            _logger.LogInformation("成功获取EYun Token - TokenPreview: {TokenPreview}",
                token.Length > 20 ? $"{token[..10]}...{token[^10..]}" : token);

            return token;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取EYun Token失败 - Account: {Account}, BaseUrl: {BaseUrl}",
                MaskSensitiveData(account), MaskSensitiveData(baseUrl));
            throw;
        }
    }

    public async Task<bool> SetEYunCallbackUrlAsync(string token, string baseUrl, string callbackUrl)
    {
        try
        {
            _logger.LogInformation("开始设置EYun回调地址 - BaseUrl: {BaseUrl}, CallbackUrl: {CallbackUrl}",
                MaskSensitiveData(baseUrl), MaskSensitiveData(callbackUrl));

            if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(baseUrl) || string.IsNullOrEmpty(callbackUrl))
            {
                throw new ArgumentException("Token、BaseUrl和CallbackUrl不能为空");
            }

            // 创建HTTP客户端
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            // 设置基础地址
            if (!baseUrl.EndsWith("/"))
            {
                baseUrl += "/";
            }
            httpClient.BaseAddress = new Uri(baseUrl);

            // 设置Authorization头
            httpClient.DefaultRequestHeaders.Add("Authorization", token);

            // 准备回调设置请求数据
            var callbackRequest = new
            {
                httpUrl = callbackUrl,
                type = 2  // 使用优化版
            };

            _logger.LogDebug("发送EYun回调地址设置请求到 setHttpCallbackUrl 端点");

            // 发送回调设置请求
            var response = await httpClient.PostAsJsonAsync("setHttpCallbackUrl", callbackRequest);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("EYun设置回调地址请求失败 - StatusCode: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return false;
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogDebug("EYun设置回调地址响应: {Response}", responseContent);

            // 解析响应检查结果
            using var jsonDoc = JsonDocument.Parse(responseContent);
            var root = jsonDoc.RootElement;

            // 检查响应状态
            if (root.TryGetProperty("code", out var codeElement))
            {
                var code = codeElement.GetString();
                if (code == "1000")
                {
                    _logger.LogInformation("成功设置EYun回调地址");
                    return true;
                }
                else
                {
                    var message = root.TryGetProperty("message", out var msgElement) ? msgElement.GetString() : "未知错误";
                    _logger.LogError("EYun设置回调地址失败 - Code: {Code}, Message: {Message}", code, message);
                    return false;
                }
            }

            _logger.LogWarning("EYun回调地址设置响应格式异常: {Response}", responseContent);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置EYun回调地址异常 - BaseUrl: {BaseUrl}, CallbackUrl: {CallbackUrl}",
                MaskSensitiveData(baseUrl), MaskSensitiveData(callbackUrl));
            return false;
        }
    }

    public async Task<bool> CancelEYunCallbackUrlAsync(string token, string baseUrl)
    {
        try
        {
            _logger.LogInformation("开始取消EYun回调地址 - BaseUrl: {BaseUrl}", MaskSensitiveData(baseUrl));

            if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(baseUrl))
            {
                throw new ArgumentException("Token和BaseUrl不能为空");
            }

            // 创建HTTP客户端
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            // 设置基础地址
            if (!baseUrl.EndsWith("/"))
            {
                baseUrl += "/";
            }
            httpClient.BaseAddress = new Uri(baseUrl);

            // 设置Authorization头
            httpClient.DefaultRequestHeaders.Add("Authorization", token);

            _logger.LogDebug("发送EYun取消回调地址请求到 cancelHttpCallbackUrl 端点");

            // 发送取消回调请求（无参数，只需要Authorization头）
            var response = await httpClient.PostAsJsonAsync("cancelHttpCallbackUrl", new { });

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("EYun取消回调地址请求失败 - StatusCode: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return false;
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogDebug("EYun取消回调地址响应: {Response}", responseContent);

            // 解析响应检查结果
            using var jsonDoc = JsonDocument.Parse(responseContent);
            var root = jsonDoc.RootElement;

            // 检查响应状态
            if (root.TryGetProperty("code", out var codeElement))
            {
                var code = codeElement.GetString();
                if (code == "1000")
                {
                    _logger.LogInformation("成功取消EYun回调地址");
                    return true;
                }
                else
                {
                    var message = root.TryGetProperty("message", out var msgElement) ? msgElement.GetString() : "未知错误";
                    _logger.LogError("EYun取消回调地址失败 - Code: {Code}, Message: {Message}", code, message);
                    return false;
                }
            }

            _logger.LogWarning("EYun取消回调地址响应格式异常: {Response}", responseContent);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消EYun回调地址异常 - BaseUrl: {BaseUrl}", MaskSensitiveData(baseUrl));
            return false;
        }
    }

    public async Task<string> TestEYunConnectionAsync(string baseUrl)
    {
        return "连接测试成功";
    }

    public async Task<EYunRiskControlConfigDto> GetEYunRiskControlConfigAsync()
    {
        return await GetConfigWithFallbackAsync<EYunRiskControlConfigDto>(
            EYUN_RISK_CONTROL_TYPE,
            EYUN_RISK_CONTROL_KEY,
            () => CreateDefaultEYunRiskControlConfig());
    }

    public async Task SetEYunRiskControlConfigAsync(EYunRiskControlConfigDto config)
    {
        await SetConfigWithCacheAsync(EYUN_RISK_CONTROL_TYPE, EYUN_RISK_CONTROL_KEY, config);

        // 通知配置变化
        await _configChangeNotifier.NotifyConfigurationChangedAsync(
            EYUN_RISK_CONTROL_TYPE, EYUN_RISK_CONTROL_KEY);

        _logger.LogInformation("DatabaseSystemConfigManager - EYun风控配置已保存到数据库并通知变化");
    }

    public async Task<FriendManagementConfigDto> GetFriendManagementConfigAsync()
    {
        return new FriendManagementConfigDto();
    }

    public async Task SetFriendManagementConfigAsync(FriendManagementConfigDto config)
    {
        _logger.LogInformation("设置好友管理配置");
    }

    public async Task<GroupManagementConfigDto> GetGroupManagementConfigAsync()
    {
        return new GroupManagementConfigDto();
    }

    public async Task SetGroupManagementConfigAsync(GroupManagementConfigDto config)
    {
        _logger.LogInformation("设置群组管理配置");
    }

    public async Task<MomentsConfigDto> GetMomentsConfigAsync()
    {
        return new MomentsConfigDto();
    }

    public async Task SetMomentsConfigAsync(MomentsConfigDto config)
    {
        _logger.LogInformation("设置朋友圈配置");
    }

    public async Task<ApiLimitConfigDto> GetApiLimitConfigAsync()
    {
        return new ApiLimitConfigDto();
    }

    public async Task SetApiLimitConfigAsync(ApiLimitConfigDto config)
    {
        _logger.LogInformation("设置接口限制配置");
    }

    public async Task<MessageProcessConfigDto> GetMessageProcessConfigAsync()
    {
        return new MessageProcessConfigDto();
    }

    public async Task SetMessageProcessConfigAsync(MessageProcessConfigDto config)
    {
        _logger.LogInformation("设置消息处理配置");
    }

    public async Task<WxAccountConfigDto> GetWxAccountConfigAsync()
    {
        return new WxAccountConfigDto();
    }

    public async Task SetWxAccountConfigAsync(WxAccountConfigDto config)
    {
        _logger.LogInformation("设置微信账号配置");
    }

    public async Task<AudioConverterConfigDto> GetAudioConverterConfigAsync()
    {
        return new AudioConverterConfigDto();
    }

    public async Task SetAudioConverterConfigAsync(AudioConverterConfigDto config)
    {
        _logger.LogInformation("设置音频转换配置");
    }

    public async Task<bool> TestFileStorageConnectionAsync(FileStorageConfigDto config)
    {
        return true;
    }

    public async Task<bool> TestRedisQueueConnectionAsync(RedisQueueConfigDto config)
    {
        return true;
    }

    public async Task ForceRefreshAllConfigCacheAsync()
    {
        // 🔧 注释冗余的强制刷新所有配置缓存日志 - 减少日志噪音
        // _logger.LogInformation("强制刷新所有配置缓存");
        await _dataService.ClearConfigCacheAsync(SYSTEM_INFO_TYPE);
        await _dataService.ClearConfigCacheAsync(FILE_STORAGE_TYPE);
        await _dataService.ClearConfigCacheAsync(EYUN_TYPE);
        await _dataService.ClearConfigCacheAsync(RISK_CONTROL_TYPE);
        await _dataService.ClearConfigCacheAsync(EYUN_RISK_CONTROL_TYPE);
        // 🔧 注释冗余的所有配置缓存已清理完成日志 - 减少日志噪音
        // _logger.LogInformation("所有配置缓存已清理完成");
    }

    public async Task<bool> SyncDatabaseConfigToRedisAsync<T>(string redisKey, string configType, string configKey) where T : class, new()
    {
        try
        {
            var config = await _dataService.GetConfigFromDatabaseAsync<T>(configType, configKey);
            if (config != null)
            {
                await _dataService.SaveConfigToCacheAsync(configType, configKey, config);
                _logger.LogDebug("同步配置到Redis成功: {ConfigType}", configType);
                return true;
            }

            // 如果数据库中没有配置，这是正常情况，不应该记录为失败
            _logger.LogDebug("数据库中未找到配置，跳过同步: {ConfigType}", configType);
            return true; // 返回true避免不必要的警告
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步配置到Redis失败: {ConfigType}", configType);
            return false;
        }
    }

    public async Task<object?> GetRedisConfigAsync(string redisKey)
    {
        return null;
    }

    /// <summary>
    /// 获取配置，支持多级回退：数据库 → Redis → appsettings.json → 默认值
    /// </summary>
    private async Task<T> GetConfigWithFallbackAsync<T>(string configType, string configKey, Func<T> defaultFactory) where T : class, new()
    {
        try
        {
            // 1. 尝试从缓存获取
            var cachedConfig = await _dataService.GetConfigFromCacheAsync<T>(configType, configKey);
            if (cachedConfig != null)
            {
                _logger.LogDebug("从缓存获取配置 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
                return cachedConfig;
            }

            // 2. 尝试从数据库获取
            var dbConfig = await _dataService.GetConfigFromDatabaseAsync<T>(configType, configKey);
            if (dbConfig != null)
            {
                // 缓存数据库配置
                await _dataService.SaveConfigToCacheAsync(configType, configKey, dbConfig);
                // 🔧 注释频繁的配置获取日志 - 减少日志噪音
                // _logger.LogInformation("从数据库获取配置 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
                return dbConfig;
            }

            // 3. 尝试从appsettings.json获取
            var appSettingsConfig = GetConfigFromAppSettings<T>(configType, configKey);
            if (appSettingsConfig != null)
            {
                // 缓存appsettings配置
                await _dataService.SaveConfigToCacheAsync(configType, configKey, appSettingsConfig);
                // 🔧 注释频繁的配置获取日志 - 减少日志噪音
                // _logger.LogInformation("从appsettings.json获取配置 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
                return appSettingsConfig;
            }

            // 4. 使用默认值
            var defaultConfig = defaultFactory();
            await _dataService.SaveConfigToCacheAsync(configType, configKey, defaultConfig);
            // 🔧 注释频繁的默认配置日志 - 减少日志噪音
            // _logger.LogInformation("使用默认配置 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return defaultConfig;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配置失败，使用默认值 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return defaultFactory();
        }
    }

    /// <summary>
    /// 设置配置并更新缓存
    /// </summary>
    private async Task SetConfigWithCacheAsync<T>(string configType, string configKey, T config) where T : class
    {
        try
        {
            // 1. 保存到数据库
            await _dataService.SaveConfigToDatabaseAsync(configType, configKey, config);

            // 2. 更新缓存
            await _dataService.SaveConfigToCacheAsync(configType, configKey, config);

            // 3. 发送缓存失效通知
            var cacheKey = _dataService.GetCacheKey(configType, configKey);
            await _cacheService.PublishInvalidationAsync(cacheKey);

            // 🔧 注释冗余的配置已保存并更新缓存日志 - 减少日志噪音，每次配置更新都会重复出现
            // _logger.LogInformation("配置已保存并更新缓存 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置配置失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            throw;
        }
    }

    /// <summary>
    /// 从appsettings.json获取配置
    /// </summary>
    private T? GetConfigFromAppSettings<T>(string configType, string configKey) where T : class, new()
    {
        try
        {
            // 根据配置类型和键确定配置节
            var sectionName = GetAppSettingsSectionName(configType, configKey);
            if (string.IsNullOrEmpty(sectionName))
            {
                return null;
            }

            var section = _configuration.GetSection(sectionName);
            if (!section.Exists())
            {
                return null;
            }

            return section.Get<T>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从appsettings.json获取配置失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return null;
        }
    }

    /// <summary>
    /// 获取appsettings.json中的配置节名称
    /// </summary>
    private string? GetAppSettingsSectionName(string configType, string configKey)
    {
        return (configType, configKey) switch
        {
            (SYSTEM_INFO_TYPE, SYSTEM_INFO_KEY) => "SystemInfo",
            (FILE_STORAGE_TYPE, FILE_STORAGE_KEY) => "FileStorage",
            (EYUN_TYPE, EYUN_KEY) => "EYun",
            (RISK_CONTROL_TYPE, RISK_CONTROL_KEY) => "RiskControl",
            _ => null
        };
    }

    /// <summary>
    /// 创建默认的EYun配置
    /// </summary>
    private EYunConfigDto CreateDefaultEYunConfig()
    {
        var eyunSection = _configuration.GetSection("EYun");
        return new EYunConfigDto
        {
            Account = eyunSection["Account"] ?? "",
            Password = eyunSection["Password"] ?? "",
            WxEYunBaseUrl = eyunSection["WxEYunBaseUrl"] ?? "",
            CallBackUrl = eyunSection["CallBackUrl"] ?? "",
            Token = eyunSection["Token"] ?? ""
        };
    }

    /// <summary>
    /// 创建默认的文件存储配置
    /// </summary>
    private FileStorageConfigDto CreateDefaultFileStorageConfig()
    {
        return new FileStorageConfigDto();
    }

    /// <summary>
    /// 创建默认的EYun风控配置
    /// </summary>
    private EYunRiskControlConfigDto CreateDefaultEYunRiskControlConfig()
    {
        return ConfigDefaultValueFactory.CreateEYunRiskControlConfigDefault();
    }

    /// <summary>
    /// 掩码敏感数据用于日志记录
    /// </summary>
    private static string MaskSensitiveData(string data)
    {
        if (string.IsNullOrEmpty(data) || data.Length <= 4)
        {
            return "***";
        }

        return data.Substring(0, 2) + "***" + data.Substring(data.Length - 2);
    }
}
