using HappyWechat.Application.DTOs.SystemConfig;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace HappyWechat.Infrastructure.Configuration;

/// <summary>
/// 环境感知的配置提供者 - 根据运行环境智能选择配置源
/// </summary>
public class EnvironmentVariableConfigurationProvider
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EnvironmentVariableConfigurationProvider> _logger;
    private readonly bool _isDockerEnvironment;

    public EnvironmentVariableConfigurationProvider(
        IConfiguration configuration,
        ILogger<EnvironmentVariableConfigurationProvider> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _isDockerEnvironment = IsDockerEnvironment();

        // 🔧 注释冗余的配置提供者初始化日志 - 降低日志噪音
        // _logger.LogInformation("🔧 配置提供者初始化 - 运行环境: {Environment}",
        //     _isDockerEnvironment ? "Docker容器" : "本地开发");
    }

    /// <summary>
    /// 检测是否在Docker环境中运行
    /// </summary>
    private bool IsDockerEnvironment()
    {
        try
        {
            // 检查Docker容器特有的环境变量
            if (Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") == "true")
                return true;

            // 检查Docker容器特有的文件
            if (File.Exists("/.dockerenv"))
                return true;

            // 检查cgroup信息（Linux容器）
            if (File.Exists("/proc/1/cgroup"))
            {
                var cgroup = File.ReadAllText("/proc/1/cgroup");
                if (cgroup.Contains("docker") || cgroup.Contains("containerd"))
                    return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 环境感知的配置值获取
    /// Docker环境：MySQL/Redis从环境变量获取，其他从配置文件
    /// 本地环境：所有配置从配置文件获取
    /// </summary>
    public string GetConfigValue(string key, string defaultValue = "")
    {
        try
        {
            // 检查是否为基础设施配置（MySQL/Redis）
            bool isInfrastructureConfig = IsInfrastructureConfigKey(key);

            if (_isDockerEnvironment && isInfrastructureConfig)
            {
                // Docker环境下的基础设施配置：优先从环境变量获取
                var envValue = Environment.GetEnvironmentVariable(key);
                if (!string.IsNullOrEmpty(envValue))
                {
                    _logger.LogDebug("🐳 Docker环境-从环境变量获取基础设施配置: {Key} = {Value}", key, MaskSensitiveValue(key, envValue));
                    return envValue;
                }

                _logger.LogDebug("🐳 Docker环境-环境变量未找到，使用默认值: {Key} = {Value}", key, defaultValue);
                return defaultValue;
            }
            else
            {
                // 本地环境或非基础设施配置：从配置文件获取
                var configValue = _configuration[key];
                if (!string.IsNullOrEmpty(configValue))
                {
                    _logger.LogDebug("📄 从配置文件获取配置: {Key} = {Value}", key, MaskSensitiveValue(key, configValue));
                    return configValue;
                }

                // Docker环境下的非基础设施配置，如果配置文件没有，也尝试环境变量
                if (_isDockerEnvironment)
                {
                    var envValue = Environment.GetEnvironmentVariable(key);
                    if (!string.IsNullOrEmpty(envValue))
                    {
                        _logger.LogDebug("🐳 Docker环境-从环境变量获取其他配置: {Key} = {Value}", key, MaskSensitiveValue(key, envValue));
                        return envValue;
                    }
                }

                _logger.LogDebug("使用默认配置值: {Key} = {Value}", key, defaultValue);
                return defaultValue;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配置值失败: {Key}", key);
            return defaultValue;
        }
    }

    /// <summary>
    /// 判断是否为基础设施配置键
    /// </summary>
    private bool IsInfrastructureConfigKey(string key)
    {
        var infrastructureKeys = new[]
        {
            // MySQL配置
            "MYSQL_HOST", "MYSQL_PORT", "MYSQL_DATABASE", "MYSQL_USER", "MYSQL_PASSWORD",
            // Redis配置
            "REDIS_HOST", "REDIS_PORT", "REDIS_PASSWORD", "REDIS_DATABASE_CACHE",
            "REDIS_DATABASE_MESSAGEQUEUE", "REDIS_DATABASE_SESSION", "REDIS_DATABASE_LOCK",
            // EYun配置
            "EYUN_ACCOUNT", "EYUN_PASSWORD", "EYUN_BASE_URL", "EYUN_CALLBACK_URL", "EYUN_TOKEN",
            // MinIO配置
            "MINIO_ENDPOINT", "MINIO_ACCESSKEY", "MINIO_SECRETKEY", "MINIO_USESSL",
            "MINIO_DEFAULTBUCKET", "MINIO_REGION",
            // 应用配置
            "APPLICATION_DOMAIN",
            // JWT配置（虽然已废弃，但保持兼容性）
            "JWT_SECRET_KEY", "JWT_ISSUER", "JWT_AUDIENCE"
        };

        return infrastructureKeys.Contains(key, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 脱敏敏感配置值用于日志输出
    /// </summary>
    private string MaskSensitiveValue(string key, string value)
    {
        var sensitiveKeys = new[] { "PASSWORD", "SECRET", "KEY", "TOKEN" };

        if (sensitiveKeys.Any(k => key.ToUpper().Contains(k)))
        {
            return value.Length > 6 ? $"{value[..3]}***{value[^3..]}" : "***";
        }

        return value;
    }

    /// <summary>
    /// 构建数据库连接字符串
    /// </summary>
    public string BuildDatabaseConnectionString()
    {
        if (_isDockerEnvironment)
        {
            // Docker环境：从环境变量构建
            var host = GetConfigValue("MYSQL_HOST", "localhost");
            var port = GetConfigValue("MYSQL_PORT", "3306");
            var database = GetConfigValue("MYSQL_DATABASE", "huakai");
            var user = GetConfigValue("MYSQL_USER", "root");
            var password = GetConfigValue("MYSQL_PASSWORD", "");

            var connectionString = $"Server={host};Port={port};Database={database};User={user};Password={password};ConnectionTimeout=30;Pooling=true;MinPoolSize=10;MaxPoolSize=100;";
            _logger.LogInformation("🐳 Docker环境-构建数据库连接字符串: Server={Host};Port={Port};Database={Database}", host, port, database);
            return connectionString;
        }
        else
        {
            // 本地环境：从配置文件获取
            var connectionString = _configuration.GetConnectionString("DefaultConnection");
            if (!string.IsNullOrEmpty(connectionString))
            {
                // 🔧 注释冗余的数据库连接字符串日志 - 降低日志噪音
                // _logger.LogInformation("📄 本地环境-使用配置文件中的数据库连接字符串");
                return connectionString;
            }

            // 如果配置文件没有，使用默认值
            _logger.LogWarning("⚠️ 本地环境-配置文件中未找到数据库连接字符串，使用默认配置");
            return "Server=localhost;Port=3306;Database=huakai;User=root;Password=;ConnectionTimeout=30;Pooling=true;MinPoolSize=10;MaxPoolSize=100;";
        }
    }

    /// <summary>
    /// 构建Redis连接字符串
    /// </summary>
    public string BuildRedisConnectionString()
    {
        if (_isDockerEnvironment)
        {
            // Docker环境：从环境变量构建
            var host = GetConfigValue("REDIS_HOST", "localhost");
            var port = GetConfigValue("REDIS_PORT", "6379");
            var password = GetConfigValue("REDIS_PASSWORD", "");

            var connectionString = string.IsNullOrEmpty(password)
                ? $"{host}:{port},abortConnect=false"
                : $"{host}:{port},password={password},abortConnect=false";

            _logger.LogInformation("🐳 Docker环境-构建Redis连接字符串: {Host}:{Port}", host, port);
            return connectionString;
        }
        else
        {
            // 本地环境：从配置文件获取
            var connectionString = _configuration["Redis:ConnectionString"];
            if (!string.IsNullOrEmpty(connectionString))
            {
                // 🔧 注释冗余的Redis连接字符串日志 - 降低日志噪音
                // _logger.LogInformation("📄 本地环境-使用配置文件中的Redis连接字符串");
                return connectionString;
            }

            // 如果配置文件没有，使用默认值
            _logger.LogWarning("⚠️ 本地环境-配置文件中未找到Redis连接字符串，使用默认配置");
            return "localhost:6379,abortConnect=false";
        }
    }



    /// <summary>
    /// 获取EYun配置（用于统一配置管理器）
    /// </summary>
    public EYunConfigDto GetEYunConfig()
    {
        // 从appsettings.json获取默认值
        var defaultAccount = _configuration["EYun:Account"] ?? "";
        var defaultPassword = _configuration["EYun:Password"] ?? "";
        var defaultBaseUrl = _configuration["EYun:WxEYunBaseUrl"] ?? "";
        var defaultCallBackUrl = _configuration["EYun:CallBackUrl"] ?? "";
        var defaultToken = _configuration["EYun:Token"] ?? "";

        return new EYunConfigDto
        {
            Account = GetConfigValue("EYUN_ACCOUNT", defaultAccount),
            Password = GetConfigValue("EYUN_PASSWORD", defaultPassword),
            WxEYunBaseUrl = GetConfigValue("EYUN_BASE_URL", defaultBaseUrl),
            CallBackUrl = GetConfigValue("EYUN_CALLBACK_URL", defaultCallBackUrl),
            Token = GetConfigValue("EYUN_TOKEN", defaultToken)
        };
    }

    /// <summary>
    /// JWT配置已移除 - 现在使用纯SessionId认证
    /// </summary>
    // GetJwtConfig方法已移除

    /// <summary>
    /// 获取应用域名
    /// </summary>
    public string GetApplicationDomain()
    {
        return GetConfigValue("APPLICATION_DOMAIN", "http://localhost:5215");
    }

    /// <summary>
    /// 获取Redis数据库配置
    /// </summary>
    public RedisDbConfig GetRedisDbConfig()
    {
        return new RedisDbConfig
        {
            Cache = int.Parse(GetConfigValue("REDIS_DATABASE_CACHE", "2")),
            MessageQueue = int.Parse(GetConfigValue("REDIS_DATABASE_MESSAGEQUEUE", "3")),
            Session = int.Parse(GetConfigValue("REDIS_DATABASE_SESSION", "1")),
            Lock = int.Parse(GetConfigValue("REDIS_DATABASE_LOCK", "4"))
        };
    }

    /// <summary>
    /// 获取MinIO配置
    /// </summary>
    public MinIOConfig GetMinIOConfig()
    {
        return new MinIOConfig
        {
            Endpoint = GetConfigValue("MINIO_ENDPOINT", "localhost:9000"),
            AccessKey = GetConfigValue("MINIO_ACCESSKEY", ""),
            SecretKey = GetConfigValue("MINIO_SECRETKEY", ""),
            UseSSL = bool.Parse(GetConfigValue("MINIO_USESSL", "false")),
            DefaultBucket = GetConfigValue("MINIO_DEFAULTBUCKET", "wechat"),
            Region = GetConfigValue("MINIO_REGION", "cn-north-1")
        };
    }
}

/// <summary>
/// EYun配置模型
/// </summary>
public class EYunConfig
{
    public string Account { get; set; } = "";
    public string Password { get; set; } = "";
    public string WxEYunBaseUrl { get; set; } = "";
    public string CallBackUrl { get; set; } = "";
    public string Token { get; set; } = "";
}

// JwtConfig类已移除 - 现在使用纯SessionId认证

/// <summary>
/// Redis数据库配置模型
/// </summary>
public class RedisDbConfig
{
    public int Cache { get; set; } = 2;
    public int MessageQueue { get; set; } = 3;
    public int Session { get; set; } = 1;
    public int Lock { get; set; } = 4;
}

/// <summary>
/// MinIO配置模型
/// </summary>
public class MinIOConfig
{
    public string Endpoint { get; set; } = "";
    public string AccessKey { get; set; } = "";
    public string SecretKey { get; set; } = "";
    public bool UseSSL { get; set; } = false;
    public string DefaultBucket { get; set; } = "";
    public string Region { get; set; } = "";
}
