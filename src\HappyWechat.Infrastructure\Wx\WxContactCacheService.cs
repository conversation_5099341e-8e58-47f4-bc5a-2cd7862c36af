using Microsoft.Extensions.Logging;
using StackExchange.Redis.Extensions.Core.Abstractions;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.Commons;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Redis;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 缓存清除范围枚举
/// </summary>
public enum CacheClearScope
{
    /// <summary>
    /// 只清除特定联系人
    /// </summary>
    ContactOnly,
    
    /// <summary>
    /// 只清除查询缓存
    /// </summary>
    QueryOnly,
    
    /// <summary>
    /// 只清除基础数据
    /// </summary>
    BaseDataOnly,
    
    /// <summary>
    /// 清除所有相关缓存
    /// </summary>
    All
}

/// <summary>
/// 微信联系人缓存服务实现
/// 实现分层缓存策略，提升查询性能和用户体验
/// </summary>
public class WxContactCacheService : IWxContactCacheService
{
    private readonly IRedisDatabase _redisDb;
    private readonly ILogger<WxContactCacheService> _logger;
    
    public WxContactCacheService(IRedisDatabase redisDb, ILogger<WxContactCacheService> logger)
    {
        _redisDb = redisDb;
        _logger = logger;
    }
    
    #region 基础数据缓存层 (2小时过期)
    
    public async Task<List<WxContactDto>?> GetBaseContactDataAsync(Guid managerId, WxContactType contactType)
    {
        try
        {
            var cacheKey = CacheKeys.GetBaseContactDataKey(managerId, contactType);
            var result = await _redisDb.GetAsync<List<WxContactDto>>(cacheKey);
            
            var isHit = result != null;
            await RecordCacheHitAsync(managerId, "BaseData", isHit);
            
            if (isHit)
            {
                _logger.LogInformation($"[缓存命中] 基础数据 - ManagerId: {managerId}, Type: {contactType}, 记录数: {result!.Count}");
            }
            else
            {
                _logger.LogInformation($"[缓存未命中] 基础数据 - ManagerId: {managerId}, Type: {contactType}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"获取基础联系人数据缓存失败 - ManagerId: {managerId}, Type: {contactType}");
            return null;
        }
    }
    
    public async Task SetBaseContactDataAsync(Guid managerId, WxContactType contactType, List<WxContactDto> contacts, TimeSpan? expiry = null)
    {
        try
        {
            var cacheKey = CacheKeys.GetBaseContactDataKey(managerId, contactType);
            var cacheExpiry = expiry ?? CacheKeys.GetCacheExpiry(CacheKeyType.BaseData);
            
            await _redisDb.AddAsync(cacheKey, contacts, cacheExpiry);
            
            _logger.LogInformation($"[缓存写入] 基础数据 - ManagerId: {managerId}, Type: {contactType}, 记录数: {contacts.Count}, 过期时间: {cacheExpiry.TotalMinutes}分钟");
            
            // 同时更新联系人数量统计
            await UpdateContactCountsAsync(managerId, contactType, contacts.Count);
            
            // 异步预加载首页数据
            _ = Task.Run(async () => await PreloadFirstPageDataAsync(managerId, contactType, contacts));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设置基础联系人数据缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }
    
    public async Task<Dictionary<WxContactType, int>?> GetContactCountsAsync(Guid managerId)
    {
        try
        {
            var contactTypes = new[] { WxContactType.Contact, WxContactType.Group };
            var tasks = contactTypes.Select(async type =>
            {
                var cacheKey = CacheKeys.GetContactCountKey(managerId, type);
                var count = await _redisDb.GetAsync<int?>(cacheKey);
                return new { Type = type, Count = count ?? 0 };
            });
            
            var results = await Task.WhenAll(tasks);
            return results.ToDictionary(r => r.Type, r => r.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"获取联系人数量统计缓存失败 - ManagerId: {managerId}");
            return null;
        }
    }
    
    public async Task SetContactCountsAsync(Guid managerId, Dictionary<WxContactType, int> counts)
    {
        try
        {
            var expiry = CacheKeys.GetCacheExpiry(CacheKeyType.BaseData);
            var tasks = counts.Select(async kvp =>
            {
                var cacheKey = CacheKeys.GetContactCountKey(managerId, kvp.Key);
                await _redisDb.AddAsync(cacheKey, kvp.Value, expiry);
            });
            
            await Task.WhenAll(tasks);
            _logger.LogInformation($"[缓存写入] 联系人数量统计 - ManagerId: {managerId}, 统计: {string.Join(", ", counts.Select(kv => $"{kv.Key}:{kv.Value}"))}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设置联系人数量统计缓存失败 - ManagerId: {managerId}");
        }
    }
    
    #endregion
    
    #region 查询结果缓存层 (30分钟过期)
    
    public async Task<PageResponse<WxContactDto>?> GetQueryResultAsync(
        Guid managerId,
        WxContactType contactType,
        string? searchNickName,
        string? searchRemarkName,
        int page,
        int pageSize)
    {
        // 调用多类型版本的方法，保持向后兼容
        return await GetQueryResultAsync(managerId, new List<WxContactType> { contactType }, searchNickName, searchRemarkName, page, pageSize);
    }

    public async Task<PageResponse<WxContactDto>?> GetQueryResultAsync(
        Guid managerId,
        List<WxContactType> contactTypes,
        string? searchNickName,
        string? searchRemarkName,
        int page,
        int pageSize)
    {
        try
        {
            var cacheKey = CacheKeys.GetQueryResultKey(managerId, contactTypes, searchNickName, searchRemarkName, page, pageSize);
            var result = await _redisDb.GetAsync<PageResponse<WxContactDto>>(cacheKey);

            var isHit = result != null;
            await RecordCacheHitAsync(managerId, "QueryResult", isHit);

            if (isHit)
            {
                // 异步记录热门查询
                _ = Task.Run(async () => await RecordHotQueryAsync(managerId, contactTypes, searchNickName, searchRemarkName));
            }
            else
            {
                _logger.LogInformation($"[缓存未命中] 查询结果 - ManagerId: {managerId}, Types: {string.Join(",", contactTypes)}, Page: {page}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"获取查询结果缓存失败 - ManagerId: {managerId}, Types: {string.Join(",", contactTypes)}");
            return null;
        }
    }
    
    public async Task SetQueryResultAsync(
        Guid managerId,
        WxContactType contactType,
        string? searchNickName,
        string? searchRemarkName,
        int page,
        int pageSize,
        PageResponse<WxContactDto> result,
        TimeSpan? expiry = null)
    {
        try
        {
            var cacheKey = CacheKeys.GetQueryResultKey(managerId, contactType, searchNickName, searchRemarkName, page, pageSize);
            var cacheExpiry = expiry ?? CacheKeys.GetCacheExpiry(CacheKeyType.QueryResult);

            await _redisDb.AddAsync(cacheKey, result, cacheExpiry);

            _logger.LogInformation($"[缓存写入] 查询结果 - ManagerId: {managerId}, Type: {contactType}, Page: {page}, 记录数: {result.Items.Count}, 过期时间: {cacheExpiry.TotalMinutes}分钟");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设置查询结果缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    public async Task SetQueryResultAsync(
        Guid managerId,
        List<WxContactType> contactTypes,
        string? searchNickName,
        string? searchRemarkName,
        int page,
        int pageSize,
        PageResponse<WxContactDto> result,
        TimeSpan? expiry = null)
    {
        try
        {
            var cacheKey = CacheKeys.GetQueryResultKey(managerId, contactTypes, searchNickName, searchRemarkName, page, pageSize);
            var cacheExpiry = expiry ?? CacheKeys.GetCacheExpiry(CacheKeyType.QueryResult);

            await _redisDb.AddAsync(cacheKey, result, cacheExpiry);

            _logger.LogInformation($"[缓存写入] 查询结果 - ManagerId: {managerId}, Types: {string.Join(",", contactTypes)}, Page: {page}, 记录数: {result.Items.Count}, 过期时间: {cacheExpiry.TotalMinutes}分钟");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设置查询结果缓存失败 - ManagerId: {managerId}, Types: {string.Join(",", contactTypes)}");
        }
    }
    
    #endregion
    
    #region 预加载缓存层 (15分钟过期)
    
    public async Task PreloadCommonQueriesAsync(Guid managerId, WxContactType contactType)
    {
        try
        {
            _logger.LogInformation($"[预加载] 开始预加载常用查询 - ManagerId: {managerId}, Type: {contactType}");
            
            // 获取基础数据
            var baseData = await GetBaseContactDataAsync(managerId, contactType);
            if (baseData == null || !baseData.Any())
            {
                _logger.LogWarning($"[预加载] 基础数据为空，跳过预加载 - ManagerId: {managerId}, Type: {contactType}");
                return;
            }
            
            // 预加载常用页面大小的首页数据
            var commonPageSizes = new[] { 10, 20, 50 };
            var preloadTasks = new List<Task>();
            
            foreach (var pageSize in commonPageSizes)
            {
                preloadTasks.Add(PreloadQueryResultAsync(managerId, contactType, null, null, 1, pageSize, baseData));
            }
            
            await Task.WhenAll(preloadTasks);
            _logger.LogInformation($"[预加载] 完成常用查询预加载 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"预加载常用查询失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }
    
    public async Task<PageResponse<WxContactDto>?> GetPreloadedFirstPageAsync(Guid managerId, WxContactType contactType, int pageSize)
    {
        try
        {
            var cacheKey = CacheKeys.GetPreloadFirstPageKey(managerId, contactType, pageSize);
            var result = await _redisDb.GetAsync<PageResponse<WxContactDto>>(cacheKey);
            
            var isHit = result != null;
            await RecordCacheHitAsync(managerId, "Preload", isHit);
            
            if (isHit)
            {
                _logger.LogInformation($"[缓存命中] 预加载首页 - ManagerId: {managerId}, Type: {contactType}, PageSize: {pageSize}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"获取预加载首页数据失败 - ManagerId: {managerId}, Type: {contactType}");
            return null;
        }
    }
    
    #endregion
    
    #region 缓存失效管理
    
    public async Task ClearAllContactCacheAsync(Guid managerId, WxContactType? contactType = null)
    {
        try
        {
            // 🔧 注释冗余的缓存清除开始日志 - 降低日志噪音
            // _logger.LogInformation($"[缓存清除] 开始清除所有联系人缓存 - ManagerId: {managerId}, Type: {contactType}");
            
            // 简化实现：直接清除具体的缓存键
            var contactTypes = contactType.HasValue 
                ? new[] { contactType.Value }
                : new[] { WxContactType.Contact, WxContactType.Group };
            
            var clearTasks = new List<Task>();
            
            foreach (var type in contactTypes)
            {
                // 清除基础数据缓存
                clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetBaseContactDataKey(managerId, type)));
                
                // 清除预加载缓存（常用页面大小）
                var commonPageSizes = new[] { 10, 20, 50 };
                foreach (var pageSize in commonPageSizes)
                {
                    clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetPreloadFirstPageKey(managerId, type, pageSize)));
                }
            }
            
            // 清除通用缓存
            var allContactTypes = new[] { WxContactType.Contact, WxContactType.Group };
            foreach (var type in allContactTypes)
            {
                clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetContactCountKey(managerId, type)));
            }
            clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetCacheStatsKey(managerId)));
            
            await Task.WhenAll(clearTasks);
            
            // 🔧 注释冗余的缓存清除完成日志 - 降低日志噪音
            // _logger.LogInformation($"[缓存清除] 完成清除所有联系人缓存 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"清除所有联系人缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }
    
    public async Task ClearBaseDataCacheAsync(Guid managerId, WxContactType? contactType = null)
    {
        try
        {
            _logger.LogInformation($"[缓存清除] 开始清除基础数据缓存 - ManagerId: {managerId}, Type: {contactType}");
            
            var contactTypes = contactType.HasValue 
                ? new[] { contactType.Value }
                : new[] { WxContactType.Contact, WxContactType.Group };
            
            var clearTasks = new List<Task>();
            
            foreach (var type in contactTypes)
            {
                clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetBaseContactDataKey(managerId, type)));
                // 清除对应类型的联系人数量统计
                clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetContactCountKey(managerId, type)));
            }
            
            await Task.WhenAll(clearTasks);
            
            _logger.LogInformation($"[缓存清除] 完成清除基础数据缓存 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"清除基础数据缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }
    
    public async Task ClearQueryCacheAsync(Guid managerId, WxContactType? contactType = null)
    {
        try
        {
            _logger.LogInformation($"[缓存清除] 开始清除查询结果缓存 - ManagerId: {managerId}, Type: {contactType}");

            var contactTypes = contactType.HasValue
                ? new[] { contactType.Value }
                : new[] { WxContactType.Contact, WxContactType.Group };

            var clearTasks = new List<Task>();

            foreach (var type in contactTypes)
            {
                // 清除预加载缓存
                var commonPageSizes = new[] { 10, 20, 50 };
                foreach (var pageSize in commonPageSizes)
                {
                    clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetPreloadFirstPageKey(managerId, type, pageSize)));
                }

                // 清除查询结果缓存 - 使用模式匹配删除所有相关的查询缓存
                var queryPattern = $"query_result:{managerId}:{type}:*";
                try
                {
                    // 获取所有匹配的键
                    var keys = await _redisDb.SearchKeysAsync(queryPattern);
                    if (keys?.Any() == true)
                    {
                        // 批量删除匹配的键
                        clearTasks.Add(_redisDb.RemoveAllAsync(keys.ToArray()));
                        _logger.LogInformation($"[缓存清除] 找到 {keys.Count()} 个查询结果缓存键需要清除");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"清除查询结果缓存时出现警告 - Pattern: {queryPattern}");
                    // 如果模式匹配失败，尝试清除常见的查询组合
                    await ClearCommonQueryCombinations(managerId, type, clearTasks);
                }
            }

            await Task.WhenAll(clearTasks);

            _logger.LogInformation($"[缓存清除] 完成清除查询结果缓存 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"清除查询结果缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    private async Task ClearCommonQueryCombinations(Guid managerId, WxContactType contactType, List<Task> clearTasks)
    {
        // 清除常见的查询组合
        var commonSearchTerms = new[] { "", "test", "admin", "user" }; // 常见搜索词
        var commonPages = new[] { 1, 2, 3 }; // 常见页码
        var commonPageSizes = new[] { 10, 20, 50 }; // 常见页面大小

        foreach (var nickName in commonSearchTerms)
        {
            foreach (var remarkName in commonSearchTerms)
            {
                foreach (var page in commonPages)
                {
                    foreach (var pageSize in commonPageSizes)
                    {
                        var key = CacheKeys.GetQueryResultKey(managerId, contactType, nickName, remarkName, page, pageSize);
                        clearTasks.Add(_redisDb.RemoveAsync(key));
                    }
                }
            }
        }
    }
    
    public async Task SmartRefreshCacheAsync(Guid managerId, WxContactType contactType, List<WxContactDto> updatedContacts)
    {
        try
        {
            _logger.LogInformation($"[智能刷新] 开始智能刷新缓存 - ManagerId: {managerId}, Type: {contactType}, 更新数量: {updatedContacts.Count}");

            // 更新基础数据缓存
            await SetBaseContactDataAsync(managerId, contactType, updatedContacts);

            // 清除查询结果缓存（因为数据已变化）
            await ClearQueryCacheAsync(managerId, contactType);

            // 异步预加载常用查询
            _ = Task.Run(async () => await PreloadCommonQueriesAsync(managerId, contactType));

            _logger.LogInformation($"[智能刷新] 完成智能刷新缓存 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"智能刷新缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    /// <summary>
    /// 清除单个联系人相关的缓存（优化版本，使用精细化清除策略）
    /// </summary>
    public async Task ClearContactCacheAsync(Guid managerId, WxContactType contactType, Guid? contactId = null)
    {
        try
        {
            _logger.LogInformation($"[缓存清除] 开始清除联系人缓存（优化版本） - ManagerId: {managerId}, Type: {contactType}, ContactId: {contactId}");

            if (contactId.HasValue)
            {
                // 如果指定了联系人ID，使用精细化清除：只清除查询缓存和特定联系人缓存
                await ClearCacheByScope(managerId, contactType, CacheClearScope.QueryOnly);
                await ClearCacheByScope(managerId, contactType, CacheClearScope.ContactOnly, contactId);
            }
            else
            {
                // 如果没有指定联系人ID，只清除查询缓存，保留基础数据避免频繁重建
                await ClearCacheByScope(managerId, contactType, CacheClearScope.QueryOnly);
            }

            _logger.LogInformation($"[缓存清除] 完成清除联系人缓存（优化版本） - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"清除联系人缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    /// <summary>
    /// 强制清除所有联系人相关缓存（用于删除操作后）
    /// </summary>
    public async Task ForceCleanAllContactCacheAsync(Guid managerId, WxContactType contactType)
    {
        try
        {
            _logger.LogInformation($"[强制缓存清除] 开始强制清除所有联系人缓存 - ManagerId: {managerId}, Type: {contactType}");

            var clearTasks = new List<Task>();

            // 1. 清除基础数据缓存
            var baseDataKey = CacheKeys.GetBaseContactDataKey(managerId, contactType);
            clearTasks.Add(_redisDb.RemoveAsync(baseDataKey));

            // 2. 清除联系人统计缓存
            var countKey = CacheKeys.GetContactCountKey(managerId, contactType);
            clearTasks.Add(_redisDb.RemoveAsync(countKey));

            // 3. 清除热门查询缓存
            var hotQueryKey = CacheKeys.GetHotQueryKey(managerId, contactType);
            clearTasks.Add(_redisDb.RemoveAsync(hotQueryKey));

            // 4. 清除缓存统计
            var statsKey = CacheKeys.GetCacheStatsKey(managerId);
            clearTasks.Add(_redisDb.RemoveAsync(statsKey));

            // 5. 清除预加载缓存
            var commonPageSizes = new[] { 10, 20, 50 };
            foreach (var pageSize in commonPageSizes)
            {
                clearTasks.Add(_redisDb.RemoveAsync(CacheKeys.GetPreloadFirstPageKey(managerId, contactType, pageSize)));
            }

            // 6. 尝试使用模式匹配清除所有查询结果缓存
            try
            {
                var queryPattern = $"query_result:{managerId}:{contactType}:*";
                var keys = await _redisDb.SearchKeysAsync(queryPattern);
                if (keys?.Any() == true)
                {
                    clearTasks.Add(_redisDb.RemoveAllAsync(keys.ToArray()));
                    _logger.LogInformation($"[强制缓存清除] 找到 {keys.Count()} 个查询结果缓存键");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"模式匹配清除缓存时出现警告，将使用备用方案");
                // 备用方案：清除常见查询组合
                await ClearCommonQueryCombinations(managerId, contactType, clearTasks);
            }

            // 7. 执行所有清除任务
            await Task.WhenAll(clearTasks);

            _logger.LogInformation($"[强制缓存清除] 完成强制清除所有联系人缓存 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"强制清除联系人缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    /// <summary>
    /// 使相关缓存失效，强制下次查询重新加载数据
    /// </summary>
    public async Task InvalidateRelatedCacheAsync(Guid managerId, WxContactType contactType)
    {
        try
        {
            _logger.LogInformation($"[缓存失效] 开始使相关缓存失效 - ManagerId: {managerId}, Type: {contactType}");

            // 清除所有相关的缓存
            await ClearContactCacheAsync(managerId, contactType);

            // 清除热门查询缓存
            var hotQueryKey = CacheKeys.GetHotQueryKey(managerId, contactType);
            await _redisDb.RemoveAsync(hotQueryKey);

            // 清除缓存统计
            var statsKey = CacheKeys.GetCacheStatsKey(managerId);
            await _redisDb.RemoveAsync(statsKey);

            _logger.LogInformation($"[缓存失效] 完成使相关缓存失效 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"使相关缓存失效失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    /// <summary>
    /// 统一的缓存清除方法，支持精细化清除策略
    /// </summary>
    /// <param name="managerId">微信管理器ID</param>
    /// <param name="contactType">联系人类型</param>
    /// <param name="scope">清除范围</param>
    /// <param name="contactId">联系人ID（ContactOnly模式时需要）</param>
    public async Task ClearCacheByScope(Guid managerId, WxContactType contactType, 
        CacheClearScope scope, Guid? contactId = null)
    {
        try
        {
            _logger.LogInformation($"[缓存清除] 开始按范围清除缓存 - ManagerId: {managerId}, Type: {contactType}, Scope: {scope}, ContactId: {contactId}");

            var clearTasks = new List<Task>();
            
            switch (scope)
            {
                case CacheClearScope.ContactOnly:
                    if (contactId.HasValue)
                    {
                        // 只清除特定联系人的详细信息缓存
                        var contactKey = contactType == WxContactType.Contact 
                            ? CacheKeys.GetContactKey(managerId, contactId.Value.ToString())
                            : CacheKeys.GetGroupKey(managerId, contactId.Value.ToString());
                        clearTasks.Add(_redisDb.RemoveAsync(contactKey));
                        
                        _logger.LogDebug($"[缓存清除] 清除单个联系人缓存 - Key: {contactKey}");
                    }
                    break;
                    
                case CacheClearScope.QueryOnly:
                    // 只清除查询结果缓存，保留基础数据
                    await ClearQueryCacheAsync(managerId, contactType);
                    _logger.LogDebug($"[缓存清除] 清除查询缓存完成");
                    break;
                    
                case CacheClearScope.BaseDataOnly:
                    // 只清除基础数据缓存
                    var baseDataKey = CacheKeys.GetBaseContactDataKey(managerId, contactType);
                    clearTasks.Add(_redisDb.RemoveAsync(baseDataKey));
                    
                    // 清除对应类型的联系人数量统计
                    var countKey = CacheKeys.GetContactCountKey(managerId, contactType);
                    clearTasks.Add(_redisDb.RemoveAsync(countKey));
                    
                    _logger.LogDebug($"[缓存清除] 清除基础数据缓存 - BaseDataKey: {baseDataKey}, CountKey: {countKey}");
                    break;
                    
                case CacheClearScope.All:
                    // 清除所有相关缓存
                    await ForceCleanAllContactCacheAsync(managerId, contactType);
                    _logger.LogDebug($"[缓存清除] 强制清除所有缓存完成");
                    return; // ForceCleanAllContactCacheAsync已经处理了所有清除操作
            }
            
            // 执行清除任务（除了All模式，因为All模式已经在switch中处理）
            if (clearTasks.Count > 0)
            {
                await Task.WhenAll(clearTasks);
            }
            
            _logger.LogInformation($"[缓存清除] 完成按范围清除缓存 - ManagerId: {managerId}, Type: {contactType}, Scope: {scope}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"按范围清除缓存失败 - ManagerId: {managerId}, Type: {contactType}, Scope: {scope}");
            throw new InvalidOperationException($"缓存清除失败: {ex.Message}", ex);
        }
    }
    
    #endregion
    
    #region 缓存监控和统计
    
    public async Task<CacheStatistics> GetCacheStatisticsAsync(Guid managerId)
    {
        try
        {
            var cacheKey = CacheKeys.GetCacheStatsKey(managerId);
            var stats = await _redisDb.GetAsync<CacheStatistics>(cacheKey);
            
            if (stats == null)
            {
                stats = new CacheStatistics
                {
                    ManagerId = managerId,
                    LastUpdated = DateTime.UtcNow
                };
            }
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"获取缓存统计失败 - ManagerId: {managerId}");
            return new CacheStatistics { ManagerId = managerId, LastUpdated = DateTime.UtcNow };
        }
    }
    
    public async Task RecordCacheHitAsync(Guid managerId, string cacheType, bool isHit)
    {
        try
        {
            var stats = await GetCacheStatisticsAsync(managerId);
            
            switch (cacheType)
            {
                case "BaseData":
                    if (isHit) stats.BaseDataHits++; else stats.BaseDataMisses++;
                    break;
                case "QueryResult":
                    if (isHit) stats.QueryResultHits++; else stats.QueryResultMisses++;
                    break;
                case "Preload":
                    if (isHit) stats.PreloadHits++; else stats.PreloadMisses++;
                    break;
            }
            
            // 计算总体命中率
            var totalHits = stats.BaseDataHits + stats.QueryResultHits + stats.PreloadHits;
            var totalRequests = totalHits + stats.BaseDataMisses + stats.QueryResultMisses + stats.PreloadMisses;
            stats.OverallHitRate = totalRequests > 0 ? (double)totalHits / totalRequests : 0;
            stats.LastUpdated = DateTime.UtcNow;
            
            // 异步保存统计信息
            var cacheKey = CacheKeys.GetCacheStatsKey(managerId);
            var expiry = CacheKeys.GetCacheExpiry(CacheKeyType.Statistics);
            await _redisDb.AddAsync(cacheKey, stats, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"记录缓存命中统计失败 - ManagerId: {managerId}, Type: {cacheType}");
        }
    }
    
    #endregion
    
    #region 私有辅助方法
    
    /// <summary>
    /// 根据模式清除缓存（简化版本，适配现有的Redis实现）
    /// </summary>
    private async Task ClearCacheByPatternAsync(string pattern)
    {
        try
        {
            // 简化实现：仅清除常用的缓存键，避免复杂的模式匹配
            // 这里可以根据实际需要添加更精细的实现
            _logger.LogInformation($"[缓存清除] 简化清除模式: {pattern}");
            
            // TODO: 在生产环境中，可以实现更精细的模式匹配和批量删除
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"按模式清除缓存失败 - Pattern: {pattern}");
        }
    }
    
    /// <summary>
    /// 更新联系人数量统计
    /// </summary>
    private async Task UpdateContactCountsAsync(Guid managerId, WxContactType contactType, int count)
    {
        try
        {
            var counts = await GetContactCountsAsync(managerId) ?? new Dictionary<WxContactType, int>();
            counts[contactType] = count;
            await SetContactCountsAsync(managerId, counts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"更新联系人数量统计失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }
    
    /// <summary>
    /// 预加载首页数据
    /// </summary>
    private async Task PreloadFirstPageDataAsync(Guid managerId, WxContactType contactType, List<WxContactDto> allContacts)
    {
        try
        {
            var commonPageSizes = new[] { 10, 20, 50 };
            
            foreach (var pageSize in commonPageSizes)
            {
                var firstPageData = allContacts.Take(pageSize).ToList();
                var pageResponse = new PageResponse<WxContactDto>
                {
                    Items = firstPageData,
                    TotalCount = allContacts.Count,
                    Page = 1,
                    PageSize = pageSize
                };
                
                var cacheKey = CacheKeys.GetPreloadFirstPageKey(managerId, contactType, pageSize);
                var expiry = CacheKeys.GetCacheExpiry(CacheKeyType.Preload);
                
                await _redisDb.AddAsync(cacheKey, pageResponse, expiry);
            }
            
            _logger.LogInformation($"[预加载] 完成首页数据预加载 - ManagerId: {managerId}, Type: {contactType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"预加载首页数据失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }
    
    /// <summary>
    /// 预加载查询结果
    /// </summary>
    private async Task PreloadQueryResultAsync(
        Guid managerId, 
        WxContactType contactType, 
        string? searchNickName, 
        string? searchRemarkName, 
        int page, 
        int pageSize, 
        List<WxContactDto> baseData)
    {
        try
        {
            // 模拟查询逻辑
            var filteredData = baseData.AsQueryable();
            
            if (!string.IsNullOrEmpty(searchNickName))
            {
                filteredData = filteredData.Where(c => c.NickName != null && c.NickName.Contains(searchNickName));
            }
            
            if (!string.IsNullOrEmpty(searchRemarkName))
            {
                filteredData = filteredData.Where(c => c.Remark != null && c.Remark.Contains(searchRemarkName));
            }
            
            var totalCount = filteredData.Count();
            // 🔧 修复：为内存查询添加排序，确保分页结果一致性
            var pageData = filteredData
                .OrderBy(x => x.NickName).ThenBy(x => x.Id)
                .Skip((page - 1) * pageSize).Take(pageSize).ToList();
            
            var pageResponse = new PageResponse<WxContactDto>
            {
                Items = pageData,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
            
            await SetQueryResultAsync(managerId, contactType, searchNickName, searchRemarkName, page, pageSize, pageResponse, CacheKeys.GetCacheExpiry(CacheKeyType.Preload));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"预加载查询结果失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }
    
    /// <summary>
    /// 记录热门查询
    /// </summary>
    private async Task RecordHotQueryAsync(Guid managerId, WxContactType contactType, string? searchNickName, string? searchRemarkName)
    {
        try
        {
            // 这里可以实现热门查询统计逻辑
            // 暂时只记录日志
            if (!string.IsNullOrEmpty(searchNickName) || !string.IsNullOrEmpty(searchRemarkName))
            {
                _logger.LogInformation($"[热门查询] ManagerId: {managerId}, Type: {contactType}, Nick: {searchNickName}, Remark: {searchRemarkName}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"记录热门查询失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    /// <summary>
    /// 记录热门查询 - 支持多个联系人类型
    /// </summary>
    private async Task RecordHotQueryAsync(Guid managerId, List<WxContactType> contactTypes, string? searchNickName, string? searchRemarkName)
    {
        try
        {
            // 这里可以实现热门查询统计逻辑
            // 暂时只记录日志
            if (!string.IsNullOrEmpty(searchNickName) || !string.IsNullOrEmpty(searchRemarkName))
            {
                _logger.LogInformation($"[热门查询] ManagerId: {managerId}, Types: {string.Join(",", contactTypes)}, Nick: {searchNickName}, Remark: {searchRemarkName}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"记录热门查询失败 - ManagerId: {managerId}, Types: {string.Join(",", contactTypes)}");
        }
    }

    /// <summary>
    /// 验证缓存是否已清理
    /// </summary>
    public async Task<bool> VerifyCacheClearedAsync(Guid managerId, WxContactType contactType)
    {
        try
        {
            // 检查基础数据缓存
            var baseDataKey = CacheKeys.GetBaseContactDataKey(managerId, contactType);
            var hasBaseData = await _redisDb.ExistsAsync(baseDataKey);

            // 检查联系人数量统计缓存
            var countKey = CacheKeys.GetContactCountKey(managerId, contactType);
            var hasCountData = await _redisDb.ExistsAsync(countKey);

            // 检查查询结果缓存（简化检查，只检查常见的查询模式）
            var commonQueryKeys = new[]
            {
                CacheKeys.GetQueryResultKey(managerId, contactType, null, null, 1, 20),
                CacheKeys.GetQueryResultKey(managerId, contactType, "", "", 1, 20)
            };

            var hasQueryData = false;
            foreach (var queryKey in commonQueryKeys)
            {
                if (await _redisDb.ExistsAsync(queryKey))
                {
                    hasQueryData = true;
                    break;
                }
            }

            // 如果所有检查的缓存都不存在，认为已清理
            var isCleared = !hasBaseData && !hasCountData && !hasQueryData;

            _logger.LogDebug("缓存清理验证 - ManagerId: {ManagerId}, Type: {Type}, 基础数据: {BaseData}, 统计数据: {CountData}, 查询数据: {QueryData}, 已清理: {Cleared}",
                managerId, contactType, hasBaseData, hasCountData, hasQueryData, isCleared);

            return isCleared;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证缓存清理状态失败 - ManagerId: {ManagerId}, Type: {Type}", managerId, contactType);
            return false; // 出错时保守返回false
        }
    }

    #endregion
}