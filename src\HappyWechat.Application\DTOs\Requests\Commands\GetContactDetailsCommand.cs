using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 获取联系人详情命令
/// </summary>
public class GetContactDetailsCommand
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public required Guid WxManagerId { get; set; }

    /// <summary>
    /// 要获取的联系人类型列表
    /// 1 = 个人微信好友, 5 = 企业微信联系人
    /// </summary>
    public List<WxContactListType> ListTypes { get; set; } = new();

    /// <summary>
    /// 是否强制重新获取（忽略缓存）
    /// </summary>
    public bool ForceRefresh { get; set; } = false;

    /// <summary>
    /// 上次同步时间
    /// </summary>
    public DateTime? LastSyncTime { get; set; }
}
