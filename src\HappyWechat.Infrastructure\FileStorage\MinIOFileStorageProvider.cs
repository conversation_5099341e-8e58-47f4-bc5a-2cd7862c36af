using HappyWechat.Application.DTOs.FileStorage;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.ValueObjects.Enums;
using Microsoft.Extensions.Logging;
using Minio;
using Minio.DataModel.Args;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats;
using DTOFileDownloadResult = HappyWechat.Application.DTOs.FileStorage.FileDownloadResult;
using HappyWechat.Infrastructure.FileStorage;

namespace HappyWechat.Infrastructure.FileStorage;

/// <summary>
/// MinIO文件存储提供者
/// </summary>
public class MinIOFileStorageProvider : IFileStorageService, IFileStorageProvider
{
    private readonly ISystemConfigManager _systemConfigManager;
    private readonly ILogger<MinIOFileStorageProvider> _logger;

    public MinIOFileStorageProvider(
        ISystemConfigManager systemConfigManager,
        ILogger<MinIOFileStorageProvider> logger)
    {
        _systemConfigManager = systemConfigManager;
        _logger = logger;
    }

    public async Task<FileUploadResult> UploadFileAsync(Stream fileStream, string fileName, MaterialType materialType, Guid userId)
    {
        try
        {
            var filePath = $"{materialType}/{userId}/{fileName}";
            var result = await UploadFileInternalAsync(fileStream, filePath);
            
            return new FileUploadResult
            {
                Success = !string.IsNullOrEmpty(result),
                FilePath = result ?? string.Empty,
                FileSize = fileStream.Length,
                FileExtension = Path.GetExtension(fileName),
                OriginalFileName = fileName,
                MimeType = GetContentType(fileName)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO上传文件失败: {FileName}", fileName);
            return new FileUploadResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
    
    private async Task<string> UploadFileInternalAsync(Stream fileStream, string filePath)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            // 确保存储桶存在
            await EnsureBucketExistsAsync(minioClient, config.MinIO.DefaultBucket);

            // 获取文件信息（BrowserFileStream不支持Position设置）
            var fileSize = fileStream.Length;
            var contentType = GetContentType(filePath);

            // 上传文件到MinIO
            var putObjectArgs = new PutObjectArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithObject(filePath)
                .WithStreamData(fileStream)
                .WithObjectSize(fileSize)
                .WithContentType(contentType);

            await minioClient.PutObjectAsync(putObjectArgs);

            // 🔧 注释冗余的MinIO上传文件成功日志 - 降低日志噪音
            // _logger.LogInformation("MinIO上传文件成功: {FilePath}", filePath);

            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO上传文件失败: {FilePath} - {ErrorMessage}", filePath, ex.Message);
            return string.Empty;
        }
    }

    public async Task<DTOFileDownloadResult> DownloadFileAsync(string filePath)
    {
        try
        {
            var stream = await DownloadFileInternalAsync(filePath);
            if (stream != null)
            {
                return new DTOFileDownloadResult
                {
                    Success = true,
                    FileName = Path.GetFileName(filePath),
                    FileStream = stream,
                    MimeType = GetContentType(filePath)
                };
            }
            return new DTOFileDownloadResult
            {
                Success = false,
                ErrorMessage = "文件不存在"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO下载文件失败: {FilePath}", filePath);
            return new DTOFileDownloadResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
    
    private async Task<Stream?> DownloadFileInternalAsync(string filePath)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            var memoryStream = new MemoryStream();

            var getObjectArgs = new GetObjectArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithObject(filePath)
                .WithCallbackStream(stream => stream.CopyTo(memoryStream));

            await minioClient.GetObjectAsync(getObjectArgs);

            memoryStream.Position = 0;

            _logger.LogInformation("MinIO下载文件成功: {FilePath}", filePath);

            return memoryStream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO下载文件失败: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> DeleteFileAsync(string filePath)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            var removeObjectArgs = new RemoveObjectArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithObject(filePath);

            await minioClient.RemoveObjectAsync(removeObjectArgs);

            // 🔧 注释冗余的MinIO删除文件成功日志 - 降低日志噪音
            // _logger.LogInformation("MinIO删除文件成功: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO删除文件失败: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<string?> GetFileUrlAsync(string filePath, int? expirationMinutes = null)
    {
        try
        {
            var expiry = expirationMinutes.HasValue ? TimeSpan.FromMinutes(expirationMinutes.Value) : TimeSpan.FromHours(24);
            return await GeneratePresignedUrlAsync(filePath, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO获取文件URL失败: {FilePath}", filePath);
            return null;
        }
    }
    
    public async Task<string> GeneratePresignedUrlAsync(string filePath, TimeSpan expiry)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            // 生成预签名URL
            var presignedGetObjectArgs = new PresignedGetObjectArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithObject(filePath)
                .WithExpiry((int)expiry.TotalSeconds);

            var url = await minioClient.PresignedGetObjectAsync(presignedGetObjectArgs);

            // 🔧 注释冗余的MinIO获取文件URL日志 - 降低日志噪音
            // _logger.LogInformation("MinIO获取文件URL: {FilePath} -> {Url}", filePath, url);

            return url ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO获取文件URL失败: {FilePath}", filePath);
            return string.Empty;
        }
    }

    public async Task<bool> FileExistsAsync(string filePath)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            var statObjectArgs = new StatObjectArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithObject(filePath);

            await minioClient.StatObjectAsync(statObjectArgs);

            _logger.LogInformation("MinIO文件存在: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("MinIO文件不存在: {FilePath}, 错误: {Error}", filePath, ex.Message);
            return false;
        }
    }

    public async Task<Application.DTOs.FileStorage.FileInfo?> GetFileInfoAsync(string filePath)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            var statObjectArgs = new StatObjectArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithObject(filePath);

            var stat = await minioClient.StatObjectAsync(statObjectArgs);

            return new Application.DTOs.FileStorage.FileInfo
            {
                Size = stat.Size,
                ContentType = stat.ContentType,
                LastModified = stat.LastModified,
                ETag = stat.ETag
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO获取文件信息失败: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<List<string>> ListFilesAsync(string directory, string? pattern = null)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            var listObjectsArgs = new ListObjectsArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithPrefix(directory)
                .WithRecursive(true);

            var files = new List<string>();
            await foreach (var item in minioClient.ListObjectsEnumAsync(listObjectsArgs))
            {
                if (string.IsNullOrEmpty(pattern) || item.Key.Contains(pattern))
                {
                    files.Add(item.Key);
                }
            }

            return files;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO列出文件失败: {Directory}", directory);
            return new List<string>();
        }
    }

    public async Task<bool> CopyFileAsync(string sourcePath, string destinationPath)
    {
        try
        {
            var config = await _systemConfigManager.GetFileStorageConfigAsync();
            var minioClient = CreateMinioClient(config.MinIO);

            var copyObjectArgs = new CopyObjectArgs()
                .WithBucket(config.MinIO.DefaultBucket)
                .WithObject(destinationPath)
                .WithCopyObjectSource(new CopySourceObjectArgs()
                    .WithBucket(config.MinIO.DefaultBucket)
                    .WithObject(sourcePath));

            await minioClient.CopyObjectAsync(copyObjectArgs);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO复制文件失败: {SourcePath} -> {DestinationPath}", sourcePath, destinationPath);
            return false;
        }
    }

    public async Task<string?> GenerateThumbnailAsync(string originalFilePath, MaterialType materialType)
    {
        if (materialType != MaterialType.Image)
        {
            return null;
        }

        var fileExtension = Path.GetExtension(originalFilePath).ToLowerInvariant();
        if (!IsImageFormatSupported(fileExtension))
        {
            _logger.LogWarning("不支持的图片格式: {FilePath}", originalFilePath);
            return null;
        }

        return await GenerateThumbnailWithRetryAsync(originalFilePath, maxRetries: 1);
    }

    private async Task<string?> GenerateThumbnailWithRetryAsync(string originalFilePath, int maxRetries)
    {
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                var config = await _systemConfigManager.GetFileStorageConfigAsync();
                var minioClient = CreateMinioClient(config.MinIO);
                var thumbnailPath = originalFilePath.Replace(Path.GetExtension(originalFilePath), "_thumb.jpg");

                using var originalStream = new MemoryStream();
                var getObjectArgs = new GetObjectArgs()
                    .WithBucket(config.MinIO.DefaultBucket)
                    .WithObject(originalFilePath)
                    .WithCallbackStream(stream => stream.CopyTo(originalStream));

                await minioClient.GetObjectAsync(getObjectArgs);
                originalStream.Position = 0;

                if (!ValidateImageMagicNumbers(originalStream, Path.GetExtension(originalFilePath).ToLowerInvariant()))
                {
                    _logger.LogWarning("图片文件格式验证失败，跳过缩略图生成: {FilePath}", originalFilePath);
                    return null;
                }

                originalStream.Position = 0;

                using var image = await LoadImageWithSpecificHandlingAsync(originalStream);
                using var thumbnailStream = new MemoryStream();

                var maxSize = 300;
                var ratio = Math.Min((double)maxSize / image.Width, (double)maxSize / image.Height);
                var newWidth = (int)(image.Width * ratio);
                var newHeight = (int)(image.Height * ratio);

                image.Mutate(x => x.Resize(newWidth, newHeight));
                await image.SaveAsJpegAsync(thumbnailStream, new JpegEncoder { Quality = 85 });
                thumbnailStream.Position = 0;

                var putThumbnailArgs = new PutObjectArgs()
                    .WithBucket(config.MinIO.DefaultBucket)
                    .WithObject(thumbnailPath)
                    .WithStreamData(thumbnailStream)
                    .WithObjectSize(thumbnailStream.Length)
                    .WithContentType("image/jpeg");

                await minioClient.PutObjectAsync(putThumbnailArgs);
                _logger.LogDebug("缩略图生成成功: {OriginalPath} -> {ThumbnailPath}", originalFilePath, thumbnailPath);
                return thumbnailPath;
            }
            catch (UnknownImageFormatException ex)
            {
                _logger.LogError(ex, "无法识别的图片格式: {FilePath}, 尝试 {Attempt}/{MaxRetries}", originalFilePath, attempt, maxRetries);
                if (attempt == maxRetries) return null;
            }
            catch (InvalidImageContentException ex)
            {
                _logger.LogError(ex, "图片内容无效: {FilePath}, 尝试 {Attempt}/{MaxRetries}", originalFilePath, attempt, maxRetries);
                if (attempt == maxRetries) return null;
            }
            catch (OutOfMemoryException ex)
            {
                _logger.LogError(ex, "处理图片时内存不足: {FilePath}, 尝试 {Attempt}/{MaxRetries}", originalFilePath, attempt, maxRetries);
                if (attempt == maxRetries) return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "缩略图生成失败: {FilePath}, 尝试 {Attempt}/{MaxRetries}, 异常类型: {ExceptionType}", 
                    originalFilePath, attempt, maxRetries, ex.GetType().Name);
                if (attempt == maxRetries) return null;
                
                await Task.Delay(TimeSpan.FromMilliseconds(100 * attempt));
            }
        }
        
        return null;
    }

    private async Task<Image> LoadImageWithSpecificHandlingAsync(Stream stream)
    {
        try
        {
            return await Image.LoadAsync(stream);
        }
        catch (UnknownImageFormatException)
        {
            stream.Position = 0;
            var buffer = new byte[8];
            await stream.ReadAsync(buffer, 0, 8);
            stream.Position = 0;
            
            var hexString = Convert.ToHexString(buffer);
            _logger.LogWarning("图片格式识别失败，文件头: {FileHeader}", hexString);
            throw;
        }
    }

    private bool IsImageFormatSupported(string extension)
    {
        return extension switch
        {
            ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => true,
            _ => false
        };
    }

    private bool ValidateImageMagicNumbers(Stream stream, string extension)
    {
        var buffer = new byte[8];
        var originalPosition = stream.Position;
        stream.Position = 0;
        var bytesRead = stream.Read(buffer, 0, 8);
        stream.Position = originalPosition;

        if (bytesRead < 4)
        {
            _logger.LogWarning("文件太小，无法读取魔数: 只读取了 {BytesRead} 字节", bytesRead);
            return false;
        }

        var actualHex = Convert.ToHexString(buffer.Take(bytesRead).ToArray());
        
        var result = extension switch
        {
            // PNG: 89 50 4E 47 0D 0A 1A 0A - 完整的8字节魔数验证
            ".png" => bytesRead >= 8 &&
                      buffer[0] == 0x89 && buffer[1] == 0x50 && buffer[2] == 0x4E && buffer[3] == 0x47 &&
                      buffer[4] == 0x0D && buffer[5] == 0x0A && buffer[6] == 0x1A && buffer[7] == 0x0A,
            // JPEG: FF D8 FF
            ".jpg" or ".jpeg" => bytesRead >= 3 && buffer[0] == 0xFF && buffer[1] == 0xD8 && buffer[2] == 0xFF,
            // GIF: 47 49 46 38 (GIF8)
            ".gif" => bytesRead >= 4 && buffer[0] == 0x47 && buffer[1] == 0x49 && buffer[2] == 0x46 && buffer[3] == 0x38,
            // BMP: 42 4D (BM)
            ".bmp" => bytesRead >= 2 && buffer[0] == 0x42 && buffer[1] == 0x4D,
            // WebP: 52 49 46 46 (RIFF)
            ".webp" => bytesRead >= 4 && buffer[0] == 0x52 && buffer[1] == 0x49 && buffer[2] == 0x46 && buffer[3] == 0x46,
            // 其他格式默认通过
            _ => true
        };

        if (!result)
        {
            _logger.LogDebug("文件魔数验证失败 - Extension: {Extension}, ActualHex: {ActualHex}", extension, actualHex);
        }

        return result;
    }

    /// <summary>
    /// 创建MinIO客户端
    /// </summary>
    private IMinioClient CreateMinioClient(MinIOConfigDto config)
    {
        var minioClient = new MinioClient()
            .WithEndpoint(config.Endpoint)
            .WithCredentials(config.AccessKey, config.SecretKey);

        if (config.UseSSL)
        {
            minioClient = minioClient.WithSSL();
        }

        return minioClient.Build();
    }

    /// <summary>
    /// 确保存储桶存在
    /// </summary>
    private async Task EnsureBucketExistsAsync(IMinioClient minioClient, string bucketName)
    {
        var bucketExistsArgs = new BucketExistsArgs().WithBucket(bucketName);
        var bucketExists = await minioClient.BucketExistsAsync(bucketExistsArgs);

        if (!bucketExists)
        {
            var makeBucketArgs = new MakeBucketArgs().WithBucket(bucketName);
            await minioClient.MakeBucketAsync(makeBucketArgs);
            _logger.LogInformation("创建MinIO存储桶: {BucketName}", bucketName);
        }
    }

    /// <summary>
    /// 根据文件扩展名获取Content-Type
    /// </summary>
    private string GetContentType(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".webp" => "image/webp",
            ".svg" => "image/svg+xml",
            ".mp4" => "video/mp4",
            ".avi" => "video/avi",
            ".mov" => "video/quicktime",
            ".wmv" => "video/x-ms-wmv",
            ".flv" => "video/x-flv",
            ".mp3" => "audio/mpeg",
            ".wav" => "audio/wav",
            ".ogg" => "audio/ogg",
            ".m4a" => "audio/mp4",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".ppt" => "application/vnd.ms-powerpoint",
            ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ".txt" => "text/plain",
            ".zip" => "application/zip",
            ".rar" => "application/x-rar-compressed",
            ".7z" => "application/x-7z-compressed",
            _ => "application/octet-stream"
        };
    }

    // IFileStorageProvider explicit implementations
    async Task<FileUploadResult> IFileStorageProvider.UploadFileAsync(Stream fileStream, string filePath)
    {
        var result = await UploadFileInternalAsync(fileStream, filePath);
        return new FileUploadResult
        {
            Success = !string.IsNullOrEmpty(result),
            FilePath = result ?? string.Empty,
            FileSize = fileStream.Length,
            MimeType = GetContentType(filePath)
        };
    }

    Task<DTOFileDownloadResult> IFileStorageProvider.DownloadFileAsync(string filePath)
    {
        return DownloadFileAsync(filePath);
    }

    Task<string?> IFileStorageProvider.GetFileUrlAsync(string filePath, int expirationMinutes)
    {
        return GetFileUrlAsync(filePath, expirationMinutes);
    }
}
