using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 获取群组详情命令
/// </summary>
public class GetGroupDetailsCommand
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public required Guid WxManagerId { get; set; }

    /// <summary>
    /// 是否强制重新获取（忽略缓存）
    /// </summary>
    public bool ForceRefresh { get; set; } = false;

    /// <summary>
    /// 是否同时获取群成员信息
    /// </summary>
    public bool IncludeMembers { get; set; } = true;

    /// <summary>
    /// 单次处理的最大群组数量（防止单次处理过多数据）
    /// </summary>
    public int MaxGroupsPerBatch { get; set; } = 50;

    /// <summary>
    /// API调用间隔范围（毫秒）
    /// </summary>
    public int MinIntervalMs { get; set; } = 300;
    public int MaxIntervalMs { get; set; } = 1500;

    /// <summary>
    /// 上次同步时间
    /// </summary>
    public DateTime? LastSyncTime { get; set; }
}