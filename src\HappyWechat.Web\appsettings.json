{
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://*:5215"
      }
    }
  },

  "MessageDeduplication": {
    "Enabled": true,
    "TimeWindowMinutes": 5,
    "CacheKeyPrefix": "msg_dedup"
  },

  "RedisQueue": {
    "QueuePrefix": "hw:queue:",
    "DelayedQueuePrefix": "hw:delayed:",
    "ProgressPrefix": "hw:progress:",
    "MonitoringPrefix": "hw:monitor:",
    "KeyPrefix": "hw",
    "DefaultMessageTtl": 86400,
    "DefaultQueueTtl": 3600,
    "DelayedMessageCheckInterval": 1000,
    "DelayedMessageScanInterval": 1500,
    "MonitoringInterval": 30000,
    "MonitorInterval": 10,
    "MaxRetryCount": 1,
    "BatchSize": 10,
    "ConsumerConcurrency": 5,
    "MessageProcessTimeout": 60,
    "DeadLetterRetentionHours": 24,
    "EnableMonitoring": true,
    "EnableDelayedMessages": true,
    "Queues": {
      "WxCallbackQueue": "wx:callback",
      "AiMessageQueue": "ai:message",
      "ContactSyncQueue": "contact:sync",
      "GroupSyncQueue": "group:sync",
      "FileSendQueue": "file:send",
      "DeadLetterQueue": "dead:letter"
    },
    "MessageInterval": {
      "SendMessageMinInterval": 1500,
      "SendMessageMaxInterval": 2000,
      "ContactOperationMinInterval": 300,
      "ContactOperationMaxInterval": 1500
    },
    "ContactSync": {
      "BatchGetContactCount": 20,
      "SyncTimeoutMinutes": 30,
      "ProgressUpdateInterval": 2
    },
    "GroupSync": {
      "SyncTimeoutMinutes": 60,
      "ProgressUpdateInterval": 3,
      "MemberFetchDelay": 1000
    }
  },

  "UnifiedSystemHealth": {
    "HealthCheckIntervalSeconds": 30,
    "DatabaseTimeoutSeconds": 10,
    "RedisTimeoutSeconds": 5,
    "EnableDetailedMetrics": true,
    "EnablePerformanceMonitoring": true
  },

  "Notification": {
    "Enabled": true,
    "MaxRetryAttempts": 3,
    "NotificationTimeoutSeconds": 30,
    "MaxBatchSize": 100,

    "RetryPolicy": {
      "EnableExponentialBackoff": true,
      "BaseDelayMs": 1000,
      "MaxDelayMs": 60000,
      "BackoffMultiplier": 2.0,
      "JitterFactor": 0.1
    },

    "Monitoring": {
      "EnableHealthCheck": true,
      "HealthCheckIntervalSeconds": 30,
      "EnablePerformanceMonitoring": true,
      "MetricsCollectionIntervalSeconds": 60,
      "EnableReliabilityMonitoring": true,
      "StatisticsRetentionDays": 7
    },

    "Performance": {
      "MaxConcurrentThreads": 10,
      "MessageProcessingTimeoutMs": 5000,
      "EnableMessageDeduplication": true,
      "DeduplicationCacheSize": 1000,
      "DeduplicationCacheExpirationMinutes": 10,
      "EnableRateLimiting": true,
      "MaxNotificationsPerSecond": 100,
      "RateLimitWindowSeconds": 1
    }
  },

  "Monitoring": {
    "Enabled": true,
    "CollectionIntervalSeconds": 60,
    "HistoryRetentionDays": 30,
    "EnableRealTimeMonitoring": true,
    "EnableAlerting": true,
    
    "NotificationMonitoring": {
      "Enabled": true,
      "TrackNotificationDelivery": true,
      "TrackSignalRConnections": true,
      "DeliveryTimeThresholdMs": 1000,
      "MaxHistoryRecords": 5000,
      "EnableNotificationMetrics": true
    },
    


    "AiMonitoring": {
      "Enabled": true,
      "ResponseTimeThresholdMs": 10000,
      "SuccessRateThreshold": 95.0,
      "MaxHistoryRecords": 1000,
      "RecordRequestDetails": true
    },

    "MessageMonitoring": {
      "Enabled": true,
      "DeliveryTimeThresholdMs": 5000,
      "SuccessRateThreshold": 98.0,
      "MaxHistoryRecords": 2000,
      "GroupByAccount": true,
      "GroupByMessageType": true
    },

    "FileMonitoring": {
      "Enabled": true,
      "ProcessingTimeThresholdMs": 30000,
      "SuccessRateThreshold": 90.0,
      "MaxHistoryRecords": 1500,
      "GroupByFileType": true,
      "MaxFileSizeThreshold": *********
    },

    "Alerting": {
      "Enabled": true,
      "CheckIntervalSeconds": 300,
      "CooldownSeconds": 1800,
      "Email": {
        "Enabled": false,
        "SmtpServer": "",
        "SmtpPort": 587,
        "Username": "",
        "Password": "",
        "FromEmail": "",
        "ToEmails": [],
        "UseSsl": true
      },
      "Webhook": {
        "Enabled": false,
        "Url": "",
        "TimeoutSeconds": 30,
        "RetryCount": 3,
        "Headers": {}
      },
      "Rules": [

        {
          "Name": "队列积压告警",
          "Condition": "QueueLength > 5000",
          "Severity": "Critical",
          "Enabled": true
        },
        {
          "Name": "通知失败率告警",
          "Condition": "NotificationFailureRate > 5",
          "Severity": "Warning",
          "Enabled": true
        }
      ]
    },

    "Storage": {
      "StorageType": "Memory",
      "ConnectionString": "",
      "TimeSeries": {
        "Server": "",
        "Port": 8086,
        "Database": "happywechat_monitoring",
        "Username": "",
        "Password": "",
        "BatchSize": 1000,
        "WriteIntervalSeconds": 10
      }
    }
  },
  
  // 🗑️ Performance配置已整合到各自的服务配置中
  // "Performance": { ... },
  
  // 🗑️ RetryPolicy配置已整合到Resilience配置中
  // "RetryPolicy": { ... },
  
  "CircuitBreaker": {
    "FailureThreshold": 5,
    "FailureRateThreshold": 50.0,
    "MinimumThroughput": 10,
    "OpenDurationSeconds": 60,
    "HalfOpenMaxRequests": 3,
    "StatisticsWindowSeconds": 60,
    "Enabled": true,
    
    "NotificationCircuitBreaker": {
      "FailureThreshold": 10,
      "FailureRateThreshold": 30.0,
      "MinimumThroughput": 20,
      "OpenDurationSeconds": 30,
      "HalfOpenMaxRequests": 5,
      "Enabled": true
    }
  },

  "ConnectionStrings": {
    "DefaultConnection": "Server=**************;Port=18606;Database=huakai;User=root;Password=******************************;ConnectionTimeout=30;Pooling=true;MinPoolSize=0;MaxPoolSize=100;"
  },

  "Database": {
    "WriteConnectionString": "Server=**************;Port=18606;Database=huakai;User=root;Password=******************************;ConnectionTimeout=30;Pooling=true;MinPoolSize=0;MaxPoolSize=100;",
    "ReadConnectionString": "Server=**************;Port=18606;Database=huakai;User=root;Password=******************************;ConnectionTimeout=30;Pooling=true;MinPoolSize=0;MaxPoolSize=100;",
    "EnableReadWriteSeparation": false,
    "QueryTimeoutSeconds": 30,
    "CommandTimeoutSeconds": 60,
    "EnableSensitiveDataLogging": false,
    "EnableDetailedErrors": false,
    "ConnectionPool": {
      "MaxPoolSize": 100,
      "MinPoolSize": 10,
      "IdleTimeoutSeconds": 300,
      "ConnectionLifetimeSeconds": 3600,
      "ConnectionCheckIntervalSeconds": 30,
      
      // 🚀 新增：池化DbContext配置
      "PoolSize": 128,
      "EnablePooledFactory": true,
      "PooledFactoryTimeout": 30,
      "PoolHealthCheckInterval": 60
    },
    "RetryPolicy": {
      "EnableRetry": true,
      "MaxRetryCount": 3,
      "BaseDelayMs": 1000,
      "MaxDelayMs": 30000,
      "DelayMultiplier": 2.0
    }
  },
  
  "TimeZone": {
    "DefaultTimeZone": "Asia/Shanghai",
    "EnableTimeZoneConversion": true,
    "DisplayFormat": "yyyy-MM-dd HH:mm:ss",
    "DateFormat": "yyyy-MM-dd",
    "TimeFormat": "HH:mm:ss",
    "Use24HourFormat": true,
    "ShowTimeZoneInLogs": true,
    "AutoDetectClientTimeZone": false,
    "SupportedTimeZones": [
      {
        "Id": "Asia/Shanghai",
        "Name": "中国标准时间",
        "Offset": "+08:00",
        "IsDefault": true
      },
      {
        "Id": "UTC",
        "Name": "协调世界时",
        "Offset": "+00:00",
        "IsDefault": false
      }
    ]
  },

  "Redis": {
    "ConnectionString": "**************:16947,password=xUzvGVlMx@8zO!mASb6Wr3@PWq,abortConnect=false",
    "KeyPrefix": "HW",
    "Database": 1,
    "ConnectTimeout": 30,
    "SyncTimeout": 60,
    "CommandTimeout": 60,
    "RetryTimes": 3,
    "EnableCache": true,
    "DefaultExpirationMinutes": 60,
    "EnableDistributedLock": true,
    "LockTimeoutSeconds": 30,
    "Databases": {
      "Cache": 2,
      "MessageQueue": 3,
      "Session": 1,
      "Lock": 4  
    }
  },
  // 🗑️ HybridCache和UnifiedCache配置已被统一Redis缓存替代
  // "UnifiedCache": { ... },

  // 🗑️ UnifiedArchitecture配置已废弃 - 功能已整合到其他服务中
  // "UnifiedArchitecture": { ... },

  // 🗑️ QueryOptimizer配置已废弃 - 查询优化功能已整合到数据库服务中
  // "QueryOptimizer": { ... },

  // 🗑️ UnifiedConfig配置已废弃 - 配置管理已整合到SystemConfig中
  // "UnifiedConfig": { ... },

  "Logging": {
    "LogLevel": {
      "Default": "Information",
      // 🔧 大幅减少ASP.NET Core框架日志噪音
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.AspNetCore.Mvc.Infrastructure": "Warning",
      "Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker": "Warning",
      "Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor": "Warning",
      "Microsoft.AspNetCore.Routing": "Warning",
      "Microsoft.AspNetCore.Routing.EndpointMiddleware": "Warning",
      "Microsoft.AspNetCore.Hosting": "Warning",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning",
      "Microsoft.AspNetCore.Cors": "Warning",
      // 🔧 大幅减少Entity Framework日志噪音
      "Microsoft.EntityFrameworkCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "None",
      "Microsoft.EntityFrameworkCore.Query": "Warning",
      "Microsoft.EntityFrameworkCore.Migrations": "Information",
      "Microsoft.EntityFrameworkCore.Infrastructure": "Warning",
      "Microsoft.EntityFrameworkCore.Model": "Warning",
      // 🔧 大幅减少HTTP客户端日志噪音
      "System.Net.Http.HttpClient": "Warning",
      "System.Net.Http.HttpClient.frontApi": "Warning",
      "HappyWechat": "Information",
      "HappyWechat.Infrastructure": "Information",

      // 🔧 优化基础设施服务日志等级
      "HappyWechat.Infrastructure.MessageQueue": "Warning",
      "HappyWechat.Infrastructure.Database": "Warning",
      "HappyWechat.Infrastructure.Common": "None",
      "HappyWechat.Infrastructure.Caching": "Warning",
      "HappyWechat.Infrastructure.SystemConfig": "Information",
      "HappyWechat.Infrastructure.Auth": "Debug",
      "HappyWechat.Web.Middleware.RequestLoggingMiddleware": "Information",
      "HappyWechat.Web.Controllers": "Information",
      "HappyWechat.Web.Services": "Information",
      // 🔧 优化UI组件日志 - 降低冗余的页面组件初始化日志
      "HappyWechat.Web.Components": "Information",
      "HappyWechat.Web.Components.Common.BaseWxPageComponent": "Information",
      // 🔧 优化SignalR日志 - 降低连接认证详细日志
      "HappyWechat.Web.Hubs": "Information",
      "HappyWechat.Web.Hubs.CustomUserIdProvider": "Information",
      "HappyWechat.Infrastructure.SignalR": "Information",
      "HappyWechat.Infrastructure.SignalR.UASSignalRAuthenticationService": "Information",
      // 🔧 优化集成服务日志 - 降低缓存命中详细信息
      "HappyWechat.Infrastructure.Integration": "Information",
      "HappyWechat.Infrastructure.Integration.UnifiedArchitectureIntegrationService": "Information",
      // 🔧 保持关键业务日志
      "HappyWechat.Web.Controllers.WxController": "Information",
      "HappyWechat.Web.Services.GroupAiConfigCacheService": "Information",
      // 🔧 优化系统监控日志 - 只在异常时输出
      "HappyWechat.Infrastructure.Logging.Services.EnhancedLoggingService": "Information",
      "HappyWechat.Infrastructure.Monitoring.Services.SystemMonitoringService": "Information",
      "HappyWechat.Infrastructure.Monitoring.Services.MonitoringBackgroundService": "Information",
      "HappyWechat.Web.Services.BlazorStandardizedDataManager": "Information",
      "HappyWechat.Infrastructure.Wx.WxContactQueryOptimizer": "Information",
      // 🔧 精简日志 - 优化系统监控和队列处理日志级别
      "HappyWechat.Infrastructure.MessageQueue.Redis.RedisDelayedMessageService": "Information",
      "HappyWechat.Infrastructure.MessageQueue.Consumers.RedisAiMessageConsumer": "Information",
      "HappyWechat.Infrastructure.AiAgent.AiRequestLogger": "Information",
      "HappyWechat.Infrastructure.MessageProcessing.Configuration.ConfigChangeNotificationService": "Information",
      // 🔧 精简日志 - 进一步优化启动服务和媒体处理日志
      "HappyWechat.Infrastructure.Services.QueueMonitoringService": "Information",
      "HappyWechat.Infrastructure.MessageQueue.Consumers.RedisWxCallbackMessageConsumer": "Information",
      "HappyWechat.Infrastructure.MessageQueue.Core.UnifiedMessageConsumer": "Information",
      "HappyWechat.Infrastructure.Audio.SilkAudioConverter": "Information",
      "HappyWechat.Infrastructure.MessageProcess.FileUploadService": "Information",
      "HappyWechat.Infrastructure.EYun.EYunMessageWrapper": "Information",
      "HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiProcessor": "Information",
      // 🔧 精简日志 - 新增AI提供商和消息发送相关组件的日志级别控制
      "HappyWechat.Infrastructure.AiProvider.MaxKBAiProvider": "Information",
      "HappyWechat.Infrastructure.AiProvider.CoZeAiProvider": "Information",
      "HappyWechat.Infrastructure.AiProvider.DifyAiProvider": "Information",
      "HappyWechat.Infrastructure.AiProvider.ChatGPTAiProvider": "Information",
      "HappyWechat.Infrastructure.AiProvider.BaseAiProvider": "Information",
      "HappyWechat.Infrastructure.AiProvider.AiProviderFactory": "Information",
      "HappyWechat.Infrastructure.AiProvider.EnhancedAiService": "Information",
      "HappyWechat.Infrastructure.MessageQueue.Consumers.MessageSendConsumer": "Information",
      "HappyWechat.Infrastructure.MessageQueue.Redis.RedisQueueConsumer": "Information",
      "HappyWechat.Infrastructure.Services.SensitiveWordDetectionService": "Information",
      "HappyWechat.Infrastructure.MessageQueue.Core.MessageSendQueueManager": "Information",
      "HappyWechat.Infrastructure.MessageQueue.Services.DeadLetterQueueCleanupService": "Information",
      "HappyWechat.Infrastructure.SystemConfig.SystemConfigSyncHostedService": "Information",
      "HappyWechat.Infrastructure.Configuration.ConfigurationChangeListenerStartupService": "Information",
      "HappyWechat.Infrastructure.Configuration.ConfigurationChangeNotifier": "Information",
      "HappyWechat.Infrastructure.Database.DatabaseSchemaUpdater": "Information",
      "Hangfire": "Information",
      "StackExchange.Redis": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Information",
        // 🔧 Console日志也要减少框架噪音
        "Microsoft.AspNetCore": "Warning",
        "Microsoft.AspNetCore.Mvc.Infrastructure": "Warning",
        "Microsoft.AspNetCore.Routing": "Warning",
        "Microsoft.AspNetCore.Hosting": "Warning",
        "Microsoft.EntityFrameworkCore": "Warning",
        "Microsoft.EntityFrameworkCore.Database": "Warning",
        "Microsoft.EntityFrameworkCore.Database.Command": "None",
        "Microsoft.EntityFrameworkCore.Query": "Warning",
        "Microsoft.EntityFrameworkCore.Migrations": "Information",
        "System.Net.Http.HttpClient": "Warning",
        "HappyWechat": "Information",
        // 🔧 Console日志优化 - 降低UI和SignalR噪音
        "HappyWechat.Web.Components": "Information",
        "HappyWechat.Web.Hubs": "Information",
        "HappyWechat.Infrastructure.SignalR": "Information",
        "HappyWechat.Infrastructure.Integration": "Information",
        // 🔧 保持关键业务日志在控制台显示
        "HappyWechat.Web.Controllers.WxController": "Information",
        "Hangfire": "Information",
        "StackExchange.Redis": "Information"
      },
      "FormatterOptions": {
        "IncludeScopes": false,
        "TimestampFormat": "yyyy-MM-dd HH:mm:ss ",
        "UseUtcTimestamp": false
      }
    }
  },

  "RequestLogging": {
    "LogRequestBody": true,
    "LogResponseBody": false
  },

  "AllowedHosts": "*",
  // 🗑️ JWT配置已废弃 - 现在使用纯SessionId认证
  // "Jwt": { ... },
  
  "RedisAuthentication": {
    "SessionExpirationMinutes": 480,
    "SessionRenewalMinutes": 30,
    "MaxConcurrentSessions": 3,
    "EnableAutoRenewal": true,
    "EnableDetailedLogging": true,
    "SessionCleanupIntervalMinutes": 15,
    "KeyPrefix": "auth",
    "EnablePermissionCache": true,
    "PermissionCacheExpirationMinutes": 60,
    "EnableSessionStatistics": true,
    "ForceLogoutInactiveHours": 24
  },

  "MyDomains": {
    "ApplicationDomain": "http://127.0.0.1:5215"
  },

  "EYun": {
    "Account": "***********",
    "Password": "X7sDKRaSDZkcvbM",
    "WxEYunBaseUrl": "http://*************:9899/",
    "CallBackUrl": "http://**************:15125/api/wx/eYunWxMessageCallback",
    "Token": "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************.wYQ23_tHm6wDfN4YPqIYe5HvOYmMKcNWHoSjS1UAl8be3-LLxyTFdeWj3o-HZIqnSoGeCO6Bi8ACAzfcVCB_FA"
  },

  "FileStorage": {
    "Provider": "MinIO",
    "MinIO": {
      "Endpoint": "**************:35684",
      "AccessKey": "lRH2411C25mhOJ4X8Qy4",
      "SecretKey": "40arCfeZgPJE9oZNXjTbbcYxFbiPJVznfeVx3EG5",
      "UseSSL": false,
      "DefaultBucket": "wechat",
      "Region": "cn-north-1"
    },
    "TencentCOS": {
      "AppId": "",
      "SecretId": "",
      "SecretKey": "",
      "Region": "ap-beijing",
      "DefaultBucket": "wenxiang",
      "CdnDomain": "",
      "UseCdn": false
    },
    "File": {
      "AllowedExtensions": [
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
        ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv",
        ".mp3", ".wav", ".flac", ".aac", ".m4a",
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt"
      ],
      "MaxFileSize": *********,
      "MaxImageSize": 10485760,
      "MaxVideoSize": *********,
      "MaxAudioSize": 52428800,
      "UrlExpirationMinutes": 60,
      "EnableCompression": true,
      "ImageCompressionQuality": 85,
      "Paths": {
        "Images": "images/{yyyy}/{MM}/{dd}/{guid}{ext}",
        "Videos": "videos/{yyyy}/{MM}/{dd}/{guid}{ext}",
        "Audios": "audios/{yyyy}/{MM}/{dd}/{guid}{ext}",
        "Documents": "documents/{yyyy}/{MM}/{dd}/{guid}{ext}",
        "Others": "files/{yyyy}/{MM}/{dd}/{guid}{ext}"
      }
    }
  },

  "Resilience": {
    "MaxRetries": 3,
    "BaseDelayMs": 1000,
    "MaxDelayMs": 30000,
    "CircuitBreakerFailureThreshold": 5,
    "CircuitBreakerTimeoutMs": 60000,
    "EYunMaxRetries": 2,
    "ContactSyncMaxRetries": 1,
    "MessageSendMaxRetries": 2
  },

  "SystemInfo": {
    "SystemName": "乐学营销系统",
    "SystemVersion": "V2.1",
    "AdminEmail": "<EMAIL>",
    "CompanyName": "乐学科技",
    "SystemDescription": "智能微信管理系统"
  },

  "MessageProcess": {
    "UserSwitchIntervalSeconds": 1,
    "GroupSendMinIntervalSeconds": 2,
    "GroupSendMaxIntervalSeconds": 5,
    "EnableMessageQueue": true,
    "DetectSimilarContent": true
  },

  "Deduplication": {
    "DefaultWindowMinutes": 5,
    "CleanupBatchSize": 100,
    "DefaultStrategy": "Combined",
    "EnableLocalCache": true,
    "LocalCacheMaxEntries": 10000
  },

  "SignalRHealthCheck": {
    "HealthCheckIntervalMs": 45000,
    "UnstableHealthCheckIntervalMs": 15000,
    "InitializationGracePeriodMs": 60000,
    "JavaScriptFunctionTimeoutMs": 5000,
    "CircuitHealthCheckTimeoutMs": 3000,
    "HealthCheckRetryCount": 2,
    "RetryIntervalMs": 1000,
    "EnableFallbackMode": true,
    "FallbackModeTimeoutMs": 20000,
    "TaskCancelledToleranceCount": 3,
    "TaskCancelledToleranceWindowMs": 45000,
    "EnableVerboseLogging": false,
    "ConnectionStateCacheMs": 15000,
    "UserOperationQuickCheckTimeoutMs": 2000,
    "UserOperationHealthCheckCacheMs": 15000,
    "AllowFallbackModeForUserOperations": true,
    "TreatWarningAsHealthyForUserOperations": true,
    "TreatDegradedAsHealthyForUserOperations": true
  },

  "EYunRiskControl": {
    "SensitiveWords": [
      "违法", "政治", "敏感", "广告", "推广", "色情", "淫秽", "赌博", "暴力"
    ],
    "GroupWelcomeMessage": "欢迎 {memberName} 加入群聊！如有问题请随时咨询。",
    "FriendWelcomeMessage": "您好，很高兴成为您的好友！有什么可以帮助您的吗？",
    "UseAiForWelcome": false,
    "UseMaterialLibraryForWelcome": false,
    "MaxContentLength": 2000,
    "MessageIntervalMinMs": 1500,
    "MessageIntervalMaxMs": 2000,
    "BatchOperationIntervalMinMs": 300,
    "BatchOperationIntervalMaxMs": 1500,
    "AutoAcceptFriendsPerDay": 120,
    "EnableAutoAcceptFriends": false,

    "AutoAcceptKeywords": ["朋友", "合作", "咨询", "了解"],
    "EnableKeywordMatching": false,
    "EnableIncomingMessageSensitiveWordDetection": true,
    "EnableAiReplyMessageSensitiveWordDetection": true,
    "SensitiveWordAction": "Block",
    "WelcomeMessageDelaySeconds": 10
  },

  // 新增：媒体处理配置
  "MediaProcessing": {
    "Audio": {
      "MaxFileSizeBytes": *********,
      "MaxDurationSeconds": 300,
      "DefaultSampleRate": 24000,
      "DefaultBitrate": 128,
      "MaxConcurrentConversions": 5,
      "FFmpegPath": "/usr/bin/ffmpeg"
    },

    "SilkDecoder": {
      "SearchPaths": [
        "/usr/local/bin/silk_v3_decoder",
        "/usr/bin/silk_v3_decoder",
        "/opt/silk/silk_v3_decoder",
        "/app/silk_v3_decoder"
      ],
      "HealthCheckInterval": "00:05:00",
      "EnableDiagnostics": true,
      "EnablePerformanceMonitoring": true
    },

    "FailureRecovery": {
      "MaxRecoveryAttempts": 3,
      "RecoveryDelay": "00:00:05",
      "MaxFileSizeMB": 100,
      "EnableAutoRecovery": true,
      "RecoverableFailureTypes": [
        "NetworkTimeout",
        "EYunApiError",
        "InsufficientResources"
      ]
    },

    "HealthCheck": {
      "MinApiSuccessRate": 0.8,
      "MaxMemoryUsageMB": 1024,
      "WarningMemoryUsageMB": 512,
      "MinFreeSpaceGB": 1,
      "WarningFreeSpaceGB": 5,
      "MaxCpuUsagePercent": 80,
      "CheckInterval": "00:05:00"
    },

    "Monitoring": {
      "MinSuccessRateThreshold": 0.8,
      "MaxAverageProcessingTimeSeconds": 30,
      "SlowProcessingThresholdSeconds": 60,
      "EventRetentionPeriod": "1.00:00:00",
      "MaxRecentEvents": 1000
    }
  },

  // EYun重试策略配置
  "EYunRetry": {
    "MaxRetryAttempts": 2,
    "BaseDelay": "00:00:01",
    "MaxDelay": "00:00:30",
    "RetryStrategy": "ExponentialWithJitter",
    "JitterFactor": 0.1,
    "FileDownloadMaxRetries": 5,
    "FileDownloadBaseDelay": "00:00:02",
    "CircuitBreakerFailureThreshold": 5,
    "CircuitBreakerDuration": "00:01:00"
  }
}