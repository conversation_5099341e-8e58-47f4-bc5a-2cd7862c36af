namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 批量更新联系人命令
/// </summary>
public class BatchUpdateContactCommand
{
    /// <summary>
    /// 要更新的联系人ID列表
    /// </summary>
    public List<Guid> ContactIds { get; set; } = new();

    /// <summary>
    /// 更新操作类型
    /// </summary>
    public BatchUpdateType UpdateType { get; set; }

    /// <summary>
    /// 更新值（根据操作类型而定）
    /// </summary>
    public object? UpdateValue { get; set; }
}

/// <summary>
/// 批量更新操作类型
/// </summary>
public enum BatchUpdateType
{
    /// <summary>
    /// 设置自动回复状态
    /// </summary>
    SetAutoReply,

    /// <summary>
    /// 设置备注
    /// </summary>
    SetRemark,

    /// <summary>
    /// 设置标签
    /// </summary>
    SetLabel
}
