﻿namespace HappyWechat.Application.DTOs.Requests.Queries;

public class WxGetQrCodeQuery
{
    //微信原始id （首次登录平台的号传""，掉线重登必须传值，否则会频繁掉线！！！） 第3步会返回此字段，记得入库保存
    public string WcId { get; set; } = string.Empty;

    /*
     *1:北京    2:天津     3:上海     4:重庆     5:河北
        6:山西     7:江苏     8:浙江     9:安徽     10:福建
        11:江西     12:山东     13:河南     14:湖北     15:湖南
        16:广东     17:海南     18:四川     19:云南     20:陕西
        21:黑龙江     22:辽宁     23:贵州
     */
    public int Proxy { get; set; }
    public string? ProxyIp { get; set; } = string.Empty;
    public string? ProxyUser { get; set; } = string.Empty;
    public string? ProxyPassword { get; set; } = string.Empty;
    
    //登录设备类型：ipad(默认), mac, android
    public string DeviceType { get; set; } = "ipad";
}