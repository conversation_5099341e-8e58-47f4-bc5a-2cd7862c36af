using System.Text.Json;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Caching.Interfaces;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.SystemConfig;

/// <summary>
/// 系统配置数据服务实现
/// 负责配置的数据库操作和缓存管理
/// </summary>
public class SystemConfigDataService : ISystemConfigDataService
{
    private readonly ISystemConfigRepository _configRepository;
    private readonly IUnifiedCacheService _cacheService;
    private readonly ILogger<SystemConfigDataService> _logger;
    
    private const string CACHE_PREFIX = "config";
    private static readonly TimeSpan DEFAULT_CACHE_EXPIRY = TimeSpan.FromMinutes(30);
    
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    public SystemConfigDataService(
        ISystemConfigRepository configRepository,
        IUnifiedCacheService cacheService,
        ILogger<SystemConfigDataService> logger)
    {
        _configRepository = configRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<T?> GetConfigFromDatabaseAsync<T>(string configType, string configKey) where T : class, new()
    {
        try
        {
            var entity = await _configRepository.GetConfigurationAsync(configType, configKey);
            if (entity == null)
            {
                _logger.LogDebug("数据库中未找到配置 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
                return null;
            }

            if (string.IsNullOrEmpty(entity.ConfigValue))
            {
                _logger.LogWarning("数据库中配置值为空 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
                return null;
            }

            var config = JsonSerializer.Deserialize<T>(entity.ConfigValue, JsonOptions);
            _logger.LogDebug("从数据库成功获取配置 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return config;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "反序列化配置失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从数据库获取配置失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return null;
        }
    }

    public async Task SaveConfigToDatabaseAsync<T>(string configType, string configKey, T config) where T : class
    {
        try
        {
            var configValue = JsonSerializer.Serialize(config, JsonOptions);
            await _configRepository.SetConfigurationAsync(configType, configKey, configValue);
            // 🔧 注释冗余的配置保存到数据库日志 - 减少日志噪音，每次配置更新都会重复出现
            // _logger.LogInformation("配置已保存到数据库 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "序列化配置失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置到数据库失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            throw;
        }
    }

    public async Task<T?> GetConfigFromCacheAsync<T>(string configType, string configKey) where T : class, new()
    {
        try
        {
            var cacheKey = GetCacheKey(configType, configKey);
            var cachedConfig = await _cacheService.GetAsync<T>(cacheKey);
            
            if (cachedConfig != null)
            {
                _logger.LogDebug("从缓存获取配置 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            }
            
            return cachedConfig;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取配置失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return null;
        }
    }

    public async Task SaveConfigToCacheAsync<T>(string configType, string configKey, T config, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var cacheKey = GetCacheKey(configType, configKey);
            var cacheExpiry = expiry ?? DEFAULT_CACHE_EXPIRY;
            
            await _cacheService.SetAsync(cacheKey, config, cacheExpiry);
            _logger.LogDebug("配置已保存到缓存 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}, Expiry: {Expiry}", 
                configType, configKey, cacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置到缓存失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            throw;
        }
    }

    public async Task ClearConfigCacheAsync(string configType, string? configKey = null)
    {
        try
        {
            if (string.IsNullOrEmpty(configKey))
            {
                // 清除整个配置类型的缓存
                var pattern = $"{CACHE_PREFIX}:{configType}:*";
                await _cacheService.RemoveByPatternAsync(pattern);
                // 🔧 注释冗余的清除配置类型缓存日志 - 减少日志噪音，每次配置更新都会重复出现
                // _logger.LogInformation("已清除配置类型缓存 - ConfigType: {ConfigType}", configType);
            }
            else
            {
                // 清除特定配置的缓存
                var cacheKey = GetCacheKey(configType, configKey);
                await _cacheService.RemoveAsync(cacheKey);
                _logger.LogDebug("已清除特定配置缓存 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除配置缓存失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
        }
    }

    public async Task<bool> ConfigExistsInDatabaseAsync(string configType, string configKey)
    {
        try
        {
            var entity = await _configRepository.GetConfigurationAsync(configType, configKey);
            return entity != null && !string.IsNullOrEmpty(entity.ConfigValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查配置存在性失败 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}", configType, configKey);
            return false;
        }
    }

    public string GetCacheKey(string configType, string configKey)
    {
        return $"{CACHE_PREFIX}:{configType}:{configKey}";
    }
}
