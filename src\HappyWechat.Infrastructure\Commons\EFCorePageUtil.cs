﻿using Microsoft.EntityFrameworkCore;
using HappyWechat.Infrastructure.Extensions;
using System.Linq.Expressions;

namespace HappyWechat.Application.Commons;

public class EFCorePageUtil
{
    /// <summary>
    /// 获取分页结果（带排序）
    /// </summary>
    public static async Task<PageResponse<T>> GetPagedResultAsync<T, TKey>(IQueryable<T> query,
        PageQuery pageQuery, Expression<Func<T, TKey>> orderBy, bool ascending = true)
    {
        // 获取总数
        var totalCount = await query.CountAsync();
        var page = pageQuery.Page;
        var pageSize = pageQuery.PageSize;

        // 🔧 修复：使用带排序的分页方法，确保查询结果一致性
        var items = await query.ApplyPagination(page, pageSize, orderBy, ascending).ToListAsync();

        // 计算分页信息
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        var hasNextPage = page < totalPages;
        var hasPreviousPage = page > 1;

        return new PageResponse<T>
        {
            Items = items,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize,
            TotalPages = totalPages,
            HasNextPage = hasNextPage,
            HasPreviousPage = hasPreviousPage
        };
    }

    /// <summary>
    /// 获取分页结果（兼容性方法）
    /// </summary>
    public static async Task<PageResponse<T>> GetPagedResultAsync<T>(IQueryable<T> query,
        PageQuery pageQuery)
    {
        // 获取总数
        var totalCount = await query.CountAsync();
        var page = pageQuery.Page;
        var pageSize = pageQuery.PageSize;

        // 获取分页数据 - 确保使用相同的查询对象
        var items = await query.ApplyPagination(page, pageSize).ToListAsync();

        // 计算分页信息
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        var hasNextPage = page < totalPages;
        var hasPreviousPage = page > 1;

        // 添加调试日志
        if (totalCount != items.Count && page == 1 && pageSize >= totalCount)
        {
            // 当第一页且页大小足够大时，返回的数据应该等于总数
            Console.WriteLine($"[EFCorePageUtil] 数据不一致警告 - 总数: {totalCount}, 实际返回: {items.Count}, 页: {page}, 页大小: {pageSize}");
        }

        return new PageResponse<T>
        {
            Page = page,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = totalPages,
            HasNextPage = hasNextPage,
            HasPreviousPage = hasPreviousPage,
            Items = items
        };
    }

    /// <summary>
    /// 获取分页结果（带性能优化）
    /// </summary>
    public static async Task<PageResponse<T>> GetOptimizedPagedResultAsync<T>(IQueryable<T> query,
        PageQuery pageQuery, bool noTracking = true) where T : class
    {
        if (noTracking && query is IQueryable<T>)
        {
            query = query.AsNoTracking();
        }

        var totalCount = await query.CountAsync();
        var page = pageQuery.Page;
        var pageSize = pageQuery.PageSize;
        var items = await query.ApplyPagination(page, pageSize).ToListAsync();

        // 计算分页信息
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        var hasNextPage = page < totalPages;
        var hasPreviousPage = page > 1;

        return new PageResponse<T>
        {
            Page = page,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = totalPages,
            HasNextPage = hasNextPage,
            HasPreviousPage = hasPreviousPage,
            Items = items
        };
    }

    /// <summary>
    /// 获取分页结果（支持投影）
    /// </summary>
    public static async Task<PageResponse<TResult>> GetPagedResultWithProjectionAsync<T, TResult>(
        IQueryable<T> query, System.Linq.Expressions.Expression<Func<T, TResult>> projection, PageQuery pageQuery)
    {
        var totalCount = await query.CountAsync();
        var page = pageQuery.Page;
        var pageSize = pageQuery.PageSize;
        
        var items = await query
            .ApplyPagination(page, pageSize)
            .Select(projection)
            .ToListAsync();

        // 计算分页信息
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        var hasNextPage = page < totalPages;
        var hasPreviousPage = page > 1;

        return new PageResponse<TResult>
        {
            Page = page,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = totalPages,
            HasNextPage = hasNextPage,
            HasPreviousPage = hasPreviousPage,
            Items = items
        };
    }
}