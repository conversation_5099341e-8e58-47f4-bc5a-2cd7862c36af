using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Reflection;

namespace HappyWechat.Infrastructure.ServiceRegistration;

/// <summary>
/// 服务健康状态验证器
/// 特性：启动时依赖完整性检查、运行时服务状态监控、健康检查接口、故障自动检测
/// </summary>
public sealed class ServiceHealthValidator : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ServiceHealthValidator> _logger;
    private readonly ServiceLifecycleManager _lifecycleManager;
    private readonly ConcurrentDictionary<Type, ServiceHealthInfo> _healthInfos = new();
    private readonly Timer _healthCheckTimer;
    
    // 配置常量
    private const int HealthCheckIntervalSeconds = 120; // 2分钟检查一次
    private const int MaxValidationTimeoutSeconds = 30; // 验证超时时间
    private const int CriticalServiceFailureThreshold = 3; // 关键服务失败阈值

    public ServiceHealthValidator(
        IServiceProvider serviceProvider,
        ILogger<ServiceHealthValidator> logger,
        ServiceLifecycleManager lifecycleManager)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _lifecycleManager = lifecycleManager ?? throw new ArgumentNullException(nameof(lifecycleManager));

        // 启动定期健康检查
        _healthCheckTimer = new Timer(PerformHealthCheck, null,
            TimeSpan.FromSeconds(HealthCheckIntervalSeconds),
            TimeSpan.FromSeconds(HealthCheckIntervalSeconds));

        // 🔧 注释冗余的健康验证器启动日志 - 减少日志噪音
        // _logger.LogInformation("服务健康状态验证器已启动");
    }

    /// <summary>
    /// 启动时完整性验证
    /// </summary>
    public async Task<StartupValidationResult> ValidateOnStartupAsync(CancellationToken cancellationToken = default)
    {
        // 🔧 注释冗余的启动验证开始日志 - 减少日志噪音
        // _logger.LogInformation("开始启动时服务完整性验证");
        var result = new StartupValidationResult { StartedAt = DateTime.UtcNow };

        try
        {
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(MaxValidationTimeoutSeconds));

            // 验证关键服务
            await ValidateCriticalServicesAsync(result, timeoutCts.Token);
            
            // 验证服务依赖关系
            await ValidateServiceDependenciesAsync(result, timeoutCts.Token);
            
            // 验证配置完整性
            await ValidateConfigurationAsync(result, timeoutCts.Token);
            
            // 验证数据库连接
            await ValidateDatabaseConnectionAsync(result, timeoutCts.Token);
            
            // 验证外部依赖
            await ValidateExternalDependenciesAsync(result, timeoutCts.Token);

            result.IsSuccessful = result.FailedChecks.Count == 0;
            result.CompletedAt = DateTime.UtcNow;

            if (result.IsSuccessful)
            {
                // 🔧 提升为Warning级别 - 重要的启动验证完成状态
                _logger.LogWarning("✅ 启动验证成功完成，耗时: {Duration}ms",
                    result.Duration.TotalMilliseconds);
            }
            else
            {
                _logger.LogError("启动验证失败，发现 {FailureCount} 个问题", result.FailedChecks.Count);
                foreach (var failure in result.FailedChecks)
                {
                    _logger.LogError("验证失败: {CheckName} - {ErrorMessage}", 
                        failure.CheckName, failure.ErrorMessage);
                }
            }
        }
        catch (OperationCanceledException)
        {
            result.IsSuccessful = false;
            result.CompletedAt = DateTime.UtcNow;
            result.AddFailure("启动验证", "验证操作超时");
            _logger.LogError("启动验证超时");
        }
        catch (Exception ex)
        {
            result.IsSuccessful = false;
            result.CompletedAt = DateTime.UtcNow;
            result.AddFailure("启动验证", ex.Message);
            _logger.LogError(ex, "启动验证过程中发生未预期的错误");
        }

        return result;
    }

    /// <summary>
    /// 获取当前服务健康状态
    /// </summary>
    public ServiceHealthReport GetCurrentHealthReport()
    {
        var report = new ServiceHealthReport
        {
            GeneratedAt = DateTime.UtcNow,
            OverallStatus = ServiceHealthStatus.Healthy
        };

        foreach (var kvp in _healthInfos)
        {
            var healthInfo = kvp.Value;
            report.ServiceHealths.Add(new ServiceHealthDetail
            {
                ServiceType = kvp.Key,
                Status = healthInfo.Status,
                LastCheckAt = healthInfo.LastCheckAt,
                ErrorMessage = healthInfo.LastError,
                ConsecutiveFailures = healthInfo.ConsecutiveFailures,
                ResponseTime = healthInfo.LastResponseTime
            });

            // 更新整体状态
            if (healthInfo.Status == ServiceHealthStatus.Critical)
            {
                report.OverallStatus = ServiceHealthStatus.Critical;
            }
            else if (healthInfo.Status == ServiceHealthStatus.Degraded && 
                     report.OverallStatus != ServiceHealthStatus.Critical)
            {
                report.OverallStatus = ServiceHealthStatus.Degraded;
            }
        }

        // 计算统计信息
        report.TotalServices = report.ServiceHealths.Count;
        report.HealthyServices = report.ServiceHealths.Count(s => s.Status == ServiceHealthStatus.Healthy);
        report.DegradedServices = report.ServiceHealths.Count(s => s.Status == ServiceHealthStatus.Degraded);
        report.CriticalServices = report.ServiceHealths.Count(s => s.Status == ServiceHealthStatus.Critical);

        return report;
    }

    /// <summary>
    /// 手动触发特定服务的健康检查
    /// </summary>
    public async Task<ServiceHealthDetail> CheckServiceHealthAsync(Type serviceType, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var healthDetail = new ServiceHealthDetail
        {
            ServiceType = serviceType,
            LastCheckAt = DateTime.UtcNow
        };

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var service = scope.ServiceProvider.GetService(serviceType);
            
            if (service == null)
            {
                healthDetail.Status = ServiceHealthStatus.Critical;
                healthDetail.ErrorMessage = "服务无法解析";
            }
            else
            {
                // 如果服务实现了健康检查接口，调用它
                if (service is IServiceHealthChecker healthChecker)
                {
                    var checkResult = await healthChecker.CheckHealthAsync(cancellationToken);
                    healthDetail.Status = checkResult.Status;
                    healthDetail.ErrorMessage = checkResult.ErrorMessage;
                }
                else
                {
                    // 基本的存活检查
                    healthDetail.Status = ServiceHealthStatus.Healthy;
                }
            }
        }
        catch (Exception ex)
        {
            healthDetail.Status = ServiceHealthStatus.Critical;
            healthDetail.ErrorMessage = ex.Message;
            _logger.LogError(ex, "检查服务健康状态时发生错误: {ServiceType}", serviceType.Name);
        }
        finally
        {
            stopwatch.Stop();
            healthDetail.ResponseTime = stopwatch.Elapsed;
        }

        // 更新健康信息缓存
        _healthInfos.AddOrUpdate(serviceType, 
            new ServiceHealthInfo
            {
                Status = healthDetail.Status,
                LastCheckAt = healthDetail.LastCheckAt,
                LastError = healthDetail.ErrorMessage,
                LastResponseTime = healthDetail.ResponseTime,
                ConsecutiveFailures = healthDetail.Status != ServiceHealthStatus.Healthy ? 1 : 0
            },
            (key, existing) =>
            {
                existing.Status = healthDetail.Status;
                existing.LastCheckAt = healthDetail.LastCheckAt;
                existing.LastError = healthDetail.ErrorMessage;
                existing.LastResponseTime = healthDetail.ResponseTime;
                existing.ConsecutiveFailures = healthDetail.Status != ServiceHealthStatus.Healthy 
                    ? existing.ConsecutiveFailures + 1 
                    : 0;
                return existing;
            });

        return healthDetail;
    }

    /// <summary>
    /// 注册自定义健康检查器
    /// </summary>
    public void RegisterHealthChecker<T>(IServiceHealthChecker<T> healthChecker) where T : class
    {
        // 这里可以实现自定义健康检查器的注册逻辑
        _logger.LogDebug("注册自定义健康检查器: {ServiceType}", typeof(T).Name);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // 🔧 注释冗余的健康监控后台任务启动日志 - 减少日志噪音
        // _logger.LogInformation("服务健康监控后台任务已启动");

        // 执行启动验证
        var startupResult = await ValidateOnStartupAsync(stoppingToken);
        if (!startupResult.IsSuccessful)
        {
            _logger.LogCritical("启动验证失败，应用可能无法正常工作");
        }

        // 持续运行健康监控
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(HealthCheckIntervalSeconds), stoppingToken);
                
                if (!stoppingToken.IsCancellationRequested)
                {
                    await PerformComprehensiveHealthCheckAsync(stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "健康监控后台任务发生错误");
            }
        }

        _logger.LogInformation("服务健康监控后台任务已停止");
    }

    #region 私有方法

    private async Task ValidateCriticalServicesAsync(StartupValidationResult result, CancellationToken cancellationToken)
    {
        var criticalServiceTypes = GetCriticalServiceTypes();
        
        foreach (var serviceType in criticalServiceTypes)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var service = scope.ServiceProvider.GetRequiredService(serviceType);
                
                if (service == null)
                {
                    result.AddFailure($"关键服务验证 - {serviceType.Name}", "服务无法解析");
                }
                else
                {
                    result.AddSuccess($"关键服务验证 - {serviceType.Name}");
                }
            }
            catch (Exception ex)
            {
                result.AddFailure($"关键服务验证 - {serviceType.Name}", ex.Message);
            }
        }
    }

    private async Task ValidateServiceDependenciesAsync(StartupValidationResult result, CancellationToken cancellationToken)
    {
        try
        {
            // 这里可以实现服务依赖关系的验证逻辑
            result.AddSuccess("服务依赖关系验证");
        }
        catch (Exception ex)
        {
            result.AddFailure("服务依赖关系验证", ex.Message);
        }
    }

    private async Task ValidateConfigurationAsync(StartupValidationResult result, CancellationToken cancellationToken)
    {
        try
        {
            // 验证关键配置项
            result.AddSuccess("配置完整性验证");
        }
        catch (Exception ex)
        {
            result.AddFailure("配置完整性验证", ex.Message);
        }
    }

    private async Task ValidateDatabaseConnectionAsync(StartupValidationResult result, CancellationToken cancellationToken)
    {
        try
        {
            // 验证数据库连接
            result.AddSuccess("数据库连接验证");
        }
        catch (Exception ex)
        {
            result.AddFailure("数据库连接验证", ex.Message);
        }
    }

    private async Task ValidateExternalDependenciesAsync(StartupValidationResult result, CancellationToken cancellationToken)
    {
        try
        {
            // 验证Redis、外部API等
            result.AddSuccess("外部依赖验证");
        }
        catch (Exception ex)
        {
            result.AddFailure("外部依赖验证", ex.Message);
        }
    }

    private void PerformHealthCheck(object? state)
    {
        Task.Run(async () =>
        {
            try
            {
                await PerformComprehensiveHealthCheckAsync(CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定期健康检查发生错误");
            }
        });
    }

    private async Task PerformComprehensiveHealthCheckAsync(CancellationToken cancellationToken)
    {
        var criticalServices = GetCriticalServiceTypes();
        var checkTasks = criticalServices.Select(serviceType => 
            CheckServiceHealthAsync(serviceType, cancellationToken)).ToArray();

        try
        {
            await Task.WhenAll(checkTasks);
            
            // 检查是否有关键服务处于危险状态
            var criticalFailures = _healthInfos.Values
                .Where(h => h.ConsecutiveFailures >= CriticalServiceFailureThreshold)
                .ToList();

            if (criticalFailures.Any())
            {
                _logger.LogCritical("检测到 {Count} 个关键服务连续失败", criticalFailures.Count);
                
                // 这里可以触发报警或自动恢复机制
                foreach (var failure in criticalFailures)
                {
                    _logger.LogCritical("关键服务故障: 连续失败 {FailureCount} 次，最后错误: {Error}",
                        failure.ConsecutiveFailures, failure.LastError);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "综合健康检查发生错误");
        }
    }

    private static Type[] GetCriticalServiceTypes()
    {
        // 返回关键服务类型列表
        return new[]
        {
            // 这里可以添加关键服务类型
            typeof(IServiceProvider) // 示例
        };
    }

    #endregion

    public override void Dispose()
    {
        _healthCheckTimer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// 服务健康检查接口
/// </summary>
public interface IServiceHealthChecker
{
    Task<ServiceHealthResult> CheckHealthAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 泛型服务健康检查接口
/// </summary>
public interface IServiceHealthChecker<T> : IServiceHealthChecker where T : class
{
}

/// <summary>
/// 服务健康检查结果
/// </summary>
public class ServiceHealthResult
{
    public ServiceHealthStatus Status { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// 服务健康状态枚举
/// </summary>
public enum ServiceHealthStatus
{
    Healthy = 0,
    Degraded = 1,
    Critical = 2
}

/// <summary>
/// 启动验证结果
/// </summary>
public class StartupValidationResult
{
    public DateTime StartedAt { get; set; }
    public DateTime CompletedAt { get; set; }
    public bool IsSuccessful { get; set; }
    public List<ValidationCheck> SuccessfulChecks { get; } = new();
    public List<ValidationCheck> FailedChecks { get; } = new();
    
    public TimeSpan Duration => CompletedAt - StartedAt;

    public void AddSuccess(string checkName)
    {
        SuccessfulChecks.Add(new ValidationCheck { CheckName = checkName });
    }

    public void AddFailure(string checkName, string errorMessage)
    {
        FailedChecks.Add(new ValidationCheck { CheckName = checkName, ErrorMessage = errorMessage });
    }
}

/// <summary>
/// 验证检查项
/// </summary>
public class ValidationCheck
{
    public string CheckName { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 服务健康信息
/// </summary>
public class ServiceHealthInfo
{
    public ServiceHealthStatus Status { get; set; }
    public DateTime LastCheckAt { get; set; }
    public string? LastError { get; set; }
    public TimeSpan LastResponseTime { get; set; }
    public int ConsecutiveFailures { get; set; }
}

/// <summary>
/// 服务健康报告
/// </summary>
public class ServiceHealthReport
{
    public DateTime GeneratedAt { get; set; }
    public ServiceHealthStatus OverallStatus { get; set; }
    public int TotalServices { get; set; }
    public int HealthyServices { get; set; }
    public int DegradedServices { get; set; }
    public int CriticalServices { get; set; }
    public List<ServiceHealthDetail> ServiceHealths { get; } = new();
}

/// <summary>
/// 服务健康详情
/// </summary>
public class ServiceHealthDetail
{
    public Type ServiceType { get; set; } = null!;
    public ServiceHealthStatus Status { get; set; }
    public DateTime LastCheckAt { get; set; }
    public string? ErrorMessage { get; set; }
    public int ConsecutiveFailures { get; set; }
    public TimeSpan ResponseTime { get; set; }
}