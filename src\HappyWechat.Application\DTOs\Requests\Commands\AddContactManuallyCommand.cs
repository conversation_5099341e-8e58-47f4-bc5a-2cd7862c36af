using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 手动添加联系人命令
/// </summary>
public class AddContactManuallyCommand
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public required Guid WxManagerId { get; set; }

    /// <summary>
    /// 微信联系人ID (wcid)
    /// </summary>
    public required string ContactId { get; set; }

    /// <summary>
    /// 联系人类型，默认为个人微信好友
    /// </summary>
    public WxContactListType ListType { get; set; } = WxContactListType.Friends;

    /// <summary>
    /// 是否立即获取联系人详情
    /// </summary>
    public bool FetchDetailsImmediately { get; set; } = true;
}
