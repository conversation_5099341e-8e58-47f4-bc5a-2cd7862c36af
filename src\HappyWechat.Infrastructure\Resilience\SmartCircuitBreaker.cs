using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net.Sockets;
using HappyWechat.Application.Exceptions;

namespace HappyWechat.Infrastructure.Resilience;

/// <summary>
/// 智能断路器 - 修复状态不一致问题，支持异常分类处理
/// 特性：状态机模式、异常分类处理、指标收集、配置管理
/// </summary>
public sealed class SmartCircuitBreaker : IDisposable
{
    private readonly SmartCircuitBreakerOptions _options;
    private readonly ILogger<SmartCircuitBreaker> _logger;
    private readonly Timer _stateCheckTimer;
    private readonly ConcurrentQueue<CircuitBreakerEvent> _eventHistory = new();
    private readonly object _stateLock = new();
    
    private CircuitBreakerState _currentState = CircuitBreakerState.Closed;
    private DateTime _lastStateChangeTime = DateTime.UtcNow;
    private int _consecutiveFailures;
    private int _consecutiveSuccesses;
    private long _totalRequests;
    private long _totalFailures;
    private long _totalSuccesses;
    private DateTime _lastFailureTime;
    private Exception? _lastException;

    public event EventHandler<CircuitBreakerStateChangedEventArgs>? StateChanged;

    public SmartCircuitBreaker(IOptions<SmartCircuitBreakerOptions> options, ILogger<SmartCircuitBreaker> logger)
    {
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 验证配置
        ValidateOptions();

        // 启动状态检查定时器
        _stateCheckTimer = new Timer(CheckStateTransition, null, 
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        // 🔧 注释冗余的智能断路器初始化日志 - 减少日志噪音，每次操作都会触发
        // _logger.LogInformation("智能断路器已初始化，故障阈值: {FailureThreshold}, 超时: {Timeout}ms",
        //     _options.FailureThreshold, _options.TimeoutMs);
    }

    /// <summary>
    /// 当前断路器状态
    /// </summary>
    public CircuitBreakerState CurrentState
    {
        get
        {
            lock (_stateLock)
            {
                return _currentState;
            }
        }
    }

    /// <summary>
    /// 断路器统计信息
    /// </summary>
    public CircuitBreakerStatistics Statistics
    {
        get
        {
            lock (_stateLock)
            {
                return new CircuitBreakerStatistics
                {
                    CircuitName = "SmartCircuitBreaker",
                    State = MapToStateEnum(_currentState),
                    TotalRequests = _totalRequests,
                    SuccessfulRequests = _totalSuccesses,
                    FailedRequests = _totalFailures,
                    RejectedRequests = 0, // Would need to track this separately
                    LastFailureTime = _lastFailureTime == DateTime.MinValue ? null : _lastFailureTime,
                    LastSuccessTime = null, // Would need to track this separately
                    CircuitOpenTime = _currentState == CircuitBreakerState.Open ? _lastStateChangeTime : null,
                    NextRetryTime = _currentState == CircuitBreakerState.Open ? 
                        _lastStateChangeTime.AddMilliseconds(_options.OpenTimeoutMs) : null
                };
            }
        }
    }

    /// <summary>
    /// 执行受保护的操作
    /// </summary>
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        if (operation == null) throw new ArgumentNullException(nameof(operation));

        // 检查断路器状态
        if (!CanExecute())
        {
            var exception = new CircuitBreakerOpenException(
                $"断路器处于 {CurrentState} 状态，操作被拒绝");
            RecordEvent(CircuitBreakerEventType.Rejected, exception);
            throw exception;
        }

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // 使用超时控制
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromMilliseconds(_options.TimeoutMs));

            var result = await operation().ConfigureAwait(false);
            
            stopwatch.Stop();
            OnSuccess(stopwatch.Elapsed);
            
            return result;
        }
        catch (OperationCanceledException ex) when (cancellationToken.IsCancellationRequested)
        {
            // 外部取消，不算作失败
            stopwatch.Stop();
            RecordEvent(CircuitBreakerEventType.Cancelled, ex);
            throw;
        }
        catch (OperationCanceledException ex)
        {
            // 超时
            stopwatch.Stop();
            OnFailure(ex, CircuitBreakerFailureType.Timeout, stopwatch.Elapsed);
            throw new TimeoutException($"操作超时 ({_options.TimeoutMs}ms)", ex);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var failureType = ClassifyException(ex);
            OnFailure(ex, failureType, stopwatch.Elapsed);
            throw;
        }
    }

    /// <summary>
    /// 执行受保护的操作（无返回值）
    /// </summary>
    public async Task ExecuteAsync(Func<Task> operation, CancellationToken cancellationToken = default)
    {
        await ExecuteAsync(async () =>
        {
            await operation().ConfigureAwait(false);
            return Task.CompletedTask;
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 手动重置断路器
    /// </summary>
    public void Reset()
    {
        lock (_stateLock)
        {
            TransitionToState(CircuitBreakerState.Closed, CircuitBreakerEventType.ManualReset);
            _consecutiveFailures = 0;
            _consecutiveSuccesses = 0;
            _lastException = null;
            
            _logger.LogInformation("断路器已手动重置");
        }
    }

    /// <summary>
    /// 手动打开断路器
    /// </summary>
    public void Trip()
    {
        lock (_stateLock)
        {
            TransitionToState(CircuitBreakerState.Open, CircuitBreakerEventType.ManualTrip);
            _logger.LogWarning("断路器已手动打开");
        }
    }

    /// <summary>
    /// 获取事件历史
    /// </summary>
    public IEnumerable<CircuitBreakerEvent> GetEventHistory(int maxCount = 100)
    {
        var events = new List<CircuitBreakerEvent>();
        var count = 0;
        
        while (_eventHistory.TryDequeue(out var circuitEvent) && count < maxCount)
        {
            events.Add(circuitEvent);
            count++;
        }
        
        return events.OrderByDescending(e => e.Timestamp);
    }

    #region 私有方法

    private bool CanExecute()
    {
        lock (_stateLock)
        {
            return _currentState switch
            {
                CircuitBreakerState.Closed => true,
                CircuitBreakerState.HalfOpen => true,
                CircuitBreakerState.Open => false,
                _ => false
            };
        }
    }

    private void OnSuccess(TimeSpan responseTime)
    {
        lock (_stateLock)
        {
            Interlocked.Increment(ref _totalRequests);
            Interlocked.Increment(ref _totalSuccesses);
            _consecutiveSuccesses++;
            _consecutiveFailures = 0;

            RecordEvent(CircuitBreakerEventType.Success, responseTime: responseTime);

            // 状态转换逻辑
            if (_currentState == CircuitBreakerState.HalfOpen)
            {
                if (_consecutiveSuccesses >= _options.SuccessThreshold)
                {
                    TransitionToState(CircuitBreakerState.Closed, CircuitBreakerEventType.Success);
                }
            }
        }
    }

    private void OnFailure(Exception exception, CircuitBreakerFailureType failureType, TimeSpan responseTime)
    {
        lock (_stateLock)
        {
            Interlocked.Increment(ref _totalRequests);
            Interlocked.Increment(ref _totalFailures);
            _consecutiveFailures++;
            _consecutiveSuccesses = 0;
            _lastFailureTime = DateTime.UtcNow;
            _lastException = exception;

            RecordEvent(CircuitBreakerEventType.Failure, exception, failureType, responseTime);

            // 状态转换逻辑
            if (_currentState == CircuitBreakerState.Closed || _currentState == CircuitBreakerState.HalfOpen)
            {
                if (ShouldTrip(failureType))
                {
                    TransitionToState(CircuitBreakerState.Open, CircuitBreakerEventType.Failure);
                }
            }
        }
    }

    private bool ShouldTrip(CircuitBreakerFailureType failureType)
    {
        // 根据失败类型调整触发逻辑
        var threshold = failureType switch
        {
            CircuitBreakerFailureType.Timeout => Math.Max(1, _options.FailureThreshold / 2), // 超时更敏感
            CircuitBreakerFailureType.Network => _options.FailureThreshold,
            CircuitBreakerFailureType.Service => _options.FailureThreshold,
            CircuitBreakerFailureType.Business => _options.FailureThreshold * 2, // 业务异常不那么敏感
            _ => _options.FailureThreshold
        };

        return _consecutiveFailures >= threshold;
    }

    private CircuitBreakerFailureType ClassifyException(Exception exception)
    {
        return exception switch
        {
            TimeoutException => CircuitBreakerFailureType.Timeout,
            OperationCanceledException => CircuitBreakerFailureType.Timeout,
            HttpRequestException => CircuitBreakerFailureType.Network,
            SocketException => CircuitBreakerFailureType.Network,
            InvalidOperationException when exception.Message.Contains("service") => CircuitBreakerFailureType.Service,
            ArgumentException => CircuitBreakerFailureType.Business,
            _ => CircuitBreakerFailureType.Unknown
        };
    }

    private void TransitionToState(CircuitBreakerState newState, CircuitBreakerEventType eventType)
    {
        if (_currentState == newState) return;

        var oldState = _currentState;
        _currentState = newState;
        _lastStateChangeTime = DateTime.UtcNow;

        _logger.LogInformation("断路器状态转换: {OldState} -> {NewState} (事件: {EventType})",
            oldState, newState, eventType);

        // 触发状态变更事件
        StateChanged?.Invoke(this, new CircuitBreakerStateChangedEventArgs
        {
            OldState = oldState,
            NewState = newState,
            EventType = eventType,
            Timestamp = _lastStateChangeTime
        });

        RecordEvent(CircuitBreakerEventType.StateChanged, newState: newState);
    }

    private void CheckStateTransition(object? state)
    {
        try
        {
            lock (_stateLock)
            {
                if (_currentState == CircuitBreakerState.Open)
                {
                    var timeSinceOpened = DateTime.UtcNow - _lastStateChangeTime;
                    if (timeSinceOpened >= TimeSpan.FromMilliseconds(_options.OpenTimeoutMs))
                    {
                        TransitionToState(CircuitBreakerState.HalfOpen, CircuitBreakerEventType.Timeout);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查断路器状态转换时发生错误");
        }
    }

    private void RecordEvent(CircuitBreakerEventType eventType, Exception? exception = null, 
        CircuitBreakerFailureType? failureType = null, TimeSpan? responseTime = null, 
        CircuitBreakerState? newState = null)
    {
        var circuitEvent = new CircuitBreakerEvent
        {
            EventType = eventType,
            Timestamp = DateTime.UtcNow,
            State = _currentState,
            NewState = newState,
            Exception = exception?.Message,
            FailureType = failureType,
            ResponseTime = responseTime
        };

        _eventHistory.Enqueue(circuitEvent);

        // 限制事件历史数量
        while (_eventHistory.Count > _options.MaxEventHistoryCount)
        {
            _eventHistory.TryDequeue(out _);
        }
    }

    private void ValidateOptions()
    {
        if (_options.FailureThreshold <= 0)
            throw new ArgumentException("故障阈值必须大于0");
        
        if (_options.OpenTimeoutMs <= 0)
            throw new ArgumentException("打开超时时间必须大于0");
        
        if (_options.TimeoutMs <= 0)
            throw new ArgumentException("操作超时时间必须大于0");
        
        if (_options.SuccessThreshold <= 0)
            throw new ArgumentException("成功阈值必须大于0");
    }

    private CircuitBreakerStateEnum MapToStateEnum(CircuitBreakerState state)
    {
        return state switch
        {
            CircuitBreakerState.Closed => CircuitBreakerStateEnum.Closed,
            CircuitBreakerState.Open => CircuitBreakerStateEnum.Open,
            CircuitBreakerState.HalfOpen => CircuitBreakerStateEnum.HalfOpen,
            _ => CircuitBreakerStateEnum.Closed
        };
    }

    #endregion

    public void Dispose()
    {
        _stateCheckTimer?.Dispose();
    }
}

/// <summary>
/// 智能断路器配置选项
/// </summary>
public class SmartCircuitBreakerOptions
{
    public const string SectionName = "SmartCircuitBreaker";

    /// <summary>
    /// 故障阈值（触发断路器打开的连续失败次数）
    /// </summary>
    public int FailureThreshold { get; set; } = 5;

    /// <summary>
    /// 成功阈值（半开状态下连续成功次数达到此值时关闭断路器）
    /// </summary>
    public int SuccessThreshold { get; set; } = 3;

    /// <summary>
    /// 断路器打开后的等待时间（毫秒）
    /// </summary>
    public int OpenTimeoutMs { get; set; } = 30000; // 30秒

    /// <summary>
    /// 操作超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; } = 10000; // 10秒

    /// <summary>
    /// 最大事件历史记录数量
    /// </summary>
    public int MaxEventHistoryCount { get; set; } = 1000;
}

/// <summary>
/// 断路器状态枚举
/// </summary>
public enum CircuitBreakerState
{
    /// <summary>
    /// 关闭状态 - 正常工作
    /// </summary>
    Closed,
    
    /// <summary>
    /// 半开状态 - 试探性允许请求
    /// </summary>
    HalfOpen,
    
    /// <summary>
    /// 打开状态 - 拒绝所有请求
    /// </summary>
    Open
}

/// <summary>
/// 断路器事件类型
/// </summary>
public enum CircuitBreakerEventType
{
    Success,
    Failure,
    Timeout,
    Rejected,
    Cancelled,
    StateChanged,
    ManualReset,
    ManualTrip
}

/// <summary>
/// 断路器失败类型
/// </summary>
public enum CircuitBreakerFailureType
{
    Unknown,
    Timeout,
    Network,
    Service,
    Business
}

/// <summary>
/// 断路器事件
/// </summary>
public class CircuitBreakerEvent
{
    public CircuitBreakerEventType EventType { get; set; }
    public DateTime Timestamp { get; set; }
    public CircuitBreakerState State { get; set; }
    public CircuitBreakerState? NewState { get; set; }
    public string? Exception { get; set; }
    public CircuitBreakerFailureType? FailureType { get; set; }
    public TimeSpan? ResponseTime { get; set; }
}


/// <summary>
/// 断路器状态变更事件参数
/// </summary>
public class CircuitBreakerStateChangedEventArgs : EventArgs
{
    public CircuitBreakerState OldState { get; set; }
    public CircuitBreakerState NewState { get; set; }
    public CircuitBreakerEventType EventType { get; set; }
    public DateTime Timestamp { get; set; }
}

