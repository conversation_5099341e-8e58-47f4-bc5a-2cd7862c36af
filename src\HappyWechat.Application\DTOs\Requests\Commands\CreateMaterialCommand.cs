using System.ComponentModel.DataAnnotations;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 创建素材命令
/// </summary>
public class CreateMaterialCommand
{
    /// <summary>
    /// 分类ID
    /// </summary>
    public Guid? CategoryId { get; set; }
    
    /// <summary>
    /// 素材类型
    /// </summary>
    [Required]
    public MaterialType Type { get; set; }
    
    /// <summary>
    /// 素材名称/标题
    /// </summary>
    [Required]
    [StringLength(100, ErrorMessage = "素材名称不能超过100个字符")]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 素材描述
    /// </summary>
    [StringLength(500, ErrorMessage = "素材描述不能超过500个字符")]
    public string? Description { get; set; }
    
    /// <summary>
    /// 文字内容（用于文字素材和链接素材）
    /// </summary>
    public string? Content { get; set; }
    
    /// <summary>
    /// 文件路径（用于文件类素材）
    /// </summary>
    public string? FilePath { get; set; }
    
    /// <summary>
    /// 文件URL（用于文件类素材）
    /// </summary>
    public string? FileUrl { get; set; }
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long? FileSize { get; set; }
    
    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string? FileExtension { get; set; }
    
    /// <summary>
    /// MIME类型
    /// </summary>
    public string? MimeType { get; set; }
    
    /// <summary>
    /// 缩略图路径（用于图片和视频）
    /// </summary>
    public string? ThumbnailPath { get; set; }
    
    /// <summary>
    /// 缩略图URL
    /// </summary>
    public string? ThumbnailUrl { get; set; }
    
    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}
