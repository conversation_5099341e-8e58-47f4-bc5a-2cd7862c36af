using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.Entities;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Identity.Repositories;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 微信管理器仓储实现
/// </summary>
public class WxManagerRepository : IWxManagerRepository
{
    private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
    private readonly ILogger<WxManagerRepository> _logger;

    public WxManagerRepository(
        IDbContextFactory<ApplicationDbContext> dbContextFactory,
        ILogger<WxManagerRepository> logger)
    {
        _dbContextFactory = dbContextFactory;
        _logger = logger;
    }

    public async Task<WxMangerEntity?> GetByIdAsync(Guid id)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.WxMangerEntities
                .FirstOrDefaultAsync(w => w.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取微信管理器失败 - Id: {Id}", id);
            return null;
        }
    }

    public async Task<WxMangerEntity?> GetByWIdAsync(string wId)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.WxMangerEntities
                .FirstOrDefaultAsync(w => w.WId == wId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据WId获取微信管理器失败 - WId: {WId}", wId);
            return null;
        }
    }

    public async Task<List<WxMangerEntity>> GetLoggedInManagersAsync()
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.WxMangerEntities
                .Where(w => w.WxStatus == WxStatus.AlreadyLogIn)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取已登录微信管理器失败");
            return new List<WxMangerEntity>();
        }
    }

    public async Task<List<WxMangerEntity>> GetByUserIdAsync(Guid userId)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.WxMangerEntities
                .Where(w => w.UserId == userId)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户ID获取微信管理器失败 - UserId: {UserId}", userId);
            return new List<WxMangerEntity>();
        }
    }

    public async Task<WxMangerEntity> CreateAsync(WxMangerEntity entity)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            entity.Id = Guid.NewGuid();
            entity.CreatedTime = DateTime.UtcNow;
            entity.UpdatedTime = DateTime.UtcNow;
            
            context.WxMangerEntities.Add(entity);
            await context.SaveChangesAsync();
            
            _logger.LogInformation("创建微信管理器成功 - Id: {Id}, NickName: {NickName}", entity.Id, entity.NickName);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建微信管理器失败 - NickName: {NickName}", entity.NickName);
            throw;
        }
    }

    public async Task<WxMangerEntity> UpdateAsync(WxMangerEntity entity)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            entity.UpdatedTime = DateTime.UtcNow;
            context.WxMangerEntities.Update(entity);
            await context.SaveChangesAsync();
            
            _logger.LogDebug("更新微信管理器成功 - Id: {Id}, NickName: {NickName}", entity.Id, entity.NickName);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新微信管理器失败 - Id: {Id}", entity.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var entity = await context.WxMangerEntities.FindAsync(id);
            if (entity == null)
            {
                _logger.LogWarning("要删除的微信管理器不存在 - Id: {Id}", id);
                return false;
            }
            
            context.WxMangerEntities.Remove(entity);
            await context.SaveChangesAsync();
            
            _logger.LogInformation("删除微信管理器成功 - Id: {Id}, NickName: {NickName}", id, entity.NickName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除微信管理器失败 - Id: {Id}", id);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.WxMangerEntities.AnyAsync(w => w.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查微信管理器是否存在失败 - Id: {Id}", id);
            return false;
        }
    }
}
