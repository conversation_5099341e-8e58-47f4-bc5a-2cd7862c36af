namespace HappyWechat.Application.Constants;

/// <summary>
/// 消息类型常量定义
/// 基于EYun API和流程图中的消息类型
/// </summary>
public static class MessageTypeConstants
{
    #region 私聊消息类型
    
    /// <summary>
    /// 私聊文本消息
    /// </summary>
    public const string PRIVATE_TEXT = "60001";
    
    /// <summary>
    /// 私聊图片消息
    /// </summary>
    public const string PRIVATE_IMAGE = "60002";
    
    /// <summary>
    /// 私聊语音消息
    /// </summary>
    public const string PRIVATE_VOICE = "60004";
    
    /// <summary>
    /// 🔧 重构：私聊文件发送完成消息 - 使用60009替代60008
    /// </summary>
    public const string PRIVATE_FILE = "60009";
    
    /// <summary>
    /// 私聊视频消息
    /// </summary>
    public const string PRIVATE_VIDEO = "60003";
    
    /// <summary>
    /// 私聊表情消息
    /// </summary>
    public const string PRIVATE_EMOJI = "60005";
    
    /// <summary>
    /// 私聊链接消息
    /// </summary>
    public const string PRIVATE_LINK = "60006";
    
    /// <summary>
    /// 私聊位置消息
    /// </summary>
    public const string PRIVATE_LOCATION = "60007";
    
    #endregion
    
    #region 群聊消息类型
    
    /// <summary>
    /// 群聊文本消息
    /// </summary>
    public const string GROUP_TEXT = "80001";
    
    /// <summary>
    /// 群聊图片消息
    /// </summary>
    public const string GROUP_IMAGE = "80002";
    
    /// <summary>
    /// 群聊语音消息
    /// </summary>
    public const string GROUP_VOICE = "80004";
    
    /// <summary>
    /// 🔧 重构：群聊文件发送完成消息 - 使用80009替代80008
    /// </summary>
    public const string GROUP_FILE = "80009";
    
    /// <summary>
    /// 群聊视频消息
    /// </summary>
    public const string GROUP_VIDEO = "80003";
    
    /// <summary>
    /// 群聊表情消息
    /// </summary>
    public const string GROUP_EMOJI = "80005";
    
    /// <summary>
    /// 群聊链接消息
    /// </summary>
    public const string GROUP_LINK = "80006";
    
    /// <summary>
    /// 群聊位置消息
    /// </summary>
    public const string GROUP_LOCATION = "80007";
    
    #endregion
    
    #region 系统消息类型
    
    /// <summary>
    /// 离线通知
    /// </summary>
    public const string OFFLINE_NOTIFICATION = "30000";
    
    /// <summary>
    /// 好友请求
    /// </summary>
    public const string FRIEND_REQUEST = "10001";
    
    /// <summary>
    /// 群邀请
    /// </summary>
    public const string GROUP_INVITE = "20001";
    
    /// <summary>
    /// 系统通知
    /// </summary>
    public const string SYSTEM_NOTIFICATION = "90001";
    
    #endregion
    
    #region 消息类型分组
    
    /// <summary>
    /// 所有私聊消息类型
    /// </summary>
    public static readonly string[] PRIVATE_MESSAGE_TYPES = 
    {
        PRIVATE_TEXT, PRIVATE_IMAGE, PRIVATE_VOICE, PRIVATE_FILE,
        PRIVATE_VIDEO, PRIVATE_EMOJI, PRIVATE_LINK, PRIVATE_LOCATION
    };
    
    /// <summary>
    /// 所有群聊消息类型
    /// </summary>
    public static readonly string[] GROUP_MESSAGE_TYPES = 
    {
        GROUP_TEXT, GROUP_IMAGE, GROUP_VOICE, GROUP_FILE,
        GROUP_VIDEO, GROUP_EMOJI, GROUP_LINK, GROUP_LOCATION
    };
    
    /// <summary>
    /// 需要文件下载的消息类型
    /// </summary>
    public static readonly string[] MEDIA_MESSAGE_TYPES = 
    {
        PRIVATE_IMAGE, PRIVATE_VOICE, PRIVATE_FILE, PRIVATE_VIDEO,
        GROUP_IMAGE, GROUP_VOICE, GROUP_FILE, GROUP_VIDEO
    };
    
    /// <summary>
    /// 系统消息类型
    /// </summary>
    public static readonly string[] SYSTEM_MESSAGE_TYPES = 
    {
        OFFLINE_NOTIFICATION, FRIEND_REQUEST, GROUP_INVITE, SYSTEM_NOTIFICATION
    };
    
    #endregion
    
    #region 辅助方法
    
    /// <summary>
    /// 判断是否为私聊消息
    /// </summary>
    public static bool IsPrivateMessage(string messageType)
    {
        return PRIVATE_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 判断是否为群聊消息
    /// </summary>
    public static bool IsGroupMessage(string messageType)
    {
        return GROUP_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 判断是否需要文件下载
    /// </summary>
    public static bool RequiresMediaDownload(string messageType)
    {
        return MEDIA_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 判断是否为系统消息
    /// </summary>
    public static bool IsSystemMessage(string messageType)
    {
        return SYSTEM_MESSAGE_TYPES.Contains(messageType);
    }
    
    /// <summary>
    /// 获取消息类型的描述
    /// </summary>
    public static string GetMessageTypeDescription(string messageType)
    {
        return messageType switch
        {
            PRIVATE_TEXT => "私聊文本",
            PRIVATE_IMAGE => "私聊图片",
            PRIVATE_VOICE => "私聊语音",
            PRIVATE_FILE => "私聊文件",
            PRIVATE_VIDEO => "私聊视频",
            PRIVATE_EMOJI => "私聊表情",
            PRIVATE_LINK => "私聊链接",
            PRIVATE_LOCATION => "私聊位置",
            
            GROUP_TEXT => "群聊文本",
            GROUP_IMAGE => "群聊图片",
            GROUP_VOICE => "群聊语音",
            GROUP_FILE => "群聊文件",
            GROUP_VIDEO => "群聊视频",
            GROUP_EMOJI => "群聊表情",
            GROUP_LINK => "群聊链接",
            GROUP_LOCATION => "群聊位置",
            
            OFFLINE_NOTIFICATION => "离线通知",
            FRIEND_REQUEST => "好友请求",
            GROUP_INVITE => "群邀请",
            SYSTEM_NOTIFICATION => "系统通知",
            
            _ => "未知消息类型"
        };
    }
    
    #endregion
}
