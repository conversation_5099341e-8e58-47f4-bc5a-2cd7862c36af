using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.JSInterop;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;

namespace HappyWechat.Infrastructure.Auth;

/// <summary>
/// Redis认证状态提供者 - 统一的Blazor认证状态管理
/// </summary>
public class RedisAuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly IRedisAuthenticationService _authService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ProtectedLocalStorage _localStorage;
    private readonly ILogger<RedisAuthenticationStateProvider> _logger;
    private readonly ClaimsPrincipal _anonymous;
    private AuthenticationState? _currentAuthState;
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private const string SessionStorageKey = "hw_session_id";

    public RedisAuthenticationStateProvider(
        IRedisAuthenticationService authService,
        IHttpContextAccessor httpContextAccessor,
        ProtectedLocalStorage localStorage,
        ILogger<RedisAuthenticationStateProvider> logger)
    {
        _authService = authService;
        _httpContextAccessor = httpContextAccessor;
        _localStorage = localStorage;
        _logger = logger;
        _anonymous = new ClaimsPrincipal(new ClaimsIdentity());
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            // 如果已有缓存的认证状态，直接返回
            if (_currentAuthState != null)
            {
                return _currentAuthState;
            }

            var sessionId = await GetSessionIdAsync();
            if (string.IsNullOrEmpty(sessionId))
            {
                _currentAuthState = new AuthenticationState(_anonymous);
                return _currentAuthState;
            }

            _currentAuthState = await _authService.GetAuthenticationStateAsync(sessionId);
            return _currentAuthState;
        }
        catch (JSDisconnectedException ex)
        {
            _logger.LogWarning("⚠️ Blazor Circuit已断开，返回匿名状态: {Error}", ex.Message);
            return new AuthenticationState(_anonymous);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogDebug("⚠️ 预渲染阶段或Circuit未就绪，返回匿名状态: {Error}", ex.Message);
            return new AuthenticationState(_anonymous);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取认证状态异常");
            return new AuthenticationState(_anonymous);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 通知认证状态已更改
    /// </summary>
    /// <param name="sessionId">新的会话ID</param>
    public async Task NotifyAuthenticationStateChangedAsync(string? sessionId = null)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("🔔 认证状态变更通知 - SessionId: {SessionId}", sessionId ?? "null");

            // 清除缓存的认证状态
            _currentAuthState = null;

            if (!string.IsNullOrEmpty(sessionId))
            {
                // 存储新的会话ID
                await StoreSessionIdAsync(sessionId);
                
                // 获取新的认证状态
                _currentAuthState = await _authService.GetAuthenticationStateAsync(sessionId);
            }
            else
            {
                // 清除会话ID
                await ClearSessionIdAsync();
                _currentAuthState = new AuthenticationState(_anonymous);
            }

            // 通知Blazor组件认证状态已更改
            NotifyAuthenticationStateChanged(Task.FromResult(_currentAuthState));

            _logger.LogInformation("✅ 认证状态变更完成 - IsAuthenticated: {IsAuthenticated}", 
                _currentAuthState.User.Identity?.IsAuthenticated == true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 通知认证状态变更异常");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 用户登录成功后调用
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    public async Task MarkUserAsAuthenticatedAsync(string sessionId)
    {
        await NotifyAuthenticationStateChangedAsync(sessionId);
    }

    /// <summary>
    /// 用户登出后调用
    /// </summary>
    public async Task MarkUserAsLoggedOutAsync()
    {
        await NotifyAuthenticationStateChangedAsync(null);
    }

    /// <summary>
    /// 获取当前会话ID
    /// </summary>
    /// <returns>会话ID</returns>
    public async Task<string?> GetCurrentSessionIdAsync()
    {
        return await GetSessionIdAsync();
    }

    /// <summary>
    /// 检查当前用户是否已认证
    /// </summary>
    /// <returns>是否已认证</returns>
    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            var authState = await GetAuthenticationStateAsync();
            return authState.User.Identity?.IsAuthenticated == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查认证状态异常");
            return false;
        }
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <returns>用户Claims</returns>
    public async Task<ClaimsPrincipal?> GetCurrentUserAsync()
    {
        try
        {
            var authState = await GetAuthenticationStateAsync();
            return authState.User.Identity?.IsAuthenticated == true ? authState.User : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取当前用户异常");
            return null;
        }
    }

    /// <summary>
    /// 强制刷新认证状态
    /// </summary>
    public async Task RefreshAuthenticationStateAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("🔄 强制刷新认证状态");
            
            // 清除缓存
            _currentAuthState = null;
            
            // 重新获取认证状态
            var authState = await GetAuthenticationStateAsync();
            
            // 通知状态变更
            NotifyAuthenticationStateChanged(Task.FromResult(authState));
            
            _logger.LogInformation("✅ 认证状态刷新完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 刷新认证状态异常");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private async Task<string?> GetSessionIdAsync()
    {
        try
        {
            // 优先从HTTP上下文获取
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                var sessionId = _authService.GetSessionIdFromContext(httpContext);
                if (!string.IsNullOrEmpty(sessionId))
                {
                    _logger.LogDebug("✅ 从HTTP上下文获取会话ID: {SessionId}", sessionId[..8] + "...");
                    return sessionId;
                }
                else
                {
                    _logger.LogDebug("⚠️ HTTP上下文中未找到会话ID");
                }
            }
            else
            {
                _logger.LogDebug("⚠️ HTTP上下文为空");
            }

            // 从本地存储获取
            try
            {
                var result = await _localStorage.GetAsync<string>(SessionStorageKey);
                if (result.Success && !string.IsNullOrEmpty(result.Value))
                {
                    _logger.LogDebug("✅ 从LocalStorage获取会话ID: {SessionId}", result.Value[..8] + "...");
                    return result.Value;
                }
                else
                {
                    _logger.LogDebug("⚠️ LocalStorage中未找到会话ID - Success: {Success}, Value: {Value}",
                        result.Success, string.IsNullOrEmpty(result.Value) ? "null/empty" : "has value");
                }
            }
            catch (JSDisconnectedException ex)
            {
                // Circuit断开时忽略本地存储访问
                _logger.LogDebug("⚠️ Circuit断开，无法访问LocalStorage: {Message}", ex.Message);
                return null;
            }
            catch (InvalidOperationException ex)
            {
                // 预渲染阶段忽略本地存储访问
                _logger.LogDebug("⚠️ 预渲染阶段，无法访问LocalStorage: {Message}", ex.Message);
                return null;
            }

            _logger.LogDebug("❌ 所有方式都未能获取到会话ID");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取会话ID异常");
            return null;
        }
    }

    private async Task StoreSessionIdAsync(string sessionId)
    {
        try
        {
            // 存储到HTTP上下文
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                _authService.SetSessionIdToContext(httpContext, sessionId);
            }

            // 存储到本地存储
            try
            {
                await _localStorage.SetAsync(SessionStorageKey, sessionId);
            }
            catch (JSDisconnectedException)
            {
                // Circuit断开时忽略本地存储访问
            }
            catch (InvalidOperationException)
            {
                // 预渲染阶段忽略本地存储访问
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 存储会话ID异常");
        }
    }

    private async Task ClearSessionIdAsync()
    {
        try
        {
            // 清除HTTP上下文
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                _authService.ClearSessionIdFromContext(httpContext);
            }

            // 清除本地存储
            try
            {
                await _localStorage.DeleteAsync(SessionStorageKey);
            }
            catch (JSDisconnectedException)
            {
                // Circuit断开时忽略本地存储访问
            }
            catch (InvalidOperationException)
            {
                // 预渲染阶段忽略本地存储访问
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 清除会话ID异常");
        }
    }
}
