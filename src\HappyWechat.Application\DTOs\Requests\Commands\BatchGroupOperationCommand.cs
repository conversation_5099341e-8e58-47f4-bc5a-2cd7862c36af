namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 批量群组操作命令
/// </summary>
public class BatchGroupOperationCommand
{
    /// <summary>
    /// 群组ID列表
    /// </summary>
    public List<Guid> GroupIds { get; set; } = new();
    
    /// <summary>
    /// 操作类型
    /// </summary>
    public BatchGroupOperationType OperationType { get; set; }
    
    /// <summary>
    /// 操作值（用于设置群组AI配置等）
    /// </summary>
    public object? OperationValue { get; set; }
    
    /// <summary>
    /// 操作参数（用于复杂操作）
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 批量群组操作类型
/// </summary>
public enum BatchGroupOperationType
{
    /// <summary>
    /// 设置群组AI自动回复
    /// </summary>
    SetGroupAi,
    
    /// <summary>
    /// 批量删除群组
    /// </summary>
    Delete,
    
    /// <summary>
    /// 批量更新群组备注
    /// </summary>
    UpdateRemark,
    
    /// <summary>
    /// 批量刷新群成员
    /// </summary>
    RefreshMembers,
    
    /// <summary>
    /// 批量同步群组信息
    /// </summary>
    Sync,
    
    /// <summary>
    /// 批量导出群组数据
    /// </summary>
    Export,
    
    /// <summary>
    /// 批量设置群组状态
    /// </summary>
    SetStatus
}

/// <summary>
/// 批量更新群组命令
/// </summary>
public class BatchUpdateGroupCommand
{
    /// <summary>
    /// 群组ID列表
    /// </summary>
    public List<Guid> GroupIds { get; set; } = new();

    /// <summary>
    /// AI自动回复启用状态
    /// </summary>
    public bool? IsAiEnabled { get; set; }

    /// <summary>
    /// 是否仅在@后回复
    /// </summary>
    public bool? OnlyReplyWhenMentioned { get; set; }

    /// <summary>
    /// 更新数据
    /// </summary>
    public Dictionary<string, object> UpdateData { get; set; } = new();

    /// <summary>
    /// 是否强制更新
    /// </summary>
    public bool ForceUpdate { get; set; } = false;
}