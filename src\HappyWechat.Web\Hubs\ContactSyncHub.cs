using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using HappyWechat.Application.DTOs.MessageQueue;
using HappyWechat.Infrastructure.Notifications;
using HappyWechat.Infrastructure.Auth;
using System.Security.Claims;

namespace HappyWechat.Web.Hubs;

/// <summary>
/// 联系人同步进度SignalR Hub - 使用Redis会话认证
/// </summary>
[Authorize(AuthenticationSchemes = SignalRRedisAuthenticationDefaults.AuthenticationScheme)]
public class ContactSyncHub : Hub
{
    private readonly ILogger<ContactSyncHub> _logger;
    private readonly IUnifiedRedisNotificationService _notificationService;

    public ContactSyncHub(
        ILogger<ContactSyncHub> logger,
        IUnifiedRedisNotificationService notificationService)
    {
        _logger = logger;
        _notificationService = notificationService;
    }

    /// <summary>
    /// 发送队列状态更新通知
    /// </summary>
    public async Task SendQueueStatusUpdate(string wxManagerId, string queueType, int messageCount)
    {
        try
        {
            await Clients.Group($"wx_{wxManagerId}").SendAsync("QueueStatusUpdate", new
            {
                WxManagerId = wxManagerId,
                QueueType = queueType,
                MessageCount = messageCount,
                Timestamp = DateTime.UtcNow
            });
            
            _logger.LogDebug("📊 队列状态更新已发送 - WxManagerId: {WxManagerId}, QueueType: {QueueType}, Count: {Count}",
                wxManagerId, queueType, messageCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发送队列状态更新失败 - WxManagerId: {WxManagerId}", wxManagerId);
        }
    }

    /// <summary>
    /// 客户端连接时调用 - 使用JWT认证
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var connectionId = Context.ConnectionId;
        var userAgent = Context.GetHttpContext()?.Request.Headers["User-Agent"].ToString();

        try
        {
            _logger.LogDebug("SignalR连接请求 - ConnectionId: {ConnectionId}, UserAgent: {UserAgent}",
                connectionId, userAgent?.Substring(0, Math.Min(userAgent.Length, 50)));

            // 从JWT Claims获取用户信息
            var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier) ?? Context.User?.FindFirst("sub");
            var usernameClaim = Context.User?.FindFirst(ClaimTypes.Name) ?? Context.User?.FindFirst("name");

            if (userIdClaim == null || string.IsNullOrEmpty(userIdClaim.Value))
            {
                _logger.LogWarning("SignalR连接认证失败：缺少用户ID - ConnectionId: {ConnectionId}", connectionId);

                await Clients.Caller.SendAsync("AuthenticationError", new
                {
                    Message = "身份认证失败，无法建立SignalR连接",
                    Code = "AUTHENTICATION_FAILED",
                    Reason = "缺少有效的JWT认证",
                    Timestamp = DateTime.UtcNow,
                    SuggestedAction = "请重新登录后再试",
                    ConnectionId = connectionId
                });

                Context.Abort();
                return;
            }

            var userId = userIdClaim.Value;
            var username = usernameClaim?.Value ?? userId;

            _logger.LogDebug("SignalR连接认证成功 - UserId: {UserId}, ConnectionId: {ConnectionId}",
                userId, connectionId);

            // 将用户加入到个人组
            await Groups.AddToGroupAsync(connectionId, $"User_{userId}");

            // TODO: 如果需要wcId组功能，需要从其他服务获取wcId信息
            // 这里暂时简化，只加入用户个人组

            // 发送连接确认
            await Clients.Caller.SendAsync("ConnectionConfirmed", new
            {
                ConnectionId = connectionId,
                UserId = userId,
                Username = username,
                ConnectedAt = DateTime.UtcNow,
                IsAuthenticated = true,
                AuthenticationMethod = "JWT"
            });

            await base.OnConnectedAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SignalR连接处理异常 - ConnectionId: {ConnectionId}", connectionId);

            await Clients.Caller.SendAsync("AuthenticationError", new
            {
                Message = "连接处理异常",
                Code = "CONNECTION_ERROR",
                Reason = ex.Message
            });

            Context.Abort();
        }
    }

    /// <summary>
    /// 客户端断开连接时调用
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var connectionId = Context.ConnectionId;
        var userId = Context.UserIdentifier ?? "未知";

        try
        {
            if (exception != null)
            {
                _logger.LogWarning(exception, "SignalR连接异常断开 - UserId: {UserId}, ConnectionId: {ConnectionId}",
                    userId, connectionId);
            }
            else
            {
                _logger.LogDebug("SignalR连接正常断开 - UserId: {UserId}, ConnectionId: {ConnectionId}",
                    userId, connectionId);
            }

            await base.OnDisconnectedAsync(exception);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理SignalR断开连接异常 - ConnectionId: {ConnectionId}", connectionId);
            await base.OnDisconnectedAsync(exception);
        }
    }

    /// <summary>
    /// 加入微信账号同步组
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns></returns>
    public async Task JoinWxManagerGroup(string wxManagerId)
    {
        var userId = Context.UserIdentifier;
        var connectionId = Context.ConnectionId;
        
        // 验证用户身份（由于使用了[Authorize]，这里应该总是有用户ID）
        if (string.IsNullOrEmpty(userId))
        {
            _logger.LogWarning("未认证用户尝试加入微信管理器组 - ConnectionId: {ConnectionId}, WxManagerId: {WxManagerId}",
                connectionId, wxManagerId);
            await Clients.Caller.SendAsync("Error", "用户未认证，无法加入微信管理器组");
            return;
        }

        // 验证wxManagerId格式
        if (!Guid.TryParse(wxManagerId, out var managerGuid))
        {
            _logger.LogWarning("无效的微信管理器ID - UserId: {UserId}, WxManagerId: {WxManagerId}", userId, wxManagerId);
            await Clients.Caller.SendAsync("Error", "无效的微信管理器ID");
            return;
        }

        var groupName = $"WxManager_{wxManagerId}";
        await Groups.AddToGroupAsync(connectionId, groupName);

        _logger.LogDebug("用户加入微信管理器组 - UserId: {UserId}, ConnectionId: {ConnectionId}, GroupName: {GroupName}",
            userId, connectionId, groupName);
        
        // 发送加入成功确认
        await Clients.Caller.SendAsync("JoinedWxManagerGroup", new 
        { 
            WxManagerId = wxManagerId,
            GroupName = groupName,
            JoinedAt = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 离开微信账号同步组
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns></returns>
    public async Task LeaveWxManagerGroup(string wxManagerId)
    {
        var groupName = $"WxManager_{wxManagerId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogDebug("连接 {ConnectionId} 离开组 {GroupName}", Context.ConnectionId, groupName);
    }

    /// <summary>
    /// 客户端请求当前进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <returns></returns>
    public async Task RequestCurrentProgress(string wxManagerId)
    {
        _logger.LogDebug("客户端请求微信账号 {WxManagerId} 的当前进度", wxManagerId);
        
        // 这里可以从缓存或数据库获取当前进度并发送给客户端
        // 暂时发送一个默认状态
        await Clients.Caller.SendAsync("ProgressUpdate", new ContactSyncProgressMessage
        {
            WxManagerId = Guid.Parse(wxManagerId),
            Status = HappyWechat.Domain.ValueObjects.Enums.SyncStatus.NotStarted,
            TotalCount = 0,
            ProcessedCount = 0,
            SuccessCount = 0,
            FailedCount = 0,
            UpdatedAt = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 加入批量操作组
    /// </summary>
    /// <param name="operationId">批量操作ID</param>
    /// <returns></returns>
    public async Task JoinBatchOperationGroup(string operationId)
    {
        var groupName = $"BatchOperation_{operationId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogDebug("连接 {ConnectionId} 加入批量操作组 {GroupName}", Context.ConnectionId, groupName);
    }

    /// <summary>
    /// 离开批量操作组
    /// </summary>
    /// <param name="operationId">批量操作ID</param>
    /// <returns></returns>
    public async Task LeaveBatchOperationGroup(string operationId)
    {
        var groupName = $"BatchOperation_{operationId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogDebug("连接 {ConnectionId} 离开批量操作组 {GroupName}", Context.ConnectionId, groupName);
    }

    /// <summary>
    /// 请求批量操作当前进度
    /// </summary>
    /// <param name="operationId">批量操作ID</param>
    /// <returns></returns>
    public async Task RequestBatchOperationProgress(string operationId)
    {
        _logger.LogDebug("客户端请求批量操作 {OperationId} 的当前进度", operationId);
        
        // 这里可以从缓存或数据库获取当前进度并发送给客户端
        // 暂时发送一个默认状态
        await Clients.Caller.SendAsync("BatchOperationProgress", new Application.DTOs.Responses.BatchOperationProgressDto
        {
            OperationId = Guid.Parse(operationId),
            Status = Application.DTOs.Responses.BatchOperationStatus.Pending,
            TotalCount = 0,
            ProcessedCount = 0,
            SuccessCount = 0,
            FailedCount = 0,
            StartTime = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 发送心跳包
    /// </summary>
    /// <returns></returns>
    public async Task Ping()
    {
        await Clients.Caller.SendAsync("Pong", DateTime.UtcNow);
    }

    /// <summary>
    /// 连接诊断 - 检查用户连接状态
    /// </summary>
    /// <returns></returns>
    public async Task DiagnoseConnection()
    {
        var connectionId = Context.ConnectionId;
        var userId = Context.UserIdentifier ?? "未知";

        try
        {
            // 简化的连接诊断 - 不依赖外部服务
            var isAuthenticated = Context.User?.Identity?.IsAuthenticated ?? false;

            var diagnosticInfo = new
            {
                ConnectionId = connectionId,
                UserId = userId,
                IsAuthenticated = isAuthenticated,
                UserName = Context.User?.Identity?.Name ?? "Unknown",
                DiagnosedAt = DateTime.UtcNow,
                Message = "连接诊断完成"
            };

            await Clients.Caller.SendAsync("ConnectionDiagnostic", diagnosticInfo);

            // 🔧 注释冗余的连接诊断完成日志 - 减少日志噪音，每次连接都会触发
            // _logger.LogInformation("🔍 连接诊断完成 - UserId: {UserId}, ConnectionId: {ConnectionId}, IsAuthenticated: {IsAuthenticated}",
            //     userId, connectionId, isAuthenticated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 连接诊断失败 - UserId: {UserId}, ConnectionId: {ConnectionId}", userId, connectionId);

            await Clients.Caller.SendAsync("ConnectionDiagnostic", new
            {
                ConnectionId = connectionId,
                UserId = userId,
                Error = ex.Message,
                DiagnosedAt = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 测试通知发送
    /// </summary>
    /// <param name="testMessage">测试消息</param>
    /// <returns></returns>
    public async Task TestNotification(string testMessage = "测试通知")
    {
        var userId = Context.UserIdentifier ?? "未知";
        var connectionId = Context.ConnectionId;

        var testData = new
        {
            Message = testMessage,
            UserId = userId,
            ConnectionId = connectionId,
            SentAt = DateTime.UtcNow
        };

        // 发送测试消息
        try
        {
            await Clients.User(userId).SendAsync("TestNotification", testData);
            _logger.LogInformation("📤 测试通知发送成功 - UserId: {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "📤 测试通知发送失败 - UserId: {UserId}", userId);
        }

        // 同时直接发送确认
        await Clients.Caller.SendAsync("TestNotificationResult", new
        {
            Success = true,
            TestData = testData,
            ResultAt = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知缓存已刷新，UI需要重新加载数据 - 使用统一通知服务
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    public async Task NotifyCacheRefresh(string wxManagerId, string dataType = "contact")
    {
        if (Guid.TryParse(wxManagerId, out var managerId))
        {
            await _notificationService.PublishUIRefreshAsync(managerId, dataType);
            _logger.LogDebug("通过统一通知服务发送UI刷新通知 - ManagerId: {ManagerId}, DataType: {DataType}", managerId, dataType);
        }
        else
        {
            _logger.LogWarning("无效的微信管理器ID: {WxManagerId}", wxManagerId);
        }
    }

    // 批量操作现在使用统一通知服务，不再需要Hub直接通知
    // NotifyBatchOperationCompleted 方法已移除，使用 IUnifiedRedisNotificationService.PublishBatchOperationAsync
}
