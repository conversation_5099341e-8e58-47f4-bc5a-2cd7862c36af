namespace HappyWechat.Infrastructure.MessageQueue.Simplified.Models;

/// <summary>
/// 群组同步消息模型
/// 用于队列处理群组同步任务
/// </summary>
public class GroupSyncMessage
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 同步操作类型
    /// </summary>
    public GroupSyncOperation Operation { get; set; }

    /// <summary>
    /// 需要同步的群组ID列表
    /// </summary>
    public List<string> GroupIds { get; set; } = new();

    /// <summary>
    /// 单个群组ID（用于单群组操作）
    /// </summary>
    public string? GroupId { get; set; }

    /// <summary>
    /// 同步会话ID（用于跟踪同步进度）
    /// </summary>
    public string SyncSessionId { get; set; } = string.Empty;

    /// <summary>
    /// 批次索引（用于批量处理）
    /// </summary>
    public int BatchIndex { get; set; }

    /// <summary>
    /// 总批次数
    /// </summary>
    public int TotalBatches { get; set; }

    /// <summary>
    /// 是否包含成员信息同步
    /// </summary>
    public bool IncludeMembers { get; set; } = true;

    /// <summary>
    /// API调用最小间隔（毫秒）
    /// </summary>
    public int MinIntervalMs { get; set; } = 300;

    /// <summary>
    /// API调用最大间隔（毫秒）
    /// </summary>
    public int MaxIntervalMs { get; set; } = 1500;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 群组同步操作类型
/// </summary>
public enum GroupSyncOperation
{
    /// <summary>
    /// 全量同步
    /// </summary>
    FullSync = 1,

    /// <summary>
    /// 增量同步
    /// </summary>
    IncrementalSync = 2,

    /// <summary>
    /// 单个群组同步
    /// </summary>
    SingleGroup = 3,

    /// <summary>
    /// 群组成员同步
    /// </summary>
    GroupMembers = 4,

    /// <summary>
    /// 群组信息更新
    /// </summary>
    GroupUpdate = 5,

    /// <summary>
    /// 群组删除
    /// </summary>
    GroupDelete = 6
}
