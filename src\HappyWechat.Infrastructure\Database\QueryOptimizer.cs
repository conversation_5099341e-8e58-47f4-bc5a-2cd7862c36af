using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Linq.Expressions;
using System.Collections.Concurrent;
using HappyWechat.Infrastructure.Extensions;

namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// 查询优化器选项
/// </summary>
public class QueryOptimizerOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "QueryOptimizer";

    /// <summary>
    /// 是否启用查询缓存
    /// </summary>
    public bool EnableQueryCache { get; set; } = true;

    /// <summary>
    /// 查询缓存过期时间（分钟）
    /// </summary>
    public int QueryCacheExpirationMinutes { get; set; } = 15;

    /// <summary>
    /// 最大批量查询大小
    /// </summary>
    public int MaxBatchSize { get; set; } = 1000;

    /// <summary>
    /// 是否启用自动Include优化
    /// </summary>
    public bool EnableAutoIncludeOptimization { get; set; } = true;

    /// <summary>
    /// 慢查询阈值（毫秒）
    /// </summary>
    public int SlowQueryThresholdMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用查询分析
    /// </summary>
    public bool EnableQueryAnalysis { get; set; } = true;
}

/// <summary>
/// 查询优化器接口
/// </summary>
public interface IQueryOptimizer
{
    /// <summary>
    /// 优化查询以避免N+1问题
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">原始查询</param>
    /// <param name="includes">包含的导航属性</param>
    /// <returns>优化后的查询</returns>
    IQueryable<T> OptimizeQuery<T>(IQueryable<T> query, params Expression<Func<T, object>>[] includes) where T : class;

    /// <summary>
    /// 批量查询优化
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="context">数据库上下文</param>
    /// <param name="keys">键列表</param>
    /// <param name="keySelector">键选择器</param>
    /// <param name="includes">包含的导航属性</param>
    /// <returns>批量查询结果</returns>
    Task<List<T>> BatchQueryAsync<T, TKey>(
        DbContext context,
        IEnumerable<TKey> keys,
        Expression<Func<T, TKey>> keySelector,
        params Expression<Func<T, object>>[] includes) where T : class;

    /// <summary>
    /// 分页查询优化
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="includes">包含的导航属性</param>
    /// <returns>分页结果</returns>
    Task<PagedResult<T>> PagedQueryAsync<T>(
        IQueryable<T> query,
        int pageIndex,
        int pageSize,
        params Expression<Func<T, object>>[] includes) where T : class;

    /// <summary>
    /// 获取查询统计信息
    /// </summary>
    /// <returns>查询统计</returns>
    QueryStatistics GetQueryStatistics();
}

/// <summary>
/// 查询优化器实现
/// </summary>
public class QueryOptimizer : IQueryOptimizer
{
    private readonly ILogger<QueryOptimizer> _logger;
    private readonly QueryOptimizerOptions _options;
    private readonly ConcurrentDictionary<string, object> _queryCache = new();
    private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps = new();
    
    // 统计信息
    private long _totalQueries = 0;
    private long _optimizedQueries = 0;
    private long _cacheHits = 0;
    private long _cacheMisses = 0;
    private long _slowQueries = 0;
    private readonly ConcurrentDictionary<string, long> _queryExecutionTimes = new();

    public QueryOptimizer(
        ILogger<QueryOptimizer> logger,
        IOptions<QueryOptimizerOptions> options)
    {
        _logger = logger;
        _options = options.Value;

        // _logger.LogInformation("查询优化器已初始化");
    }

    /// <summary>
    /// 优化查询以避免N+1问题
    /// </summary>
    public IQueryable<T> OptimizeQuery<T>(IQueryable<T> query, params Expression<Func<T, object>>[] includes) where T : class
    {
        try
        {
            Interlocked.Increment(ref _totalQueries);

            if (!_options.EnableAutoIncludeOptimization || !includes.Any())
            {
                return query;
            }

            var optimizedQuery = query;

            // 添加Include以避免N+1查询
            foreach (var include in includes)
            {
                optimizedQuery = optimizedQuery.Include(include);
            }

            Interlocked.Increment(ref _optimizedQueries);
            _logger.LogDebug("查询已优化，添加了 {IncludeCount} 个Include", includes.Length);

            return optimizedQuery;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询优化失败");
            return query;
        }
    }

    /// <summary>
    /// 批量查询优化
    /// </summary>
    public async Task<List<T>> BatchQueryAsync<T, TKey>(
        DbContext context,
        IEnumerable<TKey> keys,
        Expression<Func<T, TKey>> keySelector,
        params Expression<Func<T, object>>[] includes) where T : class
    {
        try
        {
            var keyList = keys.ToList();
            if (!keyList.Any())
            {
                return new List<T>();
            }

            Interlocked.Increment(ref _totalQueries);

            var startTime = DateTime.UtcNow;
            var cacheKey = GenerateCacheKey(typeof(T).Name, "batch", keyList.GetHashCode().ToString());

            // 检查缓存
            if (_options.EnableQueryCache && TryGetFromCache<List<T>>(cacheKey, out var cachedResult))
            {
                Interlocked.Increment(ref _cacheHits);
                return cachedResult;
            }

            Interlocked.Increment(ref _cacheMisses);

            // 分批查询以避免参数过多
            var results = new List<T>();
            var batches = keyList.Chunk(_options.MaxBatchSize);

            foreach (var batch in batches)
            {
                var query = context.Set<T>().Where(BuildContainsExpression(keySelector, batch));
                
                // 添加Include
                foreach (var include in includes)
                {
                    query = query.Include(include);
                }

                var batchResults = await query.ToListAsync();
                results.AddRange(batchResults);
            }

            var executionTime = DateTime.UtcNow - startTime;
            RecordQueryExecution(cacheKey, executionTime);

            // 缓存结果
            if (_options.EnableQueryCache)
            {
                AddToCache(cacheKey, results);
            }

            Interlocked.Increment(ref _optimizedQueries);
            _logger.LogDebug("批量查询完成，查询了 {KeyCount} 个键，返回 {ResultCount} 个结果", keyList.Count, results.Count);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量查询失败");
            return new List<T>();
        }
    }

    /// <summary>
    /// 分页查询优化 - 简化版本，移除缓存机制确保数据一致性
    /// </summary>
    public async Task<PagedResult<T>> PagedQueryAsync<T>(
        IQueryable<T> query,
        int pageIndex,
        int pageSize,
        params Expression<Func<T, object>>[] includes) where T : class
    {
        try
        {
            Interlocked.Increment(ref _totalQueries);
            var startTime = DateTime.UtcNow;

            _logger.LogDebug("开始分页查询 - 页索引: {PageIndex}, 页大小: {PageSize}", pageIndex, pageSize);

            // 应用Include优化（仅用于预加载，不影响数据过滤）
            var optimizedQuery = query;
            if (_options.EnableAutoIncludeOptimization && includes.Any())
            {
                foreach (var include in includes)
                {
                    optimizedQuery = optimizedQuery.Include(include);
                }
                _logger.LogDebug("已应用 {IncludeCount} 个Include优化", includes.Length);
            }

            // 获取总数（使用原始查询确保一致性）
            var totalCount = await query.CountAsync();
            _logger.LogDebug("总数查询完成 - TotalCount: {TotalCount}", totalCount);

            // 获取分页数据（使用优化后的查询）
            // 🔧 修复：添加默认排序以避免EF Core警告
            var orderedQuery = optimizedQuery;
            if (!(optimizedQuery is IOrderedQueryable<T>))
            {
                // 尝试按Id属性排序，如果没有Id则按第一个属性排序
                var entityType = typeof(T);
                var idProperty = entityType.GetProperty("Id") ?? entityType.GetProperties().FirstOrDefault();
                if (idProperty != null)
                {
                    var parameter = Expression.Parameter(entityType, "x");
                    var property = Expression.Property(parameter, idProperty);
                    var lambda = Expression.Lambda(property, parameter);

                    var orderByMethod = typeof(Queryable).GetMethods()
                        .First(m => m.Name == "OrderBy" && m.GetParameters().Length == 2);
                    var genericOrderBy = orderByMethod.MakeGenericMethod(entityType, idProperty.PropertyType);

                    orderedQuery = (IQueryable<T>)genericOrderBy.Invoke(null, new object[] { optimizedQuery, lambda });
                }
            }

            var items = await orderedQuery
                .Skip(pageIndex * pageSize)
                .Take(pageSize)
                .ToListAsync();

            _logger.LogDebug("分页数据查询完成 - 实际返回: {ActualCount} 条记录", items.Count);

            var result = new PagedResult<T>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            var executionTime = DateTime.UtcNow - startTime;
            RecordQueryExecution($"paged_{typeof(T).Name}_{pageIndex}_{pageSize}", executionTime);

            Interlocked.Increment(ref _optimizedQueries);
            _logger.LogDebug("分页查询完成: 页索引 {PageIndex}, 页大小 {PageSize}, 总数 {TotalCount}, 返回 {ActualCount}, 执行时间 {ExecutionTime}ms",
                pageIndex, pageSize, totalCount, items.Count, executionTime.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页查询失败 - 页索引: {PageIndex}, 页大小: {PageSize}", pageIndex, pageSize);
            return new PagedResult<T> { Items = new List<T>() };
        }
    }

    /// <summary>
    /// 获取查询统计信息
    /// </summary>
    public QueryStatistics GetQueryStatistics()
    {
        return new QueryStatistics
        {
            TotalQueries = _totalQueries,
            OptimizedQueries = _optimizedQueries,
            CacheHits = _cacheHits,
            CacheMisses = _cacheMisses,
            SlowQueries = _slowQueries,
            CacheHitRate = _totalQueries > 0 ? (double)_cacheHits / (_cacheHits + _cacheMisses) : 0,
            OptimizationRate = _totalQueries > 0 ? (double)_optimizedQueries / _totalQueries : 0,
            AverageExecutionTimeMs = _queryExecutionTimes.Values.Any() ? _queryExecutionTimes.Values.Average() : 0
        };
    }

    /// <summary>
    /// 构建Contains表达式
    /// </summary>
    private static Expression<Func<T, bool>> BuildContainsExpression<T, TKey>(
        Expression<Func<T, TKey>> keySelector,
        IEnumerable<TKey> keys)
    {
        var keyList = keys.ToList();
        var parameter = keySelector.Parameters[0];
        var member = keySelector.Body;

        var containsMethod = typeof(List<TKey>).GetMethod("Contains", new[] { typeof(TKey) });
        var keyListExpression = Expression.Constant(keyList);
        var containsExpression = Expression.Call(keyListExpression, containsMethod!, member);

        return Expression.Lambda<Func<T, bool>>(containsExpression, parameter);
    }

    /// <summary>
    /// 生成缓存键
    /// </summary>
    private static string GenerateCacheKey(params string[] parts)
    {
        return string.Join(":", parts);
    }

    /// <summary>
    /// 尝试从缓存获取
    /// </summary>
    private bool TryGetFromCache<T>(string key, out T result)
    {
        result = default!;

        if (!_queryCache.TryGetValue(key, out var cachedValue))
        {
            return false;
        }

        if (!_cacheTimestamps.TryGetValue(key, out var timestamp))
        {
            return false;
        }

        // 检查是否过期
        if (DateTime.UtcNow - timestamp > TimeSpan.FromMinutes(_options.QueryCacheExpirationMinutes))
        {
            _queryCache.TryRemove(key, out _);
            _cacheTimestamps.TryRemove(key, out _);
            return false;
        }

        result = (T)cachedValue;
        return true;
    }

    /// <summary>
    /// 添加到缓存
    /// </summary>
    private void AddToCache<T>(string key, T value)
    {
        _queryCache[key] = value!;
        _cacheTimestamps[key] = DateTime.UtcNow;
    }

    /// <summary>
    /// 记录查询执行时间
    /// </summary>
    private void RecordQueryExecution(string queryKey, TimeSpan executionTime)
    {
        var executionTimeMs = (long)executionTime.TotalMilliseconds;
        _queryExecutionTimes[queryKey] = executionTimeMs;

        if (executionTimeMs > _options.SlowQueryThresholdMs)
        {
            Interlocked.Increment(ref _slowQueries);
            _logger.LogWarning("检测到慢查询: {QueryKey}, 执行时间: {ExecutionTime}ms", queryKey, executionTimeMs);
        }

        if (_options.EnableQueryAnalysis)
        {
            _logger.LogDebug("查询执行完成: {QueryKey}, 执行时间: {ExecutionTime}ms", queryKey, executionTimeMs);
        }
    }
}

#region 数据模型

/// <summary>
/// 分页结果
/// </summary>
public class PagedResult<T>
{
    /// <summary>
    /// 数据项
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页索引
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageIndex > 0;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageIndex < TotalPages - 1;
}

/// <summary>
/// 查询统计信息
/// </summary>
public class QueryStatistics
{
    /// <summary>
    /// 总查询数
    /// </summary>
    public long TotalQueries { get; set; }

    /// <summary>
    /// 优化查询数
    /// </summary>
    public long OptimizedQueries { get; set; }

    /// <summary>
    /// 缓存命中数
    /// </summary>
    public long CacheHits { get; set; }

    /// <summary>
    /// 缓存未命中数
    /// </summary>
    public long CacheMisses { get; set; }

    /// <summary>
    /// 慢查询数
    /// </summary>
    public long SlowQueries { get; set; }

    /// <summary>
    /// 缓存命中率
    /// </summary>
    public double CacheHitRate { get; set; }

    /// <summary>
    /// 优化率
    /// </summary>
    public double OptimizationRate { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }
}

#endregion
