using Microsoft.EntityFrameworkCore;
using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.Entities;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Database;
using HappyWechat.Infrastructure.Extensions;

namespace HappyWechat.Infrastructure.Material;

/// <summary>
/// 素材仓储实现 - 支持读写分离
/// </summary>
public class MaterialRepository : IMaterialRepository
{
    private readonly ApplicationDbContext _context;
    private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
    private readonly IDbContextFactory _readWriteDbContextFactory;

    public MaterialRepository(
        ApplicationDbContext context,
        IDbContextFactory<ApplicationDbContext> dbContextFactory,
        IDbContextFactory readWriteDbContextFactory)
    {
        _context = context;
        _dbContextFactory = dbContextFactory;
        _readWriteDbContextFactory = readWriteDbContextFactory;
    }

    public async Task<MaterialEntity> CreateAsync(MaterialEntity material)
    {
        // 写操作使用写库
        using var context = await _readWriteDbContextFactory.CreateWriteContextAsync();
        material.Id = Guid.NewGuid();
        material.CreatedAt = DateTime.UtcNow;
        material.UpdatedAt = DateTime.UtcNow;

        context.MaterialEntities.Add(material);
        await context.SaveChangesAsync();
        return material;
    }

    public async Task<MaterialEntity> UpdateAsync(MaterialEntity material)
    {
        // 写操作使用写库
        using var context = await _readWriteDbContextFactory.CreateWriteContextAsync();
        material.UpdatedAt = DateTime.UtcNow;

        context.MaterialEntities.Update(material);
        await context.SaveChangesAsync();
        return material;
    }

    public async Task<bool> DeleteAsync(Guid id, Guid userId)
    {
        // 写操作使用写库
        using var context = await _readWriteDbContextFactory.CreateWriteContextAsync();
        var material = await context.MaterialEntities
            .FirstOrDefaultAsync(m => m.Id == id && m.UserId == userId);

        if (material == null)
            return false;

        context.MaterialEntities.Remove(material);
        await context.SaveChangesAsync();
        return true;
    }

    public async Task<MaterialEntity?> GetByIdAsync(Guid id, Guid userId)
    {
        // 读操作使用读库
        using var context = await _readWriteDbContextFactory.CreateReadContextAsync();
        return await context.MaterialEntities
            .Include(m => m.Category)
            .FirstOrDefaultAsync(m => m.Id == id && m.UserId == userId);
    }

    public async Task<PageResponse<MaterialEntity>> GetPagedListAsync(Guid userId, MaterialQuery query)
    {
        // 读操作使用读库
        using var context = await _readWriteDbContextFactory.CreateReadContextAsync();
        var queryable = context.MaterialEntities
            .Include(m => m.Category)
            .Where(m => m.UserId == userId);

        // 应用过滤条件
        if (query.Type.HasValue)
        {
            queryable = queryable.Where(m => m.Type == query.Type.Value);
        }

        if (query.CategoryId.HasValue)
        {
            queryable = queryable.Where(m => m.CategoryId == query.CategoryId.Value);
        }

        if (!string.IsNullOrWhiteSpace(query.Keyword))
        {
            var keyword = query.Keyword.Trim().ToLower();
            queryable = queryable.Where(m =>
                m.Name.ToLower().Contains(keyword) ||
                (m.Description != null && m.Description.ToLower().Contains(keyword)) ||
                (m.Content != null && m.Content.ToLower().Contains(keyword)));
        }

        if (query.Tags.Any())
        {
            foreach (var tag in query.Tags)
            {
                queryable = queryable.Where(m => m.Tags != null && m.Tags.Contains(tag));
            }
        }

        if (query.CreatedFrom.HasValue)
        {
            queryable = queryable.Where(m => m.CreatedAt >= query.CreatedFrom.Value);
        }

        if (query.CreatedTo.HasValue)
        {
            queryable = queryable.Where(m => m.CreatedAt <= query.CreatedTo.Value);
        }

        // 应用排序
        var orderedQueryable = query.SortField switch
        {
            MaterialSortField.Name => query.SortDirection == SortDirection.Ascending
                ? queryable.OrderBy(m => m.Name)
                : queryable.OrderByDescending(m => m.Name),
            MaterialSortField.UpdatedAt => query.SortDirection == SortDirection.Ascending
                ? queryable.OrderBy(m => m.UpdatedAt)
                : queryable.OrderByDescending(m => m.UpdatedAt),
            MaterialSortField.UseCount => query.SortDirection == SortDirection.Ascending
                ? queryable.OrderBy(m => m.UseCount)
                : queryable.OrderByDescending(m => m.UseCount),
            MaterialSortField.LastUsedAt => query.SortDirection == SortDirection.Ascending
                ? queryable.OrderBy(m => m.LastUsedAt)
                : queryable.OrderByDescending(m => m.LastUsedAt),
            MaterialSortField.FileSize => query.SortDirection == SortDirection.Ascending
                ? queryable.OrderBy(m => m.FileSize)
                : queryable.OrderByDescending(m => m.FileSize),
            _ => query.SortDirection == SortDirection.Ascending
                ? queryable.OrderBy(m => m.CreatedAt)
                : queryable.OrderByDescending(m => m.CreatedAt)
        };

        var totalCount = await queryable.CountAsync();
        // 🔧 修复：使用已排序的查询进行分页，确保结果一致性
        var items = await orderedQueryable
            .ApplyPagination(query.PageQuery.Page, query.PageQuery.PageSize)
            .ToListAsync();

        return new PageResponse<MaterialEntity>
        {
            Items = items,
            TotalCount = totalCount,
            Page = query.PageQuery.Page,
            PageSize = query.PageQuery.PageSize
        };
    }

    public async Task<bool> IncrementUseCountAsync(Guid id, Guid userId)
    {
        // Use factory to create independent DbContext for this operation
        using var context = await _dbContextFactory.CreateDbContextAsync();
        var material = await context.MaterialEntities
            .FirstOrDefaultAsync(m => m.Id == id && m.UserId == userId);

        if (material == null)
            return false;

        material.UseCount++;
        material.LastUsedAt = DateTime.UtcNow;

        await context.SaveChangesAsync();
        return true;
    }

    public async Task<int> BatchDeleteAsync(List<Guid> ids, Guid userId)
    {
        // Use factory to create independent DbContext for this operation
        using var context = await _dbContextFactory.CreateDbContextAsync();

        var materials = await context.MaterialEntities
            .Where(m => ids.Contains(m.Id) && m.UserId == userId)
            .ToListAsync();

        context.MaterialEntities.RemoveRange(materials);
        await context.SaveChangesAsync();

        return materials.Count;
    }

    public async Task<int> GetCountByCategoryAsync(Guid categoryId, Guid userId)
    {
        // Use factory to create independent DbContext for this operation
        using var context = await _dbContextFactory.CreateDbContextAsync();
        return await context.MaterialEntities
            .CountAsync(m => m.CategoryId == categoryId && m.UserId == userId);
    }
}
