using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.Services;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Wx;
using HappyWechat.Infrastructure.EYun;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Domain.Entities;
using HappyWechat.Application.DTOs.Wrappers.EYun.Requests.Groups;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 真正的群组同步服务实现
/// 负责从WxContactListEntities读取群聊数据，调用getChatRoomInfo和getChatRoomMember接口
/// </summary>
public class WxGroupSyncService : IGroupSyncService
{
    private readonly ILogger<WxGroupSyncService> _logger;
    private readonly ApplicationDbContext _dbContext;
    private readonly IEYunGroupWrapper _eYunGroupWrapper;
    private readonly ISimplifiedQueueService _queueService;
    private readonly IWxContactListRepository _contactListRepository;
    private readonly Random _random = new();

    public WxGroupSyncService(
        ILogger<WxGroupSyncService> logger,
        ApplicationDbContext dbContext,
        IEYunGroupWrapper eYunGroupWrapper,
        ISimplifiedQueueService queueService,
        IWxContactListRepository contactListRepository)
    {
        _logger = logger;
        _dbContext = dbContext;
        _eYunGroupWrapper = eYunGroupWrapper;
        _queueService = queueService;
        _contactListRepository = contactListRepository;
    }

    public async Task<SyncResult> PerformFullSyncAsync(Guid wxManagerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🔄 开始执行全量群组同步 - WxManagerId: {WxManagerId}", wxManagerId);

            // 1. 从WxContactListEntities表读取ListType=2的所有群聊ID
            var groupIds = await _contactListRepository.GetContactIdsAsync(wxManagerId, WxContactListType.Chatrooms);
            
            if (!groupIds.Any())
            {
                _logger.LogWarning("⚠️ 未找到需要同步的群聊 - WxManagerId: {WxManagerId}", wxManagerId);
                return new SyncResult { IsSuccess = true, SyncedCount = 0 };
            }

            _logger.LogInformation("📋 找到 {Count} 个群聊需要同步", groupIds.Count);

            // 2. 获取微信管理器信息
            var wxManager = await _dbContext.WxMangerEntities
                .FirstOrDefaultAsync(x => x.Id == wxManagerId, cancellationToken);
            
            if (wxManager == null)
            {
                throw new InvalidOperationException($"未找到微信管理器: {wxManagerId}");
            }

            var syncedCount = 0;
            var failedCount = 0;

            // 3. 逐个处理群聊信息
            foreach (var groupId in groupIds)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    // 随机间隔300ms-1500ms
                    await DelayWithRandomInterval(300, 1500);

                    // 获取群组基本信息
                    var groupInfoResult = await GetGroupInfoAsync(wxManager.WId, groupId);
                    if (groupInfoResult == null)
                    {
                        failedCount++;
                        continue;
                    }

                    // 保存群组信息
                    await SaveGroupInfoAsync(wxManagerId, groupInfoResult);

                    // 随机间隔后获取群成员信息
                    await DelayWithRandomInterval(300, 1500);
                    
                    var membersResult = await GetGroupMembersAsync(wxManager.WId, groupId);
                    if (membersResult != null && membersResult.Any())
                    {
                        await SaveGroupMembersAsync(wxManagerId, groupId, membersResult);
                    }

                    syncedCount++;
                    _logger.LogDebug("✅ 群组同步完成 - GroupId: {GroupId}", groupId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 群组同步失败 - GroupId: {GroupId}", groupId);
                    failedCount++;
                }
            }

            // 🔧 提升为Warning级别 - 重要的同步完成状态
            _logger.LogWarning("🎉 全量群组同步完成 - 成功: {Success}, 失败: {Failed}", syncedCount, failedCount);

            return new SyncResult
            {
                IsSuccess = true,
                SyncedCount = syncedCount,
                UpdatedCount = failedCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 全量群组同步异常 - WxManagerId: {WxManagerId}", wxManagerId);
            return new SyncResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<SyncResult> PerformIncrementalSyncAsync(Guid wxManagerId, DateTime? lastSyncTime, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("📈 执行增量群组同步 - WxManagerId: {WxManagerId}, LastSync: {LastSyncTime}", 
                wxManagerId, lastSyncTime);

            // 增量同步：只同步最近更新的群组
            var cutoffTime = lastSyncTime ?? DateTime.UtcNow.AddDays(-1);
            
            var groupsToUpdate = await _dbContext.WxGroupEntities
                .Where(x => x.WxManagerId == wxManagerId && x.UpdatedAt < cutoffTime)
                .Select(x => x.ChatRoomId)
                .ToListAsync(cancellationToken);

            if (!groupsToUpdate.Any())
            {
                return new SyncResult { IsSuccess = true, UpdatedCount = 0 };
            }

            // 复用全量同步的逻辑，但只处理指定的群组
            return await SyncSpecificGroupsAsync(wxManagerId, groupsToUpdate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 增量群组同步失败");
            return new SyncResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<SyncResult> SyncSingleGroupAsync(Guid wxManagerId, string groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("👥 同步单个群组 - GroupId: {GroupId}", groupId);
            
            return await SyncSpecificGroupsAsync(wxManagerId, new List<string> { groupId }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 单个群组同步失败");
            return new SyncResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<SyncResult> SyncGroupMembersAsync(Guid wxManagerId, string groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("👤 同步群组成员 - GroupId: {GroupId}", groupId);

            var wxManager = await _dbContext.WxMangerEntities
                .FirstOrDefaultAsync(x => x.Id == wxManagerId, cancellationToken);
            
            if (wxManager == null)
            {
                return new SyncResult { IsSuccess = false, ErrorMessage = "微信管理器不存在" };
            }

            var members = await GetGroupMembersAsync(wxManager.WId, groupId);
            if (members != null && members.Any())
            {
                await SaveGroupMembersAsync(wxManagerId, groupId, members);
                return new SyncResult { IsSuccess = true, MemberCount = members.Count };
            }

            return new SyncResult { IsSuccess = true, MemberCount = 0 };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组成员同步失败");
            return new SyncResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<SyncResult> UpdateGroupAsync(Guid wxManagerId, string groupId, object? groupData, CancellationToken cancellationToken = default)
    {
        try
        {
            return await SyncSingleGroupAsync(wxManagerId, groupId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组更新失败");
            return new SyncResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<SyncResult> DeleteGroupAsync(Guid wxManagerId, string groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 删除群组及其成员信息
            var group = await _dbContext.WxGroupEntities
                .FirstOrDefaultAsync(x => x.WxManagerId == wxManagerId && x.ChatRoomId == groupId, cancellationToken);
            
            if (group != null)
            {
                _dbContext.WxGroupEntities.Remove(group);
                
                // 删除群成员（如果有WxGroupMemberEntity表）
                var members = await _dbContext.Set<WxGroupMemberEntity>()
                    .Where(x => x.GroupId == group.Id)
                    .ToListAsync(cancellationToken);
                
                if (members.Any())
                {
                    _dbContext.Set<WxGroupMemberEntity>().RemoveRange(members);
                }
                
                await _dbContext.SaveChangesAsync(cancellationToken);
            }

            return new SyncResult { IsSuccess = true };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组删除失败");
            return new SyncResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    #region 私有辅助方法

    /// <summary>
    /// 随机间隔延时
    /// </summary>
    private async Task DelayWithRandomInterval(int minMs, int maxMs)
    {
        var delay = _random.Next(minMs, maxMs);
        _logger.LogDebug("⏱️ 随机延时 {Delay}ms", delay);
        await Task.Delay(delay);
    }

    /// <summary>
    /// 获取群组基本信息
    /// </summary>
    private async Task<List<HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Groups.EYunChatRoomInfoData>?> GetGroupInfoAsync(string wId, string groupId)
    {
        try
        {
            var request = new EYunGetChatRoomInfoRequest
            {
                WId = wId,
                ChatRoomId = groupId
            };

            _logger.LogDebug("📞 调用/getChatRoomInfo - GroupId: {GroupId}", groupId);
            return await _eYunGroupWrapper.GetChatRoomInfoAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取群组信息失败 - GroupId: {GroupId}", groupId);
            return null;
        }
    }

    /// <summary>
    /// 获取群组成员信息
    /// </summary>
    private async Task<List<HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Groups.EYunChatRoomMember>?> GetGroupMembersAsync(string wId, string groupId)
    {
        try
        {
            var request = new EYunGetChatRoomMembersRequest
            {
                WId = wId,
                ChatRoomId = groupId
            };

            _logger.LogDebug("📞 调用/getChatRoomMember - GroupId: {GroupId}", groupId);
            return await _eYunGroupWrapper.GetChatRoomMembersAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取群组成员失败 - GroupId: {GroupId}", groupId);
            return null;
        }
    }

    /// <summary>
    /// 保存群组基本信息
    /// </summary>
    private async Task SaveGroupInfoAsync(Guid wxManagerId, List<HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Groups.EYunChatRoomInfoData> groupInfoList)
    {
        foreach (var groupInfo in groupInfoList)
        {
            try
            {
                var existingGroup = await _dbContext.WxGroupEntities
                    .FirstOrDefaultAsync(x => x.WxManagerId == wxManagerId && x.ChatRoomId == groupInfo.ChatRoomId);

                if (existingGroup == null)
                {
                    // 创建新群组记录
                    var newGroup = new WxGroupEntity
                    {
                        Id = Guid.NewGuid(),
                        WxManagerId = wxManagerId,
                        ChatRoomId = groupInfo.ChatRoomId,
                        NickName = groupInfo.NickName ?? "",
                        ChatRoomOwner = groupInfo.ChatRoomOwner ?? "",
                        MemberCount = groupInfo.MemberCount,
                        BigHeadImgUrl = groupInfo.BigHeadImgUrl,
                        SmallHeadImgUrl = groupInfo.SmallHeadImgUrl,
                        V1 = groupInfo.V1,
                        IsOwner = false, // 需要根据ChatRoomOwner判断
                        IsAdmin = false,
                        IsAiEnabled = false,
                        OnlyReplyWhenMentioned = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _dbContext.WxGroupEntities.Add(newGroup);
                    _logger.LogDebug("➕ 新增群组 - {GroupName} ({GroupId})", groupInfo.NickName, groupInfo.ChatRoomId);
                }
                else
                {
                    // 更新现有群组
                    existingGroup.NickName = groupInfo.NickName ?? existingGroup.NickName;
                    existingGroup.ChatRoomOwner = groupInfo.ChatRoomOwner ?? existingGroup.ChatRoomOwner;
                    existingGroup.MemberCount = groupInfo.MemberCount;
                    existingGroup.BigHeadImgUrl = groupInfo.BigHeadImgUrl;
                    existingGroup.SmallHeadImgUrl = groupInfo.SmallHeadImgUrl;
                    existingGroup.V1 = groupInfo.V1;
                    existingGroup.UpdatedAt = DateTime.UtcNow;

                    _logger.LogDebug("🔄 更新群组 - {GroupName} ({GroupId})", groupInfo.NickName, groupInfo.ChatRoomId);
                }

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 保存群组信息失败 - GroupId: {GroupId}", groupInfo.ChatRoomId);
                throw;
            }
        }
    }

    /// <summary>
    /// 保存群组成员信息
    /// </summary>
    private async Task SaveGroupMembersAsync(Guid wxManagerId, string groupId, List<HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Groups.EYunChatRoomMember> members)
    {
        try
        {
            // 获取群组实体
            var group = await _dbContext.WxGroupEntities
                .FirstOrDefaultAsync(x => x.WxManagerId == wxManagerId && x.ChatRoomId == groupId);

            if (group == null)
            {
                _logger.LogWarning("⚠️ 未找到群组，无法保存成员信息 - GroupId: {GroupId}", groupId);
                return;
            }

            // 清除现有成员（全量更新）
            var existingMembers = await _dbContext.Set<WxGroupMemberEntity>()
                .Where(x => x.GroupId == group.Id)
                .ToListAsync();

            if (existingMembers.Any())
            {
                _dbContext.Set<WxGroupMemberEntity>().RemoveRange(existingMembers);
            }

            // 添加新成员
            var memberEntities = members.Select(member => new WxGroupMemberEntity
            {
                Id = Guid.NewGuid(),
                GroupId = group.Id,
                ChatRoomId = groupId,
                UserName = member.UserName,
                NickName = member.NickName ?? "",
                BigHeadImgUrl = member.BigHeadImgUrl,
                SmallHeadImgUrl = member.SmallHeadImgUrl,
                V1 = member.V1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }).ToList();

            await _dbContext.Set<WxGroupMemberEntity>().AddRangeAsync(memberEntities);
            await _dbContext.SaveChangesAsync();

            _logger.LogDebug("💾 保存群组成员完成 - GroupId: {GroupId}, 成员数: {Count}", groupId, members.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 保存群组成员失败 - GroupId: {GroupId}", groupId);
            throw;
        }
    }

    /// <summary>
    /// 同步指定的群组列表
    /// </summary>
    private async Task<SyncResult> SyncSpecificGroupsAsync(Guid wxManagerId, List<string> groupIds, CancellationToken cancellationToken = default)
    {
        var wxManager = await _dbContext.WxMangerEntities
            .FirstOrDefaultAsync(x => x.Id == wxManagerId, cancellationToken);

        if (wxManager == null)
        {
            return new SyncResult { IsSuccess = false, ErrorMessage = "微信管理器不存在" };
        }

        var syncedCount = 0;
        var failedCount = 0;

        foreach (var groupId in groupIds)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            try
            {
                await DelayWithRandomInterval(300, 1500);

                var groupInfoResult = await GetGroupInfoAsync(wxManager.WId, groupId);
                if (groupInfoResult != null)
                {
                    await SaveGroupInfoAsync(wxManagerId, groupInfoResult);

                    await DelayWithRandomInterval(300, 1500);

                    var membersResult = await GetGroupMembersAsync(wxManager.WId, groupId);
                    if (membersResult != null && membersResult.Any())
                    {
                        await SaveGroupMembersAsync(wxManagerId, groupId, membersResult);
                    }

                    syncedCount++;
                }
                else
                {
                    failedCount++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 群组同步失败 - GroupId: {GroupId}", groupId);
                failedCount++;
            }
        }

        return new SyncResult
        {
            IsSuccess = true,
            SyncedCount = syncedCount,
            UpdatedCount = failedCount
        };
    }

    #endregion

    /// <summary>
    /// 执行全量同步并跟踪进度
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="progressTracker">进度跟踪器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public async Task<SyncResult> PerformFullSyncWithProgressAsync(Guid wxManagerId, GroupSyncProgressTracker progressTracker, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🔄 开始执行全量群组同步（带进度跟踪） - WxManagerId: {WxManagerId}", wxManagerId);

            // 1. 从WxContactListEntities表读取ListType=2的所有群聊ID
            var groupIds = await _contactListRepository.GetContactIdsAsync(wxManagerId, WxContactListType.Chatrooms);

            if (!groupIds.Any())
            {
                _logger.LogWarning("⚠️ 未找到需要同步的群聊 - WxManagerId: {WxManagerId}", wxManagerId);
                return new SyncResult { IsSuccess = true, SyncedCount = 0 };
            }

            _logger.LogInformation("📋 找到 {Count} 个群聊需要同步", groupIds.Count);

            // 2. 获取微信管理器信息
            var wxManager = await _dbContext.WxMangerEntities
                .FirstOrDefaultAsync(x => x.Id == wxManagerId, cancellationToken);

            if (wxManager == null)
            {
                throw new InvalidOperationException($"未找到微信管理器: {wxManagerId}");
            }

            var syncedCount = 0;
            var failedCount = 0;

            // 3. 逐个处理群聊信息并跟踪进度
            foreach (var groupId in groupIds)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    // 随机间隔300ms-1500ms
                    await DelayWithRandomInterval(300, 1500);

                    // 获取群组基本信息
                    var groupInfoResult = await GetGroupInfoAsync(wxManager.WId, groupId);
                    if (groupInfoResult == null || !groupInfoResult.Any())
                    {
                        failedCount++;
                        await progressTracker.UpdateGroupProgressAsync(wxManagerId, groupId, null, false, false, "获取群组信息失败");
                        continue;
                    }

                    // 保存群组信息（取第一个结果）
                    var groupInfo = groupInfoResult.First();

                    // 🔧 确保NickName不为null或空
                    if (string.IsNullOrEmpty(groupInfo.NickName))
                    {
                        groupInfo.NickName = $"群组_{groupId}"; // 使用群组ID作为默认名称
                        _logger.LogWarning("⚠️ 群组NickName为空，使用默认名称 - GroupId: {GroupId}", groupId);
                    }

                    var isNewGroup = await SaveGroupInfoAsync(wxManagerId, groupInfo);

                    // 随机间隔后获取群成员信息
                    await DelayWithRandomInterval(300, 1500);

                    var membersResult = await GetGroupMembersAsync(wxManager.WId, groupId);
                    if (membersResult != null && membersResult.Any())
                    {
                        await SaveGroupMembersAsync(wxManagerId, groupId, membersResult);
                    }

                    syncedCount++;

                    // 更新进度
                    await progressTracker.UpdateGroupProgressAsync(wxManagerId, groupId, groupInfo.NickName, true, isNewGroup);

                    _logger.LogDebug("✅ 群组同步完成 - GroupId: {GroupId}", groupId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 群组同步失败 - GroupId: {GroupId}", groupId);
                    failedCount++;

                    // 更新进度（失败）
                    await progressTracker.UpdateGroupProgressAsync(wxManagerId, groupId, null, false, false, ex.Message);
                }
            }

            // 🔧 提升为Warning级别 - 重要的同步完成状态
            _logger.LogWarning("🎉 全量群组同步完成 - 成功: {Success}, 失败: {Failed}", syncedCount, failedCount);

            return new SyncResult
            {
                IsSuccess = true,
                SyncedCount = syncedCount,
                UpdatedCount = failedCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 全量群组同步异常 - WxManagerId: {WxManagerId}", wxManagerId);
            return new SyncResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    /// <summary>
    /// 保存群组信息并返回是否为新群组
    /// </summary>
    private async Task<bool> SaveGroupInfoAsync(Guid wxManagerId, HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Groups.EYunChatRoomInfoData groupInfo)
    {
        var existingGroup = await _dbContext.WxGroupEntities
            .FirstOrDefaultAsync(x => x.WxManagerId == wxManagerId && x.ChatRoomId == groupInfo.ChatRoomId);

        var isNewGroup = existingGroup == null;

        // 🔧 确保关键字段不为null
        var safeNickName = !string.IsNullOrWhiteSpace(groupInfo.NickName) ? groupInfo.NickName.Trim() : $"群组_{groupInfo.ChatRoomId}";
        var safeChatRoomOwner = !string.IsNullOrWhiteSpace(groupInfo.ChatRoomOwner) ? groupInfo.ChatRoomOwner.Trim() : string.Empty;
        var safeSmallHeadImgUrl = !string.IsNullOrWhiteSpace(groupInfo.SmallHeadImgUrl) ? groupInfo.SmallHeadImgUrl.Trim() : string.Empty;

        if (isNewGroup)
        {
            // 新群组
            var newGroup = new WxGroupEntity
            {
                Id = Guid.NewGuid(),
                WxManagerId = wxManagerId,
                ChatRoomId = groupInfo.ChatRoomId,
                NickName = safeNickName,
                SmallHeadImgUrl = safeSmallHeadImgUrl,
                BigHeadImgUrl = groupInfo.BigHeadImgUrl,
                MemberCount = groupInfo.MemberCount,
                IsOwner = groupInfo.IsManage ?? false,
                ChatRoomOwner = safeChatRoomOwner,
                V1 = groupInfo.V1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WxGroupEntities.Add(newGroup);
            _logger.LogDebug("➕ 新增群组 - {GroupName} ({GroupId})", safeNickName, groupInfo.ChatRoomId);
        }
        else
        {
            // 更新现有群组
            existingGroup.NickName = safeNickName;
            existingGroup.SmallHeadImgUrl = safeSmallHeadImgUrl;
            existingGroup.BigHeadImgUrl = groupInfo.BigHeadImgUrl;
            existingGroup.MemberCount = groupInfo.MemberCount;
            existingGroup.IsOwner = groupInfo.IsManage ?? false;
            existingGroup.ChatRoomOwner = safeChatRoomOwner;
            existingGroup.V1 = groupInfo.V1;
            existingGroup.UpdatedAt = DateTime.UtcNow;

            _logger.LogDebug("🔄 更新群组 - {GroupName} ({GroupId})", safeNickName, groupInfo.ChatRoomId);
        }

        await _dbContext.SaveChangesAsync();
        return isNewGroup;
    }
}
