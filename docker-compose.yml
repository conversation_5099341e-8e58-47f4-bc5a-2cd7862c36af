﻿services:
  happywechat:
    image: fusheng:v2.1
    restart: always
    ports:
      - "15125:5215"
    # 使用root用户启动，确保数据保护目录权限正确
    user: root
    environment:
      # ASP.NET Core 环境配置
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5215

      # 时区配置
      - TZ=Asia/Shanghai

      # MySQL 配置 (使用外部服务)
      - MYSQL_HOST=**********
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=huakai
      - MYSQL_USER=root
      - MYSQL_PASSWORD=wIhlhVv49EkYJ%HoVJyWIvUgqGTn5D

      # Redis 配置 (使用外部服务)
      - REDIS_HOST=**********
      - REDIS_PORT=6379
      - REDIS_PASSWORD=xUzvGVlMx@8zO!mASb6Wr3@PWq
      - REDIS_DATABASE_CACHE=2
      - REDIS_DATABASE_MESSAGEQUEUE=3
      - REDIS_DATABASE_SESSION=1
      - REDIS_DATABASE_LOCK=4

      # EYun 配置
      - EYUN_ACCOUNT=***********
      - EYUN_PASSWORD=X7sDKRaSDZkcvbM
      - EYUN_BASE_URL=http://*************:9899/
      - EYUN_CALLBACK_URL=http://**************:15125/api/wx/eYunWxMessageCallback
      - EYUN_TOKEN=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************.wYQ23_tHm6wDfN4YPqIYe5HvOYmMKcNWHoSjS1UAl8be3-LLxyTFdeWj3o-HZIqnSoGeCO6Bi8ACAzfcVCB_FA

      # 应用域名配置
      - APPLICATION_DOMAIN=http://**************:15125

      # JWT 配置增强 - 使用稳定域名配置
      - JWT_SECRET_KEY=8nP1dq7OfWtRuwELX2hJlvSiNVIYezQDyC9TkHUFBco5xajgK0r4Zbp6mMAsG3XS
      - JWT_ISSUER=HappyWechatApp
      - JWT_AUDIENCE=HappyWechatUsers

      # MinIO 配置 (通过系统配置界面管理，这里作为默认值)
      - MINIO_ENDPOINT=**********:9000
      - MINIO_ACCESSKEY=lRH2411C25mhOJ4X8Qy4
      - MINIO_SECRETKEY=40arCfeZgPJE9oZNXjTbbcYxFbiPJVznfeVx3EG5
      - MINIO_USESSL=false
      - MINIO_DEFAULTBUCKET=wechat
      - MINIO_REGION=cn-north-1

      # 数据保护配置增强 - 修复容器重启问题
      - ASPNETCORE_DATAPROTECTION__KEYPERSISTENCE__FILESYSTEM__DIRECTORY=/app/.aspnet/DataProtection-Keys
      - ASPNETCORE_DATAPROTECTION__APPLICATIONDISCRIMINATOR=HappyWechat-Production-v2.1
      - ASPNETCORE_DATAPROTECTION__KEYLIFETIME=90.00:00:00
      - ASPNETCORE_DATAPROTECTION__REVOKEDEFAULTKEYS=false
      - ASPNETCORE_DATAPROTECTION__AUTOMATICKEYGENERATIONENABLED=true
      - DataProtection__ApplicationName=HappyWechat-v2.1
      - DataProtection__KeyLifetime=90

      # 容器环境标识 - 用于环境感知配置
      - DOTNET_RUNNING_IN_CONTAINER=true

      # 数据保护密钥管理增强
      - ASPNETCORE_DATAPROTECTION__KEYESCROWSINKS__0__TYPE=File
      - ASPNETCORE_DATAPROTECTION__KEYESCROWSINKS__0__PATH=/app/.aspnet/DataProtection-Keys/escrow

      # 抗伪造配置增强
      - ASPNETCORE_ANTIFORGERY__SUPPRESSXFRAMEOPTIONSHEADER=false
      - ASPNETCORE_ANTIFORGERY__COOKIENAME=.AspNetCore.Antiforgery
      - ASPNETCORE_ANTIFORGERY__FORMFIELDNAME=__RequestVerificationToken
      
      # SignalR 配置增强
      - ASPNETCORE_SIGNALR__KEEPALIVEINTERVAL=30
      - ASPNETCORE_SIGNALR__CLIENTTIMEOUTINTERVAL=60

      # 禁用HTTPS重定向（容器内部使用HTTP）
      - ASPNETCORE_FORWARDEDHEADERS_ENABLED=true
      - ASPNETCORE_HTTPS_PORT=

      # Blazor配置
      - Blazor__DetailedErrors=true
      - Blazor__EnablePrerendering=false


    volumes:
      # 挂载时区文件确保时区设置生效
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro

      # 数据保护密钥持久化
      - happywechat_dataprotection:/app/.aspnet/DataProtection-Keys

      # 应用数据持久化
      - happywechat_data:/app/data

      # 新增：Silk解码器存储卷 - 解决编译和权限问题
      - happywechat_silk_v3_decoder:/opt/silk_v3_decoder:rw

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5215/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    networks:
      - 1panel-network

    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  happywechat_dataprotection:
    driver: local
  happywechat_data:
    driver: local
  # 🔧 新增：Silk解码器专用存储卷
  happywechat_silk_v3_decoder:
    driver: local

networks:
  1panel-network:
    external: true