using HappyWechat.Application.DTOs.Wrappers.EYun;

namespace HappyWechat.Application.DTOs.AiProvider;

/// <summary>
/// AI消息上下文 - 包含构建消息模板所需的所有信息
/// </summary>
public class AiMessageContext
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 微信ID (WId)
    /// </summary>
    public string WId { get; set; } = string.Empty;
    
    /// <summary>
    /// 微信号 (WcId)
    /// </summary>
    public string WcId { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送者微信ID
    /// </summary>
    public string FromUser { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送者昵称
    /// </summary>
    public string? FromUserNickName { get; set; }
    
    /// <summary>
    /// 群组ID (如果是群消息)
    /// </summary>
    public string? FromGroup { get; set; }
    
    /// <summary>
    /// 群组中的发送者微信ID (如果是群消息)
    /// </summary>
    public string? FromGroupUser { get; set; }
    
    /// <summary>
    /// 群组中的发送者昵称 (如果是群消息)
    /// </summary>
    public string? FromGroupUserNickName { get; set; }
    
    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = string.Empty;
    
    /// <summary>
    /// 原始消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否为群消息
    /// </summary>
    public bool IsGroupMessage { get; set; }
    
    /// <summary>
    /// 媒体文件描述列表 (用于媒体消息)
    /// </summary>
    public List<string> MediaDescriptions { get; set; } = new();
    
    /// <summary>
    /// 媒体文件URL列表 (用于媒体消息)
    /// </summary>
    public List<string> MediaUrls { get; set; } = new();
    
    /// <summary>
    /// 是否为媒体消息
    /// </summary>
    public bool IsMediaMessage { get; set; }
    
    /// <summary>
    /// 原始回调消息 (用于扩展信息)
    /// </summary>
    public WxCallbackMessageDto? OriginalCallbackMessage { get; set; }
    
    /// <summary>
    /// 消息时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}
