using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;
using HappyWechat.Infrastructure.Caching.Interfaces;
using InfrastructureCacheStatistics = HappyWechat.Infrastructure.Caching.Interfaces.CacheStatistics;

namespace HappyWechat.Infrastructure.Caching;

/// <summary>
/// 统一缓存管理器 - 多级缓存策略，缓存雪崩防护
/// 特性：L1(内存) + L2(Redis)缓存、缓存穿透防护、雪崩防护、热点数据识别
/// </summary>
public sealed class UnifiedCacheManager : IUnifiedCacheManager, IDisposable
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDatabase _redisDatabase;
    private readonly UnifiedCacheOptions _options;
    private readonly ILogger<UnifiedCacheManager> _logger;
    private readonly Timer _metricsTimer;
    private readonly Timer _cleanupTimer;
    
    // 缓存指标统计
    private readonly ConcurrentDictionary<string, CacheKeyMetrics> _keyMetrics = new();
    private readonly InfrastructureCacheStatistics _statistics = new();
    
    // 热点数据检测
    private readonly ConcurrentDictionary<string, HotKeyInfo> _hotKeys = new();
    
    // 缓存雪崩防护
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _cacheLocks = new();
    private readonly ConcurrentDictionary<string, DateTime> _expireSchedule = new();

    public UnifiedCacheManager(
        IMemoryCache memoryCache,
        IDatabase redisDatabase,
        IOptions<UnifiedCacheOptions> options,
        ILogger<UnifiedCacheManager> logger)
    {
        _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
        _redisDatabase = redisDatabase ?? throw new ArgumentNullException(nameof(redisDatabase));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 启动指标收集定时器
        _metricsTimer = new Timer(CollectMetrics, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        
        // 启动清理定时器
        _cleanupTimer = new Timer(PerformCleanup, null,
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

        // 🔧 注释冗余的缓存管理器启动日志 - 减少日志噪音，每次启动都会触发
        // _logger.LogInformation("统一缓存管理器已启动，L1缓存: 内存，L2缓存: Redis");
    }

    /// <summary>
    /// 获取缓存值（接口兼容版本）
    /// </summary>
    public async Task<T?> GetAsync<T>(string key)
    {
        return await GetAsync<T>(key, CancellationToken.None);
    }
    
    /// <summary>
    /// 获取缓存值
    /// </summary>
    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(key)) throw new ArgumentException("缓存键不能为空", nameof(key));

        var fullKey = BuildKey(key);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            // 记录热点访问
            RecordKeyAccess(fullKey);

            // L1缓存：内存缓存
            if (_memoryCache.TryGetValue(fullKey, out var memoryValue))
            {
                stopwatch.Stop();
                RecordCacheHit(CacheLevel.Memory, stopwatch.Elapsed);
                _logger.LogTrace("L1缓存命中: {Key}", key);
                return ConvertValue<T>(memoryValue);
            }

            // L2缓存：Redis缓存
            var redisValue = await _redisDatabase.StringGetAsync(fullKey);
            if (redisValue.HasValue)
            {
                var deserializedValue = JsonSerializer.Deserialize<T>(redisValue!);
                
                // 回写到L1缓存
                var memoryOptions = CreateMemoryCacheOptions(fullKey);
                _memoryCache.Set(fullKey, deserializedValue, memoryOptions);
                
                stopwatch.Stop();
                RecordCacheHit(CacheLevel.Redis, stopwatch.Elapsed);
                _logger.LogTrace("L2缓存命中，已回写L1: {Key}", key);
                return deserializedValue;
            }

            // 缓存未命中
            stopwatch.Stop();
            RecordCacheMiss(stopwatch.Elapsed);
            _logger.LogTrace("缓存未命中: {Key}", key);
            return default;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordCacheError(ex, stopwatch.Elapsed);
            _logger.LogError(ex, "获取缓存时发生错误: {Key}", key);
            
            // 降级到L1缓存
            if (_memoryCache.TryGetValue(fullKey, out var fallbackValue))
            {
                return ConvertValue<T>(fallbackValue);
            }
            
            return default;
        }
    }

    /// <summary>
    /// 设置缓存值
    /// </summary>
    public async Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null)
    {
        if (string.IsNullOrEmpty(key)) throw new ArgumentException("缓存键不能为空", nameof(key));

        var fullKey = BuildKey(key);
        var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_options.DefaultExpiryMinutes);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // 设置带随机偏移的过期时间，防止缓存雪崩
            var randomizedExpiry = AddRandomOffset(effectiveExpiry);
            
            // 并行设置L1和L2缓存
            var tasks = new List<Task>();
            
            // L1缓存：内存缓存
            var memoryOptions = CreateMemoryCacheOptions(fullKey, randomizedExpiry);
            _memoryCache.Set(fullKey, value, memoryOptions);
            
            // L2缓存：Redis缓存
            var serializedValue = JsonSerializer.Serialize(value);
            tasks.Add(_redisDatabase.StringSetAsync(fullKey, serializedValue, randomizedExpiry));
            
            await Task.WhenAll(tasks);
            
            // 记录过期时间调度
            _expireSchedule.TryAdd(fullKey, DateTime.UtcNow.Add(randomizedExpiry));
            
            stopwatch.Stop();
            RecordCacheSet(stopwatch.Elapsed);
            
            _logger.LogTrace("缓存已设置: {Key}, 过期时间: {Expiry}", key, randomizedExpiry);
            return true;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordCacheError(ex, stopwatch.Elapsed);
            _logger.LogError(ex, "设置缓存时发生错误: {Key}", key);
            
            // 降级：至少保证L1缓存设置成功
            try
            {
                var memoryOptions = CreateMemoryCacheOptions(fullKey, effectiveExpiry);
                _memoryCache.Set(fullKey, value, memoryOptions);
                return true;
            }
            catch (Exception memEx)
            {
                _logger.LogError(memEx, "L1缓存设置也失败: {Key}", key);
                return false;
            }
        }
    }

    /// <summary>
    /// 获取或设置缓存值
    /// </summary>
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);
        if (cachedValue != null)
        {
            return cachedValue;
        }

        // 使用分布式锁防止缓存击穿
        var lockKey = $"lock:{key}";
        var cacheLock = _cacheLocks.GetOrAdd(lockKey, _ => new SemaphoreSlim(1, 1));
        
        await cacheLock.WaitAsync(cancellationToken);
        try
        {
            // 双重检查
            cachedValue = await GetAsync<T>(key, cancellationToken);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            // 执行工厂方法
            var value = await factory();
            
            // 设置缓存
            await SetAsync(key, value, expiry);
            
            return value;
        }
        finally
        {
            cacheLock.Release();
        }
    }

    /// <summary>
    /// 删除缓存
    /// </summary>
    public async Task<bool> RemoveAsync(string key)
    {
        if (string.IsNullOrEmpty(key)) throw new ArgumentException("缓存键不能为空", nameof(key));

        var fullKey = BuildKey(key);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // 并行删除L1和L2缓存
            var tasks = new List<Task>
            {
                Task.Run(() => _memoryCache.Remove(fullKey)),
                _redisDatabase.KeyDeleteAsync(fullKey)
            };
            
            await Task.WhenAll(tasks);
            
            // 清理相关数据
            _keyMetrics.TryRemove(fullKey, out _);
            _hotKeys.TryRemove(fullKey, out _);
            _expireSchedule.TryRemove(fullKey, out _);
            
            stopwatch.Stop();
            RecordCacheRemove(stopwatch.Elapsed);
            
            _logger.LogTrace("缓存已删除: {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordCacheError(ex, stopwatch.Elapsed);
            _logger.LogError(ex, "删除缓存时发生错误: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 按模式删除缓存
    /// </summary>
    public async Task<bool> RemoveByPatternAsync(string pattern)
    {
        if (string.IsNullOrEmpty(pattern)) throw new ArgumentException("模式不能为空", nameof(pattern));

        try
        {
            var fullPattern = BuildKey(pattern);
            
            // Redis模式删除
            var server = _redisDatabase.Multiplexer.GetServer(_redisDatabase.Multiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: fullPattern).ToArray();
            
            if (keys.Length > 0)
            {
                await _redisDatabase.KeyDeleteAsync(keys);
                _logger.LogInformation("按模式删除Redis缓存: {Pattern}, 删除数量: {Count}", pattern, keys.Length);
            }

            // 内存缓存需要通过反射或自定义实现来模式删除
            // 这里简化处理，实际项目中可能需要更复杂的实现
            _logger.LogWarning("内存缓存不支持模式删除: {Pattern}", pattern);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按模式删除缓存时发生错误: {Pattern}", pattern);
            return false;
        }
    }

    /// <summary>
    /// 检查缓存是否存在
    /// </summary>
    public async Task<bool> ExistsAsync(string key)
    {
        if (string.IsNullOrEmpty(key)) throw new ArgumentException("缓存键不能为空", nameof(key));

        var fullKey = BuildKey(key);
        
        // 先检查L1缓存
        if (_memoryCache.TryGetValue(fullKey, out _))
        {
            return true;
        }
        
        // 再检查L2缓存
        return await _redisDatabase.KeyExistsAsync(fullKey);
    }

    /// <summary>
    /// 清理所有缓存
    /// </summary>
    public async Task<bool> ClearAllAsync()
    {
        try
        {
            // 清理内存缓存
            if (_memoryCache is MemoryCache mc)
            {
                mc.Clear();
            }

            // 清理Redis缓存（通过模式删除）
            await RemoveByPatternAsync("*");

            // 清理本地跟踪数据
            _keyMetrics.Clear();
            _hotKeys.Clear();
            _expireSchedule.Clear();
            _statistics.Reset();

            // 🔧 提升为Warning级别 - 重要的缓存清理操作
            _logger.LogWarning("🧹 所有缓存已清理");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理所有缓存时发生错误");
            return false;
        }
    }

    /// <summary>
    /// 预热缓存
    /// </summary>
    public async Task WarmUpAsync<T>(IDictionary<string, T> data, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        if (data == null || !data.Any()) return;

        // 🔧 注释冗余的缓存预热开始日志 - 减少日志噪音
        // _logger.LogInformation("开始缓存预热，数据量: {Count}", data.Count);

        var tasks = data.Select(async kvp =>
        {
            try
            {
                await SetAsync(kvp.Key, kvp.Value, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预热缓存项失败: {Key}", kvp.Key);
            }
        });

        await Task.WhenAll(tasks);
        
        // 🔧 提升为Warning级别 - 重要的缓存预热完成状态
        _logger.LogWarning("🔥 缓存预热完成 - 数据量: {Count}", data.Count);
    }


    /// <summary>
    /// 获取缓存统计信息（同步版本，兼容现有代码）
    /// </summary>
    public InfrastructureCacheStatistics GetStatistics()
    {
        return GetStatisticsAsync().GetAwaiter().GetResult();
    }
    
    /// <summary>
    /// 获取缓存统计信息（异步版本）
    /// </summary>
    public Task<InfrastructureCacheStatistics> GetStatisticsAsync()
    {
        var statistics = new InfrastructureCacheStatistics
        {
            TotalRequests = _statistics.TotalRequests,
            MemoryHits = _statistics.MemoryHits,
            RedisHits = _statistics.RedisHits,
            Misses = _statistics.Misses,
            Errors = _statistics.Errors,
            AverageResponseTime = _statistics.AverageResponseTime,
            HotKeysCount = _hotKeys.Count,
            TrackedKeysCount = _keyMetrics.Count
        };
        
        return Task.FromResult(statistics);
    }

    /// <summary>
    /// 获取热点键信息
    /// </summary>
    public IEnumerable<HotKeyInfo> GetHotKeys(int topCount = 20)
    {
        return _hotKeys.Values
            .OrderByDescending(h => h.AccessCount)
            .Take(topCount)
            .ToList();
    }

    #region 私有方法

    private string BuildKey(string key)
    {
        return $"{_options.KeyPrefix}:{key}";
    }

    private T? ConvertValue<T>(object? value)
    {
        if (value == null) return default;
        if (value is T directValue) return directValue;
        
        try
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return default;
        }
    }

    private MemoryCacheEntryOptions CreateMemoryCacheOptions(string key, TimeSpan? expiry = null)
    {
        var options = new MemoryCacheEntryOptions();
        
        if (expiry.HasValue)
        {
            options.AbsoluteExpirationRelativeToNow = expiry;
        }
        
        // 设置优先级
        if (_hotKeys.ContainsKey(key))
        {
            options.Priority = CacheItemPriority.High;
        }
        else
        {
            options.Priority = CacheItemPriority.Normal;
        }
        
        // 设置回调
        options.RegisterPostEvictionCallback((evictedKey, evictedValue, reason, state) =>
        {
            _logger.LogTrace("L1缓存项被驱逐: {Key}, 原因: {Reason}", evictedKey, reason);
        });
        
        return options;
    }

    private TimeSpan AddRandomOffset(TimeSpan baseExpiry)
    {
        if (!_options.EnableRandomOffset) return baseExpiry;
        
        var random = new Random();
        var offsetSeconds = random.Next(0, (int)(baseExpiry.TotalSeconds * _options.RandomOffsetPercentage));
        return baseExpiry.Add(TimeSpan.FromSeconds(offsetSeconds));
    }

    private void RecordKeyAccess(string key)
    {
        _keyMetrics.AddOrUpdate(key,
            new CacheKeyMetrics { Key = key, AccessCount = 1, LastAccessTime = DateTime.UtcNow },
            (k, existing) =>
            {
                existing.AccessCount++;
                existing.LastAccessTime = DateTime.UtcNow;
                return existing;
            });

        // 检测热点键
        if (_keyMetrics.TryGetValue(key, out var metrics))
        {
            var timeSinceFirstAccess = DateTime.UtcNow - metrics.FirstAccessTime;
            if (timeSinceFirstAccess.TotalMinutes >= 1 && metrics.AccessCount >= _options.HotKeyThreshold)
            {
                _hotKeys.TryAdd(key, new HotKeyInfo
                {
                    Key = key,
                    AccessCount = metrics.AccessCount,
                    AccessRate = metrics.AccessCount / timeSinceFirstAccess.TotalMinutes,
                    FirstDetectedAt = DateTime.UtcNow
                });
            }
        }
    }

    private void RecordCacheHit(CacheLevel level, TimeSpan responseTime)
    {
        Interlocked.Increment(ref _statistics.TotalRequests);
        
        switch (level)
        {
            case CacheLevel.Memory:
                Interlocked.Increment(ref _statistics.MemoryHits);
                break;
            case CacheLevel.Redis:
                Interlocked.Increment(ref _statistics.RedisHits);
                break;
        }
        
        UpdateAverageResponseTime(responseTime);
    }

    private void RecordCacheMiss(TimeSpan responseTime)
    {
        Interlocked.Increment(ref _statistics.TotalRequests);
        Interlocked.Increment(ref _statistics.Misses);
        UpdateAverageResponseTime(responseTime);
    }

    private void RecordCacheSet(TimeSpan responseTime)
    {
        UpdateAverageResponseTime(responseTime);
    }

    private void RecordCacheRemove(TimeSpan responseTime)
    {
        UpdateAverageResponseTime(responseTime);
    }

    private void RecordCacheError(Exception exception, TimeSpan responseTime)
    {
        Interlocked.Increment(ref _statistics.Errors);
        UpdateAverageResponseTime(responseTime);
    }

    private void UpdateAverageResponseTime(TimeSpan responseTime)
    {
        // 简化的移动平均计算
        var currentAvg = _statistics.AverageResponseTime;
        var newAvg = TimeSpan.FromMilliseconds(
            (currentAvg.TotalMilliseconds * 0.9) + (responseTime.TotalMilliseconds * 0.1));
        _statistics.AverageResponseTime = newAvg;
    }

    private void CollectMetrics(object? state)
    {
        try
        {
            var stats = GetStatistics();
            // 🔧 注释冗余的缓存指标日志 - 减少日志噪音，避免频繁输出
            // _logger.LogInformation("缓存指标 - 总请求: {Total}, 命中率: {HitRate:P2}, 热点键: {HotKeys}",
            //     stats.TotalRequests, stats.HitRate, stats.HotKeysCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收集缓存指标时发生错误");
        }
    }

    private void PerformCleanup(object? state)
    {
        try
        {
            // 清理过期的指标数据
            var cutoffTime = DateTime.UtcNow.AddHours(-1);
            var expiredKeys = _keyMetrics.Where(kvp => kvp.Value.LastAccessTime < cutoffTime)
                .Select(kvp => kvp.Key).ToList();

            foreach (var key in expiredKeys)
            {
                _keyMetrics.TryRemove(key, out _);
                _hotKeys.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogDebug("清理过期指标数据: {Count} 项", expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理缓存指标时发生错误");
        }
    }

    #endregion

    /// <summary>
    /// 获取所有缓存键
    /// </summary>
    public async Task<List<string>> GetKeysAsync(string pattern = "*")
    {
        var keys = new List<string>();
        try
        {
            var server = _redisDatabase.Multiplexer.GetServer(_redisDatabase.Multiplexer.GetEndPoints().First());
            await foreach (var key in server.KeysAsync(pattern: pattern))
            {
                keys.Add(key.ToString());
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取缓存键失败 - Pattern: {Pattern}", pattern);
        }
        return keys;
    }

    /// <summary>
    /// 从L1缓存移除
    /// </summary>
    public async Task<bool> RemoveFromL1Async(string key)
    {
        await Task.CompletedTask;
        _memoryCache.Remove(key);
        return true;
    }

    /// <summary>
    /// 从L2缓存移除
    /// </summary>
    public async Task<bool> RemoveFromL2Async(string key)
    {
        try
        {
            return await _redisDatabase.KeyDeleteAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "从L2缓存移除失败 - Key: {Key}", key);
            return false;
        }
    }


    /// <summary>
    /// 预热缓存
    /// </summary>
    public async Task<bool> WarmupCacheAsync(string[] keys)
    {
        try
        {
            foreach (var key in keys)
            {
                // 预热操作可以根据需要实现
                await ExistsAsync(key);
            }
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "缓存预热失败");
            return false;
        }
    }

    /// <summary>
    /// 批量设置缓存
    /// </summary>
    public async Task<bool> SetBatchAsync<T>(Dictionary<string, T> items, TimeSpan? expiry = null)
    {
        try
        {
            var tasks = items.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiry));
            var results = await Task.WhenAll(tasks);
            return results.All(r => r);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "批量设置缓存失败");
            return false;
        }
    }

    /// <summary>
    /// 批量获取缓存
    /// </summary>
    public async Task<Dictionary<string, T?>> GetBatchAsync<T>(string[] keys)
    {
        var result = new Dictionary<string, T?>();
        try
        {
            var tasks = keys.Select(async key => new { Key = key, Value = await GetAsync<T>(key) });
            var results = await Task.WhenAll(tasks);
            
            foreach (var item in results)
            {
                result[item.Key] = item.Value;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "批量获取缓存失败");
        }
        return result;
    }

    public void Dispose()
    {
        _metricsTimer?.Dispose();
        _cleanupTimer?.Dispose();
        
        foreach (var semaphore in _cacheLocks.Values)
        {
            semaphore?.Dispose();
        }
        _cacheLocks.Clear();
    }
}


/// <summary>
/// 统一缓存配置选项
/// </summary>
public class UnifiedCacheOptions
{
    public const string SectionName = "UnifiedCache";

    /// <summary>
    /// 缓存键前缀
    /// </summary>
    public string KeyPrefix { get; set; } = "HappyWechat";

    /// <summary>
    /// 默认过期时间（分钟）
    /// </summary>
    public int DefaultExpiryMinutes { get; set; } = 30;

    /// <summary>
    /// 是否启用随机偏移
    /// </summary>
    public bool EnableRandomOffset { get; set; } = true;

    /// <summary>
    /// 随机偏移百分比
    /// </summary>
    public double RandomOffsetPercentage { get; set; } = 0.1; // 10%

    /// <summary>
    /// 热点键阈值（每分钟访问次数）
    /// </summary>
    public int HotKeyThreshold { get; set; } = 100;
}

/// <summary>
/// 缓存级别枚举
/// </summary>
public enum CacheLevel
{
    Memory,
    Redis
}


/// <summary>
/// 缓存键指标
/// </summary>
public class CacheKeyMetrics
{
    public string Key { get; set; } = string.Empty;
    public long AccessCount { get; set; }
    public DateTime FirstAccessTime { get; set; } = DateTime.UtcNow;
    public DateTime LastAccessTime { get; set; }
}

/// <summary>
/// 热点键信息
/// </summary>
public class HotKeyInfo
{
    public string Key { get; set; } = string.Empty;
    public long AccessCount { get; set; }
    public double AccessRate { get; set; } // 每分钟访问次数
    public DateTime FirstDetectedAt { get; set; }
}