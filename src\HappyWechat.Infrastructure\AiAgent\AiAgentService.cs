using AutoMapper;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.AiAgent;
using HappyWechat.Application.DTOs.AiProvider;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.Entities;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.ServiceRegistration;
using HappyWechat.Infrastructure.Resilience;
using System.Reflection;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.AiAgent;

/// <summary>
/// AI智能体服务实现 - 现代化重构版本
/// 特性：并发安全、资源优化、错误恢复、性能监控
/// </summary>
public class AiAgentService : IAiAgentService, IDisposable
{
    private readonly IAiAgentRepository _repository;
    private readonly IMapper _mapper;
    private readonly AiTestHttpClient _httpClient;
    private readonly ILogger<AiAgentService> _logger;
    private readonly IAiProviderFactory _aiProviderFactory;
    private readonly AiRequestLogger _requestLogger;
    private readonly IAiConfigHotReloadService _hotReloadService;
    private readonly IServiceProvider _serviceProvider;
    private readonly SmartCircuitBreaker _circuitBreaker;
    private readonly ServiceLifecycleManager _lifecycleManager;
    
    // 并发安全的缓存
    private readonly ConcurrentDictionary<Guid, (AiAgentEntity Entity, DateTime CacheTime)> _entityCache;
    private readonly ConcurrentDictionary<string, (bool Exists, DateTime CacheTime)> _nameExistsCache;
    private readonly ReaderWriterLockSlim _cacheLock;
    
    // 性能监控
    private readonly ConcurrentDictionary<string, (int Count, TimeSpan TotalTime)> _operationMetrics;
    private readonly Timer _metricsReportTimer;
    
    private bool _disposed;

    public AiAgentService(
        IAiAgentRepository repository,
        IMapper mapper,
        AiTestHttpClient httpClient,
        ILogger<AiAgentService> logger,
        IAiProviderFactory aiProviderFactory,
        AiRequestLogger requestLogger,
        IAiConfigHotReloadService hotReloadService,
        IServiceProvider serviceProvider,
        SmartCircuitBreaker circuitBreaker,
        ServiceLifecycleManager lifecycleManager)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _aiProviderFactory = aiProviderFactory ?? throw new ArgumentNullException(nameof(aiProviderFactory));
        _requestLogger = requestLogger ?? throw new ArgumentNullException(nameof(requestLogger));
        _hotReloadService = hotReloadService ?? throw new ArgumentNullException(nameof(hotReloadService));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _circuitBreaker = circuitBreaker ?? throw new ArgumentNullException(nameof(circuitBreaker));
        _lifecycleManager = lifecycleManager ?? throw new ArgumentNullException(nameof(lifecycleManager));
        
        // 初始化并发安全组件
        _entityCache = new ConcurrentDictionary<Guid, (AiAgentEntity, DateTime)>();
        _nameExistsCache = new ConcurrentDictionary<string, (bool, DateTime)>();
        _cacheLock = new ReaderWriterLockSlim();
        _operationMetrics = new ConcurrentDictionary<string, (int, TimeSpan)>();
        
        // 启动性能监控定时器
        _metricsReportTimer = new Timer(ReportMetrics, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        // 注册到生命周期管理器
        _lifecycleManager.RegisterDisposable(this);
        
        // 🔧 注释冗余的AI服务初始化日志 - 减少日志噪音，每次操作都会重复出现
        // _logger.LogInformation("AI智能体服务已初始化，启用并发安全和性能监控");
    }

    public async Task<AiAgentDto> CreateAsync(Guid userId, CreateAiAgentCommand command)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            return await _circuitBreaker.ExecuteAsync(async () =>
            {
                // 参数验证
                if (command == null) throw new ArgumentNullException(nameof(command));
                if (string.IsNullOrWhiteSpace(command.Name)) throw new ArgumentException("智能体名称不能为空");
                
                // 并发安全的名称检查
                if (await IsNameExistsWithCacheAsync(command.Name, userId))
                {
                    throw new InvalidOperationException($"智能体名称 '{command.Name}' 已存在");
                }

                // 验证配置
                ValidateConfig(command.Config, command.ProviderType);

                var entity = new AiAgentEntity
                {
                    UserId = userId,
                    ProviderType = command.ProviderType,
                    Name = command.Name,
                    Description = command.Description,
                    IsEnabled = command.IsEnabled,
                    ConfigJson = JsonConvert.SerializeObject(command.Config)
                };

                var createdEntity = await _repository.CreateAsync(entity);
                
                // 更新缓存
                UpdateEntityCache(createdEntity);
                UpdateNameExistsCache($"{command.Name}_{userId}", true);
                
                _logger.LogInformation("创建AI智能体成功: {AgentId}, 名称: {Name}, 用户: {UserId}", 
                    createdEntity.Id, command.Name, userId);
                    
                return MapToDto(createdEntity);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建AI智能体失败: 名称={Name}, 用户={UserId}", command?.Name, userId);
            throw;
        }
        finally
        {
            RecordOperationMetrics("CreateAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task<AiAgentDto> UpdateAsync(Guid userId, UpdateAiAgentCommand command)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            return await _circuitBreaker.ExecuteAsync(async () =>
            {
                // 参数验证
                if (command == null) throw new ArgumentNullException(nameof(command));
                if (command.Id == Guid.Empty) throw new ArgumentException("智能体ID不能为空");
                if (string.IsNullOrWhiteSpace(command.Name)) throw new ArgumentException("智能体名称不能为空");
                
                var entity = await _repository.GetByIdAsync(command.Id, userId);
                if (entity == null)
                {
                    throw new InvalidOperationException("智能体不存在");
                }

                // 检查名称是否已存在（排除当前记录）
                if (await _repository.IsNameExistsAsync(command.Name, userId, command.Id))
                {
                    throw new InvalidOperationException($"智能体名称 '{command.Name}' 已存在");
                }

                // 验证配置
                ValidateConfig(command.Config, command.ProviderType);

                entity.ProviderType = command.ProviderType;
                entity.Name = command.Name;
                entity.Description = command.Description;
                entity.IsEnabled = command.IsEnabled;
                entity.ConfigJson = JsonConvert.SerializeObject(command.Config);

                var updatedEntity = await _repository.UpdateAsync(entity);
                
                // 更新缓存
                UpdateEntityCache(updatedEntity);
                
                // 清理相关的名称缓存
                var oldNameCacheKey = $"{entity.Name}_{userId}";
                var newNameCacheKey = $"{command.Name}_{userId}";
                _nameExistsCache.TryRemove(oldNameCacheKey, out _);
                UpdateNameExistsCache(newNameCacheKey, true);
                
                _logger.LogInformation("更新AI智能体成功: {AgentId}, 新名称: {Name}, 用户: {UserId}", 
                    command.Id, command.Name, userId);
                    
                return MapToDto(updatedEntity);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新AI智能体失败: ID={Id}, 名称={Name}, 用户={UserId}", 
                command?.Id, command?.Name, userId);
            throw;
        }
        finally
        {
            RecordOperationMetrics("UpdateAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task DeleteAsync(Guid userId, Guid id)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            await _circuitBreaker.ExecuteAsync(async () =>
            {
                if (id == Guid.Empty) throw new ArgumentException("智能体ID不能为空");
                
                // 从缓存中移除
                _entityCache.TryRemove(id, out _);
                
                await _repository.DeleteAsync(id, userId);
                
                _logger.LogInformation("删除AI智能体成功: {AgentId}, 用户: {UserId}", id, userId);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除AI智能体失败: ID={Id}, 用户={UserId}", id, userId);
            throw;
        }
        finally
        {
            RecordOperationMetrics("DeleteAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task<AiAgentDto?> GetByIdAsync(Guid userId, Guid id)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            var entity = await _repository.GetByIdAsync(id, userId);
            if (entity != null)
            {
                UpdateEntityCache(entity);
            }
            return entity == null ? null : MapToDto(entity);
        }
        finally
        {
            RecordOperationMetrics("GetByIdAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task<PageResponse<AiAgentDto>> GetPagedListAsync(Guid userId, AiAgentQuery query)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            var pageResponse = await _repository.GetPagedListAsync(userId, query);
            var dtos = pageResponse.Items.Select(MapToDto).ToList();
            
            // 批量更新缓存
            foreach (var entity in pageResponse.Items)
            {
                UpdateEntityCache(entity);
            }
            
            return PageResponse<AiAgentDto>.ReplaceItems(pageResponse, dtos);
        }
        finally
        {
            RecordOperationMetrics("GetPagedListAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task ToggleEnabledAsync(Guid userId, Guid id, bool isEnabled)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            await _circuitBreaker.ExecuteAsync(async () =>
            {
                if (id == Guid.Empty) throw new ArgumentException("智能体ID不能为空");
                
                var entity = await _repository.GetByIdAsync(id, userId);
                if (entity == null)
                {
                    throw new InvalidOperationException("智能体不存在");
                }

                entity.IsEnabled = isEnabled;
                await _repository.UpdateAsync(entity);
                
                // 更新缓存
                UpdateEntityCache(entity);
                
                _logger.LogInformation("切换AI智能体状态成功: {AgentId}, 启用: {IsEnabled}, 用户: {UserId}", 
                    id, isEnabled, userId);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换AI智能体状态失败: ID={Id}, 用户={UserId}", id, userId);
            throw;
        }
        finally
        {
            RecordOperationMetrics("ToggleEnabledAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task<string> TestConnectionAsync(Guid userId, Guid id, string testMessage = "Hello")
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            return await _circuitBreaker.ExecuteAsync(async () =>
            {
                if (id == Guid.Empty) throw new ArgumentException("智能体ID不能为空");
                if (string.IsNullOrWhiteSpace(testMessage)) testMessage = "Hello";
                
                var entity = await _repository.GetByIdAsync(id, userId);
                if (entity == null)
                {
                    throw new InvalidOperationException("智能体不存在");
                }

                // 使用增强的AI测试服务进行统一测试
                var enhancedTestService = new EnhancedAiTestService(_aiProviderFactory,
                    _serviceProvider.GetRequiredService<ILogger<EnhancedAiTestService>>(),
                    _requestLogger, _hotReloadService);
                    
                var result = await enhancedTestService.TestConnectionAsync(entity, testMessage);
                
                _logger.LogInformation("AI智能体连接测试成功: {AgentId}, 用户: {UserId}", id, userId);
                return result;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI智能体连接测试失败: ID={Id}, 用户={UserId}", id, userId);
            throw;
        }
        finally
        {
            RecordOperationMetrics("TestConnectionAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task<string> ProcessMessageAsync(Guid agentId, string message, object? context = null)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            return await _circuitBreaker.ExecuteAsync(async () =>
            {
                // 参数验证
                if (agentId == Guid.Empty) throw new ArgumentException("智能体ID不能为空");
                if (string.IsNullOrWhiteSpace(message)) throw new ArgumentException("消息内容不能为空");
                
                // 使用并发安全的缓存查询
                var entity = await GetEntityWithCacheAsync(agentId);
                if (entity == null)
                {
                    throw new InvalidOperationException("智能体不存在");
                }
                
                if (!entity.IsEnabled)
                {
                    throw new InvalidOperationException("智能体已禁用");
                }

                // 解析配置
                var config = ParseEntityConfig(entity);
                if (config == null)
                {
                    throw new InvalidOperationException("AI配置解析失败");
                }

                // 创建AI提供商实例
                var provider = _aiProviderFactory.CreateProvider(entity.ProviderType, config);

                // 构建AI请求
                var request = new Application.DTOs.AiProvider.AiRequest
                {
                    Message = message,
                    UserId = context?.ToString() ?? "system",
                    ConversationId = Guid.NewGuid().ToString(),
                    AdditionalParameters = new Dictionary<string, object>
                    {
                        ["agent_id"] = agentId,
                        ["timestamp"] = DateTime.UtcNow,
                        ["provider_type"] = entity.ProviderType.ToString()
                    }
                };

                // 检查是否有消息上下文
                AiMessageContext? messageContext = null;
                if (context != null)
                {
                    var contextType = context.GetType();
                    var messageContextProperty = contextType.GetProperty("MessageContext");
                    if (messageContextProperty != null)
                    {
                        messageContext = messageContextProperty.GetValue(context) as AiMessageContext;
                    }
                }

                // 发送AI请求并记录日志
                _logger.LogDebug("开始处理AI消息 - AgentId: {AgentId}, Provider: {Provider}, MessageLength: {Length}", 
                    agentId, entity.ProviderType, message.Length);
                    
                AiResponse response;
                if (messageContext != null && provider is Infrastructure.AiProvider.BaseAiProvider baseProvider)
                {
                    response = await baseProvider.SendMessageAsync(request, messageContext);
                }
                else
                {
                    response = await provider.SendMessageAsync(request);
                }

                if (response.IsSuccess)
                {
                    var result = response.Content ?? "AI响应为空";
                    _logger.LogDebug("AI消息处理成功 - AgentId: {AgentId}, ResponseLength: {Length}", 
                        agentId, result.Length);
                    return result;
                }
                else
                {
                    throw new InvalidOperationException($"AI处理失败: {response.ErrorMessage}");
                }
            });
        }
        catch (Exception ex) when (ex is TimeoutException || ex is TaskCanceledException || ex is OperationCanceledException)
        {
            _logger.LogWarning(ex, "AI消息处理超时 - AgentId: {AgentId}", agentId);
            throw new InvalidOperationException($"AI处理超时: {ex.Message}", ex);
        }
        catch (Exception ex) when (ex is HttpRequestException)
        {
            _logger.LogWarning(ex, "AI网络异常 - AgentId: {AgentId}", agentId);
            throw new InvalidOperationException($"AI网络异常: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理AI消息失败 - AgentId: {AgentId}, Message: {MessagePreview}", 
                agentId, message.Length > 100 ? message[..100] + "..." : message);
            throw;
        }
        finally
        {
            RecordOperationMetrics("ProcessMessageAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task<int> GetCountAsync(Guid userId)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            var count = await _repository.GetCountAsync(userId);
            _logger.LogDebug("获取用户智能体总数成功 - UserId: {UserId}, Count: {Count}", userId, count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户智能体总数失败 - UserId: {UserId}", userId);
            throw;
        }
        finally
        {
            RecordOperationMetrics("GetCountAsync", DateTime.UtcNow - operationStart);
        }
    }

    public async Task<int> GetEnabledCountAsync(Guid userId)
    {
        var operationStart = DateTime.UtcNow;
        try
        {
            var count = await _repository.GetEnabledCountAsync(userId);
            _logger.LogDebug("获取用户启用智能体数量成功 - UserId: {UserId}, EnabledCount: {Count}", userId, count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户启用智能体数量失败 - UserId: {UserId}", userId);
            throw;
        }
        finally
        {
            RecordOperationMetrics("GetEnabledCountAsync", DateTime.UtcNow - operationStart);
        }
    }

    private AiAgentDto MapToDto(AiAgentEntity entity)
    {
        var dto = _mapper.Map<AiAgentDto>(entity);

        // 根据提供商类型解析配置
        dto.Config = ParseEntityConfig(entity);

        return dto;
    }

    /// <summary>
    /// 解析实体配置
    /// </summary>
    private AiAgentConfigDto? ParseEntityConfig(AiAgentEntity entity)
    {
        try
        {
            return entity.ProviderType switch
            {
                AiProviderType.CoZe => JsonConvert.DeserializeObject<CoZeConfigDto>(entity.ConfigJson),
                AiProviderType.MaxKB => JsonConvert.DeserializeObject<MaxKBConfigDto>(entity.ConfigJson),
                AiProviderType.Dify => JsonConvert.DeserializeObject<DifyConfigDto>(entity.ConfigJson),
                AiProviderType.ChatGPT => JsonConvert.DeserializeObject<ChatGPTConfigDto>(entity.ConfigJson),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析AI配置失败 - 提供商: {ProviderType}", entity.ProviderType);
            return null;
        }
    }

    private static void ValidateConfig(AiAgentConfigDto config, AiProviderType providerType)
    {
        if (config.ProviderType != providerType)
        {
            throw new ArgumentException("配置类型与提供商类型不匹配");
        }

        switch (config)
        {
            case CoZeConfigDto cozeConfig:
                if (string.IsNullOrWhiteSpace(cozeConfig.BaseUrl) || string.IsNullOrWhiteSpace(cozeConfig.Token))
                    throw new ArgumentException("扣子配置的BaseUrl和Token不能为空");
                break;
            case MaxKBConfigDto maxkbConfig:
                if (string.IsNullOrWhiteSpace(maxkbConfig.BaseUrl) || string.IsNullOrWhiteSpace(maxkbConfig.Token))
                    throw new ArgumentException("MaxKB配置的BaseUrl和Token不能为空");
                break;
            case DifyConfigDto difyConfig:
                if (string.IsNullOrWhiteSpace(difyConfig.BaseUrl) || string.IsNullOrWhiteSpace(difyConfig.Token))
                    throw new ArgumentException("Dify配置的BaseUrl和Token不能为空");
                break;
            case ChatGPTConfigDto chatgptConfig:
                if (string.IsNullOrWhiteSpace(chatgptConfig.BaseUrl) || string.IsNullOrWhiteSpace(chatgptConfig.Token) || string.IsNullOrWhiteSpace(chatgptConfig.Model))
                    throw new ArgumentException("ChatGPT配置的BaseUrl、Token和Model不能为空");
                if (chatgptConfig.Temperature < 0 || chatgptConfig.Temperature > 2)
                    throw new ArgumentException("ChatGPT配置的Temperature必须在0-2之间");
                break;
        }
    }
    
    #region 并发安全缓存方法
    
    /// <summary>
    /// 并发安全的实体缓存查询
    /// </summary>
    private async Task<AiAgentEntity?> GetEntityWithCacheAsync(Guid agentId)
    {
        const int cacheExpiryMinutes = 30;
        
        _cacheLock.EnterReadLock();
        try
        {
            if (_entityCache.TryGetValue(agentId, out var cached) && 
                DateTime.UtcNow - cached.CacheTime < TimeSpan.FromMinutes(cacheExpiryMinutes))
            {
                return cached.Entity;
            }
        }
        finally
        {
            _cacheLock.ExitReadLock();
        }
        
        // 缓存未命中或已过期，从数据库查询
        var entity = await _repository.GetByIdSystemAsync(agentId);
        if (entity != null)
        {
            UpdateEntityCache(entity);
        }
        
        return entity;
    }
    
    /// <summary>
    /// 并发安全的名称存在检查
    /// </summary>
    private async Task<bool> IsNameExistsWithCacheAsync(string name, Guid userId)
    {
        const int cacheExpiryMinutes = 10;
        var cacheKey = $"{name}_{userId}";
        
        _cacheLock.EnterReadLock();
        try
        {
            if (_nameExistsCache.TryGetValue(cacheKey, out var cached) && 
                DateTime.UtcNow - cached.CacheTime < TimeSpan.FromMinutes(cacheExpiryMinutes))
            {
                return cached.Exists;
            }
        }
        finally
        {
            _cacheLock.ExitReadLock();
        }
        
        // 缓存未命中或已过期，从数据库查询
        var exists = await _repository.IsNameExistsAsync(name, userId);
        UpdateNameExistsCache(cacheKey, exists);
        
        return exists;
    }
    
    /// <summary>
    /// 更新实体缓存
    /// </summary>
    private void UpdateEntityCache(AiAgentEntity entity)
    {
        _cacheLock.EnterWriteLock();
        try
        {
            _entityCache.AddOrUpdate(entity.Id, 
                (entity, DateTime.UtcNow), 
                (key, old) => (entity, DateTime.UtcNow));
                
            // 清理过期缓存（最多保留100个）
            if (_entityCache.Count > 100)
            {
                var expiredKeys = _entityCache
                    .Where(kvp => DateTime.UtcNow - kvp.Value.CacheTime > TimeSpan.FromHours(1))
                    .Take(20)
                    .Select(kvp => kvp.Key)
                    .ToList();
                    
                foreach (var key in expiredKeys)
                {
                    _entityCache.TryRemove(key, out _);
                }
            }
        }
        finally
        {
            _cacheLock.ExitWriteLock();
        }
    }
    
    /// <summary>
    /// 更新名称存在缓存
    /// </summary>
    private void UpdateNameExistsCache(string cacheKey, bool exists)
    {
        _cacheLock.EnterWriteLock();
        try
        {
            _nameExistsCache.AddOrUpdate(cacheKey, 
                (exists, DateTime.UtcNow), 
                (key, old) => (exists, DateTime.UtcNow));
                
            // 清理过期缓存
            if (_nameExistsCache.Count > 200)
            {
                var expiredKeys = _nameExistsCache
                    .Where(kvp => DateTime.UtcNow - kvp.Value.CacheTime > TimeSpan.FromMinutes(30))
                    .Take(50)
                    .Select(kvp => kvp.Key)
                    .ToList();
                    
                foreach (var key in expiredKeys)
                {
                    _nameExistsCache.TryRemove(key, out _);
                }
            }
        }
        finally
        {
            _cacheLock.ExitWriteLock();
        }
    }
    
    #endregion
    
    #region 性能监控
    
    /// <summary>
    /// 记录操作指标
    /// </summary>
    private void RecordOperationMetrics(string operationName, TimeSpan duration)
    {
        _operationMetrics.AddOrUpdate(operationName,
            (1, duration),
            (key, old) => (old.Count + 1, old.TotalTime + duration));
    }
    
    /// <summary>
    /// 报告性能指标
    /// </summary>
    private void ReportMetrics(object? state)
    {
        try
        {
            if (_operationMetrics.IsEmpty) return;
            
            var metrics = _operationMetrics.ToList();
            foreach (var metric in metrics)
            {
                var avgDuration = metric.Value.TotalTime.TotalMilliseconds / metric.Value.Count;
                _logger.LogInformation("AI智能体服务指标 - 操作: {Operation}, 次数: {Count}, 平均耗时: {AvgMs}ms",
                    metric.Key, metric.Value.Count, avgDuration.ToString("F2"));
            }
            
            // 清理旧数据
            _operationMetrics.Clear();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "报告性能指标失败");
        }
    }
    
    #endregion
    
    #region 资源释放
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;
        
        try
        {
            _metricsReportTimer?.Dispose();
            _cacheLock?.Dispose();
            
            // 清理缓存
            _entityCache?.Clear();
            _nameExistsCache?.Clear();
            _operationMetrics?.Clear();
            
            // 🔧 注释冗余的AI服务资源释放日志 - 减少日志噪音，每次操作都会重复出现
            // _logger.LogInformation("AI智能体服务资源已释放");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "AI智能体服务资源释放失败");
        }
        finally
        {
            _disposed = true;
        }
    }
    
    #endregion
}