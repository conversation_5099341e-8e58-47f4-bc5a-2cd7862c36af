using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace HappyWechat.Infrastructure.Configuration;

/// <summary>
/// 配置生效状态
/// </summary>
public enum ConfigurationEffectivenessStatus
{
    /// <summary>
    /// 配置变更开始
    /// </summary>
    Started,
    
    /// <summary>
    /// 数据库已更新
    /// </summary>
    DatabaseUpdated,
    
    /// <summary>
    /// 缓存已失效
    /// </summary>
    CacheInvalidated,
    
    /// <summary>
    /// Actor通知已发送
    /// </summary>
    ActorNotified,
    
    /// <summary>
    /// SignalR通知已发送
    /// </summary>
    SignalRNotified,
    
    /// <summary>
    /// 配置已生效
    /// </summary>
    Effective,
    
    /// <summary>
    /// 配置生效失败
    /// </summary>
    Failed,
    
    /// <summary>
    /// 配置生效超时
    /// </summary>
    Timeout
}

/// <summary>
/// 配置生效跟踪记录
/// </summary>
public class ConfigurationEffectivenessRecord
{
    public string TrackingId { get; set; } = string.Empty;
    public string ConfigType { get; set; } = string.Empty;
    public string ConfigKey { get; set; } = string.Empty;
    public object? NewValue { get; set; }
    public List<string>? AffectedWcIds { get; set; }
    public ConfigurationEffectivenessStatus Status { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration => EndTime?.Subtract(StartTime);
    public List<string> StatusHistory { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> ExtendedData { get; set; } = new();
}

/// <summary>
/// 配置生效跟踪器接口
/// </summary>
public interface IConfigurationEffectivenessTracker
{
    /// <summary>
    /// 开始跟踪配置变更
    /// </summary>
    string StartTracking(string configType, string configKey, object? newValue, List<string>? affectedWcIds = null);
    
    /// <summary>
    /// 更新跟踪状态
    /// </summary>
    Task UpdateStatusAsync(string trackingId, ConfigurationEffectivenessStatus status, string? details = null);
    
    /// <summary>
    /// 完成跟踪
    /// </summary>
    Task CompleteTrackingAsync(string trackingId, bool success, string? errorMessage = null);
    
    /// <summary>
    /// 获取跟踪记录
    /// </summary>
    ConfigurationEffectivenessRecord? GetTrackingRecord(string trackingId);
    
    /// <summary>
    /// 获取所有活跃的跟踪记录
    /// </summary>
    List<ConfigurationEffectivenessRecord> GetActiveTrackingRecords();
    
    /// <summary>
    /// 清理过期的跟踪记录
    /// </summary>
    Task CleanupExpiredRecordsAsync();
}

/// <summary>
/// 配置生效跟踪器实现
/// </summary>
public class ConfigurationEffectivenessTracker : IConfigurationEffectivenessTracker
{
    private readonly ILogger<ConfigurationEffectivenessTracker> _logger;
    private readonly ConcurrentDictionary<string, ConfigurationEffectivenessRecord> _trackingRecords = new();
    
    // 跟踪记录保留时间（1小时）
    private static readonly TimeSpan RECORD_RETENTION_TIME = TimeSpan.FromHours(1);
    
    // 配置生效超时时间（20秒）
    private static readonly TimeSpan EFFECTIVENESS_TIMEOUT = TimeSpan.FromSeconds(20);

    public ConfigurationEffectivenessTracker(ILogger<ConfigurationEffectivenessTracker> logger)
    {
        _logger = logger;
        
        // 启动清理任务
        _ = Task.Run(async () =>
        {
            while (true)
            {
                try
                {
                    await CleanupExpiredRecordsAsync();
                    await Task.Delay(TimeSpan.FromMinutes(10)); // 每10分钟清理一次
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清理过期跟踪记录失败");
                }
            }
        });
    }

    public string StartTracking(string configType, string configKey, object? newValue, List<string>? affectedWcIds = null)
    {
        var trackingId = Guid.NewGuid().ToString("N")[..12];
        var record = new ConfigurationEffectivenessRecord
        {
            TrackingId = trackingId,
            ConfigType = configType,
            ConfigKey = configKey,
            NewValue = newValue,
            AffectedWcIds = affectedWcIds,
            Status = ConfigurationEffectivenessStatus.Started,
            StartTime = DateTime.UtcNow
        };
        
        record.StatusHistory.Add($"{DateTime.UtcNow:HH:mm:ss.fff} - Started");
        
        _trackingRecords.TryAdd(trackingId, record);
        
        // 🔧 注释冗余的配置生效跟踪开始日志 - 减少日志噪音，每次配置更新都会重复出现
        // _logger.LogInformation("📊 [跟踪:{TrackingId}] 开始配置生效跟踪 - {ConfigType}.{ConfigKey}",
        //     trackingId, configType, configKey);
        
        return trackingId;
    }

    public async Task UpdateStatusAsync(string trackingId, ConfigurationEffectivenessStatus status, string? details = null)
    {
        if (!_trackingRecords.TryGetValue(trackingId, out var record))
        {
            _logger.LogWarning("⚠️ 跟踪记录不存在 - TrackingId: {TrackingId}", trackingId);
            return;
        }

        var previousStatus = record.Status;
        record.Status = status;
        
        var statusDetail = string.IsNullOrEmpty(details) ? status.ToString() : $"{status} - {details}";
        record.StatusHistory.Add($"{DateTime.UtcNow:HH:mm:ss.fff} - {statusDetail}");
        
        _logger.LogDebug("📈 [跟踪:{TrackingId}] 状态更新: {PreviousStatus} → {NewStatus}", 
            trackingId, previousStatus, status);

        // 检查是否超时
        if (DateTime.UtcNow - record.StartTime > EFFECTIVENESS_TIMEOUT && 
            status != ConfigurationEffectivenessStatus.Effective &&
            status != ConfigurationEffectivenessStatus.Failed)
        {
            await UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.Timeout, "配置生效超时");
        }
        
        await Task.CompletedTask;
    }

    public async Task CompleteTrackingAsync(string trackingId, bool success, string? errorMessage = null)
    {
        if (!_trackingRecords.TryGetValue(trackingId, out var record))
        {
            _logger.LogWarning("⚠️ 跟踪记录不存在 - TrackingId: {TrackingId}", trackingId);
            return;
        }

        record.EndTime = DateTime.UtcNow;
        record.Status = success ? ConfigurationEffectivenessStatus.Effective : ConfigurationEffectivenessStatus.Failed;
        
        if (!string.IsNullOrEmpty(errorMessage))
        {
            record.ErrorMessage = errorMessage;
        }
        
        var finalStatus = success ? "成功" : "失败";
        record.StatusHistory.Add($"{DateTime.UtcNow:HH:mm:ss.fff} - 完成({finalStatus})");
        
        var duration = record.Duration?.TotalMilliseconds ?? 0;
        
        if (success)
        {
            // 🔧 注释冗余的配置生效完成日志 - 减少日志噪音，每次配置更新都会重复出现
            // _logger.LogInformation("✅ [跟踪:{TrackingId}] 配置生效完成 - {ConfigType}.{ConfigKey}, 耗时: {Duration}ms",
            //     trackingId, record.ConfigType, record.ConfigKey, duration);
        }
        else
        {
            _logger.LogError("❌ [跟踪:{TrackingId}] 配置生效失败 - {ConfigType}.{ConfigKey}, 耗时: {Duration}ms, 错误: {Error}", 
                trackingId, record.ConfigType, record.ConfigKey, duration, errorMessage);
        }
        
        await Task.CompletedTask;
    }

    public ConfigurationEffectivenessRecord? GetTrackingRecord(string trackingId)
    {
        return _trackingRecords.TryGetValue(trackingId, out var record) ? record : null;
    }

    public List<ConfigurationEffectivenessRecord> GetActiveTrackingRecords()
    {
        return _trackingRecords.Values
            .Where(r => r.Status != ConfigurationEffectivenessStatus.Effective && 
                       r.Status != ConfigurationEffectivenessStatus.Failed &&
                       r.Status != ConfigurationEffectivenessStatus.Timeout)
            .OrderByDescending(r => r.StartTime)
            .ToList();
    }

    public async Task CleanupExpiredRecordsAsync()
    {
        var expiredKeys = _trackingRecords
            .Where(kvp => DateTime.UtcNow - kvp.Value.StartTime > RECORD_RETENTION_TIME)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in expiredKeys)
        {
            _trackingRecords.TryRemove(key, out _);
        }

        if (expiredKeys.Count > 0)
        {
            _logger.LogDebug("🧹 清理过期跟踪记录: {Count} 条", expiredKeys.Count);
        }
        
        await Task.CompletedTask;
    }
}
