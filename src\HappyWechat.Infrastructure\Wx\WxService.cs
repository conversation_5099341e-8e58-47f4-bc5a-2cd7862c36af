using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis.Extensions.Core.Abstractions;
using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.Wrappers.EYun.Requests;
using HappyWechat.Application.DTOs.Wrappers.EYun.Responses;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.Options;
using HappyWechat.Domain.Entities;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Caching;
using HappyWechat.Infrastructure.Caching.Interfaces;
using HappyWechat.Infrastructure.Notifications;
using HappyWechat.Infrastructure.Redis;
using Microsoft.AspNetCore.SignalR;
using HappyWechat.Infrastructure.SignalR;

namespace HappyWechat.Infrastructure.Wx;

public class WxService(
    ApplicationDbContext dbContext,
    IMapper mapper,
    IEYunWrapper eYunWrapper,
    IOptions<EYunOptions> eYunOptions,
    ILogger<WxService> logger,
    IUnifiedRedisCacheService cacheService,
    IWxContactListRepository contactListRepository,
    IWxContactCacheService wxContactCacheService,
    IWxContactQueryOptimizer contactQueryOptimizer,
    IUnifiedRedisNotificationService notificationService,
    IContactBatchCacheService batchCacheService,
    CacheInvalidationService cacheInvalidationService)
    : IWxService
{
    public async Task<WxQrDto> GetQrCodeAsync(string userId, WxGetQrCodeQuery qrCodeQuery)
    {
        logger.LogInformation($"获取二维码 - 用户ID: {userId}, 请求参数: {System.Text.Json.JsonSerializer.Serialize(qrCodeQuery)}");
        
        var wxLoginWIdKey = string.Format(Keys.SWxLoginWIdKeyPrefix, userId);
        var wId = await cacheService.GetAsync<string>(wxLoginWIdKey) ?? "";
        logger.LogInformation($"从缓存获取的WId: {wId}");

        // 查询用户是否存在微信记录
        var existingWxManger = await dbContext.WxMangerEntities
            .FirstOrDefaultAsync(x => x.UserId == Guid.Parse(userId));

        if (existingWxManger == null)
        {
            // 【修复】先调用E云接口获取二维码，再创建数据库记录 - 避免API失败时创建孤儿记录
            using var transaction = await dbContext.Database.BeginTransactionAsync();
            try
            {
                // 构建E云请求 - 完整参数传递
                var eYunGetQrCodeRequest = new EYunGetQrCodeRequest
                {
                    WcId = string.Empty, // 首次登录传空字符串
                    Proxy = qrCodeQuery.Proxy,
                    ProxyIp = qrCodeQuery.ProxyIp,
                    ProxyUser = qrCodeQuery.ProxyUser,
                    ProxyPassword = qrCodeQuery.ProxyPassword,
                    DeviceType = qrCodeQuery.DeviceType ?? "ipad"
                };
                
                // 【修复】先调用E云接口获取二维码
                EYunQrCodeData eYunQrCodeData = await eYunWrapper.GetQrCodeAsync(eYunGetQrCodeRequest);
                logger.LogInformation($"E云返回数据: {System.Text.Json.JsonSerializer.Serialize(eYunQrCodeData)}");

                // 【修复】E云API成功后再创建数据库记录
                var wxMangerEntity = new WxMangerEntity
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.Parse(userId),
                    WxStatus = WxStatus.NotLogIn,
                    NickName = string.Empty,
                    WAccount = string.Empty,
                    DeviceType = string.IsNullOrEmpty(qrCodeQuery.DeviceType) ? "ipad" : qrCodeQuery.DeviceType,
                    WId = eYunQrCodeData.WId, // 直接设置WId
                    CreatedTime = DateTime.UtcNow,
                    UpdatedTime = DateTime.UtcNow
                };
                
                await dbContext.WxMangerEntities.AddAsync(wxMangerEntity);
                await dbContext.SaveChangesAsync();
                logger.LogInformation($"创建新的微信管理记录: {System.Text.Json.JsonSerializer.Serialize(wxMangerEntity)}");

                // 缓存wId
                await cacheService.SetAsync(wxLoginWIdKey, eYunQrCodeData.WId, TimeSpan.FromDays(3));
                
                await transaction.CommitAsync();
                
                var result = mapper.Map<WxQrDto>(eYunQrCodeData);
                logger.LogInformation($"返回首次登录二维码数据: {System.Text.Json.JsonSerializer.Serialize(result)}");
                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                logger.LogError(ex, $"首次登录获取二维码失败 - 用户ID: {userId}");
                
                // 清理可能的缓存残留
                await cacheService.RemoveAsync(wxLoginWIdKey);
                
                // 重新抛出异常，让上层处理
                throw;
            }
        }
        
        // 已存在微信记录，检查状态
        if (existingWxManger.WxStatus == WxStatus.AlreadyLogIn)
        {
            logger.LogInformation($"用户已登录，返回已登录状态");
            return new WxQrDto
            {
                WId = existingWxManger.WId ?? string.Empty,
                QrCodeUrl = string.Empty // 已登录状态不需要二维码
            };
        }
        
        // 【修复】构建E云请求 - 使用传入的qrCodeQuery参数而不是硬编码
        var eYunRequest = new EYunGetQrCodeRequest
        {
            WcId = existingWxManger.WcId ?? string.Empty,
            Proxy = qrCodeQuery.Proxy, // 使用传入的代理参数
            ProxyIp = qrCodeQuery.ProxyIp,
            ProxyUser = qrCodeQuery.ProxyUser,
            ProxyPassword = qrCodeQuery.ProxyPassword,
            DeviceType = qrCodeQuery.DeviceType ?? "ipad"
        };
        
        logger.LogInformation($"重登录E云请求参数: WcId={eYunRequest.WcId}, Proxy={eYunRequest.Proxy}, DeviceType={eYunRequest.DeviceType}");
        
        // 【修复】使用事务确保数据一致性
        using var reloginTransaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            // 调用E云接口获取二维码
            EYunQrCodeData eYunQrCodeData = await eYunWrapper.GetQrCodeAsync(eYunRequest);
            logger.LogInformation($"E云返回数据: {System.Text.Json.JsonSerializer.Serialize(eYunQrCodeData)}");

            // 【修复】更新微信状态并保存WId到数据库
            existingWxManger.WxStatus = WxStatus.NotLogIn;
            existingWxManger.WId = eYunQrCodeData.WId; // 保存新的WId
            existingWxManger.UpdatedTime = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();
            logger.LogInformation($"已更新WId到数据库 - AccountId: {existingWxManger.Id}, WId: {eYunQrCodeData.WId}");
            
            // 缓存wId
            await cacheService.SetAsync(wxLoginWIdKey, eYunQrCodeData.WId, TimeSpan.FromDays(3));
            
            await reloginTransaction.CommitAsync();
            
            var result = mapper.Map<WxQrDto>(eYunQrCodeData);
            logger.LogInformation($"返回二维码数据: {System.Text.Json.JsonSerializer.Serialize(result)}");
            return result;
        }
        catch (Exception ex)
        {
            await reloginTransaction.RollbackAsync();
            logger.LogError(ex, "获取二维码失败");
            
            if (ex.Message.Contains("wId已注销") || ex.Message.Contains("二维码失效"))
            {
                logger.LogWarning($"微信wId已失效，将用户 {userId} 的微信状态设置为未登录");
                
                // 【修复】使用独立事务处理状态清理，避免影响主流程
                using var statusCleanupTransaction = await dbContext.Database.BeginTransactionAsync();
                try
                {
                    // 清除缓存
                    await cacheService.RemoveAsync(wxLoginWIdKey);
                    
                    // 更新微信状态为未登录
                    existingWxManger.WxStatus = WxStatus.NotLogIn;
                    existingWxManger.WId = string.Empty;
                    await dbContext.SaveChangesAsync();
                    
                    await statusCleanupTransaction.CommitAsync();
                }
                catch (Exception cleanupEx)
                {
                    await statusCleanupTransaction.RollbackAsync();
                    logger.LogError(cleanupEx, "清理失效微信状态时发生错误");
                }
                
                // 抛出异常以便上层处理
                throw new InvalidOperationException("微信已注销或二维码失效，请重新登录");
            }
            
            throw;
        }
    }

    public async Task<WxLoginStatusDto> CheckLoginStatusAsync(string userId)
    {
        logger.LogInformation($"检查登录状态 - 用户ID: {userId}");
        
        // 查询用户微信记录
        var wxMangerEntity = await dbContext.WxMangerEntities
            .FirstOrDefaultAsync(x => x.UserId == Guid.Parse(userId));
        
        if (wxMangerEntity == null)
        {
            logger.LogWarning($"用户 {userId} 没有微信记录");
            return new WxLoginStatusDto
            {
                Status = "not_found",
                Message = "未找到微信记录"
            };
        }
        
        logger.LogInformation($"查询到的用户微信记录: {System.Text.Json.JsonSerializer.Serialize(wxMangerEntity)}");
        
        // 获取缓存的wId
        var wxLoginWIdKey = string.Format(Keys.SWxLoginWIdKeyPrefix, userId);
        var wId = await cacheService.GetAsync<string>(wxLoginWIdKey) ?? "";
        
        // 构建E云请求
        var eYunIsOnlineRequest = new EYunIsOnlineRequest
        {
            WId = wId
        };

        try
        {
            // 调用E云接口检查登录状态
            var eYunIsOnlineData = await eYunWrapper.IsOnlineAsync(eYunIsOnlineRequest);
            logger.LogInformation($"E云返回数据: {System.Text.Json.JsonSerializer.Serialize(eYunIsOnlineData)}");

            // 更新微信状态
            if (eYunIsOnlineData.IsOnline && wxMangerEntity.WxStatus != WxStatus.AlreadyLogIn)
            {
                wxMangerEntity.WxStatus = WxStatus.AlreadyLogIn;
                wxMangerEntity.WId = wId;
                wxMangerEntity.UpdatedTime = DateTime.UtcNow;
                await dbContext.SaveChangesAsync();

                logger.LogInformation($"用户 {userId} 登录成功");
            }
            else if (!eYunIsOnlineData.IsOnline && wxMangerEntity.WxStatus != WxStatus.NotLogIn)
            {
                // 备份 WcId 到缓存，供重登录时使用
                if (!string.IsNullOrEmpty(wxMangerEntity.WcId))
                {
                    var accountWIdBackupKey = string.Format(Keys.SWxAccountWId, wxMangerEntity.Id);
                    await cacheService.SetAsync(accountWIdBackupKey, wxMangerEntity.WcId.Trim(), TimeSpan.FromDays(90)); // 备份90天
                    logger.LogInformation($"检查登录状态时备份 WcId - 账户ID: {wxMangerEntity.Id}, WcId: {wxMangerEntity.WcId}");
                }
                
                wxMangerEntity.WxStatus = WxStatus.NotLogIn;
                wxMangerEntity.WcId = string.Empty;
                wxMangerEntity.WId = string.Empty;
                wxMangerEntity.UpdatedTime = DateTime.UtcNow;
                await dbContext.SaveChangesAsync();

                logger.LogInformation($"用户 {userId} 未登录");
            }

            // 构建返回结果
            var result = new WxLoginStatusDto
            {
                Status = eYunIsOnlineData.IsOnline ? "logged_in" : "not_logged_in",
                Message = eYunIsOnlineData.StatusDesc
            };
            logger.LogInformation($"返回登录状态: {System.Text.Json.JsonSerializer.Serialize(result)}");
            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "检查登录状态失败");
            
            if (ex.Message.Contains("wId已注销") || ex.Message.Contains("二维码失效"))
            {
                logger.LogWarning($"微信wId已失效，将用户 {userId} 的微信状态设置为未登录");
                
                // 备份 WcId 到缓存，供重登录时使用
                if (!string.IsNullOrEmpty(wxMangerEntity.WcId))
                {
                    var accountWIdBackupKey = string.Format(Keys.SWxAccountWId, wxMangerEntity.Id);
                    await cacheService.SetAsync(accountWIdBackupKey, wxMangerEntity.WcId.Trim(), TimeSpan.FromDays(90)); // 备份90天
                    logger.LogInformation($"wId失效时备份 WcId - 账户ID: {wxMangerEntity.Id}, WcId: {wxMangerEntity.WcId}");
                }
                
                // 清除缓存
                await cacheService.RemoveAsync(wxLoginWIdKey);
                
                // 更新微信状态为未登录
                wxMangerEntity.WxStatus = WxStatus.NotLogIn;
                wxMangerEntity.WcId = string.Empty;
                wxMangerEntity.WId = string.Empty;
                await dbContext.SaveChangesAsync();
            }
            
            return new WxLoginStatusDto
            {
                Status = "error",
                Message = ex.Message
            };
        }
    }

    public async Task<WxLoginDto> ConfirmLoginAsync(Guid userId, WxLoginCommand loginCommand)
    {
        logger.LogInformation($"确认登录 - 用户ID: {userId}, 请求参数: {System.Text.Json.JsonSerializer.Serialize(loginCommand)}");

        var wxLoginWIdKey = string.Format(Keys.SWxLoginWIdKeyPrefix, userId);
        var wId = await cacheService.GetAsync<string>(wxLoginWIdKey) ?? "";

        // 查询用户微信记录
        var wxMangerEntity = await dbContext.WxMangerEntities
            .FirstOrDefaultAsync(x => x.UserId == userId);

        if (wxMangerEntity == null)
        {
            throw new ArgumentException("用户微信记录不存在");
        }

        // 【修复】使用事务确保数据一致性
        using var loginTransaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            // 构建E云请求
            var eYunConfirmLoginRequest = new EYunConfirmLoginRequest
            {
                WId = wId,
                VerifyCode = loginCommand.VerifyCode
            };

            // 调用E云接口确认登录
            var eYunConfirmLoginData = await eYunWrapper.ConfirmLoginAsync(eYunConfirmLoginRequest);
            logger.LogInformation($"E云返回数据: {System.Text.Json.JsonSerializer.Serialize(eYunConfirmLoginData)}");

            // 更新微信状态 - 完善所有字段映射
            wxMangerEntity.WxStatus = WxStatus.AlreadyLogIn;
            wxMangerEntity.WcId = eYunConfirmLoginData.WcId?.Trim(); // 【修复】清理WcId格式
            wxMangerEntity.WId = wId;
            wxMangerEntity.NickName = eYunConfirmLoginData.NickName;
            wxMangerEntity.HeadUrl = eYunConfirmLoginData.HeadUrl;
            wxMangerEntity.WAccount = eYunConfirmLoginData.WAccount;
            wxMangerEntity.Sex = eYunConfirmLoginData.Sex;
            wxMangerEntity.MobilePhone = eYunConfirmLoginData.MobilePhone;
            wxMangerEntity.Status = eYunConfirmLoginData.Status.ToString();
            
            // 设置DeviceType，如果为空则默认为"ipad"
            if (string.IsNullOrEmpty(wxMangerEntity.DeviceType))
            {
                wxMangerEntity.DeviceType = string.IsNullOrEmpty(eYunConfirmLoginData.DeviceType) ? "ipad" : eYunConfirmLoginData.DeviceType;
            }
            
            wxMangerEntity.UpdatedTime = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            // 缓存微信管理员ID的缓存
            await cacheService.SetAsync(wxLoginWIdKey, wxMangerEntity.Id.ToString(), TimeSpan.FromDays(7));
            // 缓存微信管理员详细信息缓存
            await cacheService.SetAsync(string.Format(Keys.SWxManagerDetail, wxMangerEntity.Id), wxMangerEntity, TimeSpan.FromDays(7));
            
            // 【修复】缓存WcId到账户特定缓存键，用于重登录时获取 - 延长缓存时间到30天
            var accountWIdKey = string.Format(Keys.SWxAccountWId, wxMangerEntity.Id);
            var cleanWcId = eYunConfirmLoginData.WcId?.Trim();
            await cacheService.SetAsync(accountWIdKey, cleanWcId, TimeSpan.FromDays(30)); // 延长缓存时间
            logger.LogInformation($"已缓存WcId到账户缓存 - AccountId: {wxMangerEntity.Id}, WcId: {eYunConfirmLoginData.WcId}");
            
            // 缓存账户登录状态
            var accountLoginStatusKey = string.Format(Keys.SWxAccountLoginStatus, wxMangerEntity.Id);
            await cacheService.SetAsync(accountLoginStatusKey, WxStatus.AlreadyLogIn, TimeSpan.FromDays(7));

            await loginTransaction.CommitAsync();
            
            // 【修复】发送SignalR通知刷新前端账户列表
            try
            {
                await notificationService.PublishCacheInvalidationAsync($"ui_refresh:wx_accounts:{userId}");
                logger.LogInformation($"已发送账户列表刷新通知 - 用户ID: {userId}, 账户ID: {wxMangerEntity.Id}");
            }
            catch (Exception notifyEx)
            {
                // 通知失败不应影响主流程，仅记录日志
                logger.LogWarning(notifyEx, $"发送账户列表刷新通知失败 - 用户ID: {userId}");
            }

            var loginDto = mapper.Map<WxLoginDto>(eYunConfirmLoginData);
            loginDto.NeedVerifyCode = false;
            logger.LogInformation($"返回登录成功数据: {System.Text.Json.JsonSerializer.Serialize(loginDto)}");
            return loginDto;
        }
        catch (Exception ex)
        {
            await loginTransaction.RollbackAsync();
            logger.LogError(ex, $"确认登录失败 - 用户ID: {userId}");
            
            // 清理可能的缓存残留
            try
            {
                await cacheService.RemoveAsync(wxLoginWIdKey);
                await cacheService.RemoveAsync(string.Format(Keys.SWxManagerDetail, wxMangerEntity.Id));
                await cacheService.RemoveAsync(string.Format(Keys.SWxAccountWId, wxMangerEntity.Id));
                await cacheService.RemoveAsync(string.Format(Keys.SWxAccountLoginStatus, wxMangerEntity.Id));
            }
            catch (Exception cacheEx)
            {
                logger.LogWarning(cacheEx, "清理缓存时发生错误");
            }
            
            throw;
        }
    }

    /// <summary>
    /// 清理孤儿记录 - 删除没有有效WId且状态为未登录的记录
    /// </summary>
    public async Task<int> CleanupOrphanRecordsAsync(Guid userId)
    {
        logger.LogInformation($"开始清理用户孤儿记录 - 用户ID: {userId}");
        
        using var cleanupTransaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            // 查找孤儿记录：没有WId且状态为未登录的记录
            var orphanRecords = await dbContext.WxMangerEntities
                .Where(w => w.UserId == userId && 
                           w.WxStatus == WxStatus.NotLogIn && 
                           (string.IsNullOrEmpty(w.WId) || string.IsNullOrEmpty(w.WcId)) &&
                           string.IsNullOrEmpty(w.NickName))
                .ToListAsync();
            
            if (!orphanRecords.Any())
            {
                logger.LogInformation($"未发现孤儿记录 - 用户ID: {userId}");
                await cleanupTransaction.CommitAsync();
                return 0;
            }
            
            logger.LogInformation($"发现 {orphanRecords.Count} 条孤儿记录 - 用户ID: {userId}");
            
            // 删除孤儿记录
            dbContext.WxMangerEntities.RemoveRange(orphanRecords);
            await dbContext.SaveChangesAsync();
            
            // 清理相关缓存
            foreach (var record in orphanRecords)
            {
                try
                {
                    await cacheService.RemoveAsync(string.Format(Keys.SWxManagerDetail, record.Id));
                    await cacheService.RemoveAsync(string.Format(Keys.SWxAccountWId, record.Id));
                    await cacheService.RemoveAsync(string.Format(Keys.SWxAccountLoginStatus, record.Id));
                }
                catch (Exception cacheEx)
                {
                    logger.LogWarning(cacheEx, $"清理记录缓存失败 - 记录ID: {record.Id}");
                }
            }
            
            await cleanupTransaction.CommitAsync();
            
            logger.LogInformation($"成功清理 {orphanRecords.Count} 条孤儿记录 - 用户ID: {userId}");
            return orphanRecords.Count;
        }
        catch (Exception ex)
        {
            await cleanupTransaction.RollbackAsync();
            logger.LogError(ex, $"清理孤儿记录失败 - 用户ID: {userId}");
            throw;
        }
    }
    
    public async Task<PageResponse<WxMangerDto>> getMangedWxListAsync(Guid userId, GetManagedWxListQuery query)
    {
        IQueryable<WxMangerEntity> queryable = dbContext.WxMangerEntities.AsQueryable()
            .Where(w => w.UserId == userId)
            .OrderBy(w => w.NickName);

        var oldPageResponse = await EFCorePageUtil.GetPagedResultAsync(queryable, query.PageQuery);
        var wxMangerDtos = oldPageResponse.Items.Select(mapper.Map<WxMangerDto>).ToList();
        return PageResponse<WxMangerDto>.ReplaceItems(oldPageResponse, wxMangerDtos);
    }

    public async Task<PageResponse<WxContactDto>> getContactOrGroupListAsync(Guid userId, GetContactOrGroupListQuery query)
    {
        // 🔧 注释冗余的查询联系人列表开始日志 - 减少日志噪音，避免敏感参数泄露
        // logger.LogInformation($"开始查询联系人列表 - 用户ID: {userId}, 微信管理员ID: {query.WxManagerId}, 查询参数: {System.Text.Json.JsonSerializer.Serialize(query)}");

        // 验证用户ID有效性
        var wxMangerEntity = await dbContext.WxMangerEntities
            .FirstOrDefaultAsync(x => x.UserId == userId && x.Id == query.WxManagerId);

        if (wxMangerEntity == null)
        {
            logger.LogWarning($"用户 {userId} 没有微信管理员记录 {query.WxManagerId}");
            return new PageResponse<WxContactDto>
            {
                Items = new List<WxContactDto>(),
                TotalCount = 0,
                Page = query.PageQuery.Page,
                PageSize = query.PageQuery.PageSize
            };
        }

        try
        {
            // 使用优化的查询服务 - 支持多个联系人类型
            var result = await contactQueryOptimizer.GetOptimizedContactListAsync(
                query.WxManagerId,
                query.ContactTypes.Any() ? query.ContactTypes : new List<WxContactType> { query.ContactType },
                query.SearchedNickName,
                query.SearchedRemarkName,
                query.PageQuery.Page,
                query.PageQuery.PageSize);
            // 🔧 注释冗余的优化查询成功日志 - 减少日志噪音
            // logger.LogInformation($"优化查询成功 - 返回 {result.Items.Count} 条记录");
            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "优化查询失败，回退到传统查询");

            // 回退到传统查询方法
            return await GetContactListFallbackAsync(query, wxMangerEntity);
        }
    }

    private async Task<PageResponse<WxContactDto>> GetContactListFallbackAsync(GetContactOrGroupListQuery query, WxMangerEntity wxMangerEntity)
    {
        logger.LogInformation("使用传统查询方法");

        IQueryable<WxContactEntity> queryable = dbContext.WxContactEntities.AsQueryable()
            .Where(c => c.WxManagerId == wxMangerEntity.Id);

        // 应用联系人类型过滤 - 支持多个类型，使用OR条件避免EF Core限制
        var contactTypes = query.ContactTypes.Any() ? query.ContactTypes : new List<WxContactType> { query.ContactType };

        if (contactTypes.Count == 1)
        {
            queryable = queryable.Where(c => c.ContactType == contactTypes[0]);
        }
        else if (contactTypes.Count == 2)
        {
            var type1 = contactTypes[0];
            var type2 = contactTypes[1];
            queryable = queryable.Where(c => c.ContactType == type1 || c.ContactType == type2);
        }
        else if (contactTypes.Count == 3)
        {
            var type1 = contactTypes[0];
            var type2 = contactTypes[1];
            var type3 = contactTypes[2];
            queryable = queryable.Where(c => c.ContactType == type1 || c.ContactType == type2 || c.ContactType == type3);
        }
        else if (contactTypes.Count == 4)
        {
            var type1 = contactTypes[0];
            var type2 = contactTypes[1];
            var type3 = contactTypes[2];
            var type4 = contactTypes[3];
            queryable = queryable.Where(c => c.ContactType == type1 || c.ContactType == type2 || c.ContactType == type3 || c.ContactType == type4);
        }
        else if (contactTypes.Count > 4)
        {
            // 对于超过4个类型的情况，使用客户端评估
            var contactTypeValues = contactTypes.Select(ct => (int)ct).ToList();
            queryable = queryable.Where(c => contactTypeValues.Contains((int)c.ContactType));
        }

        // 应用搜索条件
        if (!string.IsNullOrEmpty(query.SearchedNickName))
        {
            queryable = queryable.Where(c => c.NickName != null && c.NickName.Contains(query.SearchedNickName));
        }

        if (!string.IsNullOrEmpty(query.SearchedRemarkName))
        {
            queryable = queryable.Where(c => c.Remark != null && c.Remark.Contains(query.SearchedRemarkName));
        }

        queryable = queryable.OrderBy(c => c.NickName);

        var oldPageResponse = await EFCorePageUtil.GetPagedResultAsync(queryable, query.PageQuery);
        var wxContactDtos = oldPageResponse.Items.Select(mapper.Map<WxContactDto>).ToList();

        logger.LogInformation($"传统查询完成 - 返回 {wxContactDtos.Count} 条记录");
        return PageResponse<WxContactDto>.ReplaceItems(oldPageResponse, wxContactDtos);
    }

    public async Task ClearContactQueryCacheAsync(Guid managerId, WxContactType? contactType = null)
    {
        try
        {
            logger.LogInformation($"[分层缓存清理] 开始清理联系人缓存 - ManagerId: {managerId}, Type: {contactType}");

            // 使用统一缓存服务清理
            var pattern = $"contact:*:{managerId}:*";
            if (contactType.HasValue)
            {
                pattern = $"contact:{contactType.Value}:{managerId}:*";
            }
            await cacheService.RemoveByPatternAsync(pattern);
            await notificationService.PublishCacheInvalidationByPatternAsync(pattern);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"[分层缓存清理] 清理联系人缓存失败 - ManagerId: {managerId}, Type: {contactType}");
        }
    }

    public async Task Logout(Guid userId)
    {
        logger.LogInformation($"开始登出操作 - 用户ID: {userId}");

        var wxMangerEntity = await dbContext.WxMangerEntities
            .FirstOrDefaultAsync(item => item.UserId == userId);

        if (wxMangerEntity == null)
        {
            logger.LogWarning($"用户 {userId} 没有微信记录");
            return;
        }

        // 备份 WcId 到缓存，然后清除其他缓存
        if (!string.IsNullOrEmpty(wxMangerEntity.WcId))
        {
            var accountWIdBackupKey = string.Format(Keys.SWxAccountWId, wxMangerEntity.Id);
            await cacheService.SetAsync(accountWIdBackupKey, wxMangerEntity.WcId.Trim(), TimeSpan.FromDays(90)); // 备份90天
            logger.LogInformation($"通用登出时备份 WcId - 账户ID: {wxMangerEntity.Id}, WcId: {wxMangerEntity.WcId}");
        }
        
        string loginWIdKey = string.Format(Keys.SWxLoginWIdKeyPrefix, userId);
        await cacheService.RemoveAsync(loginWIdKey);
        wxMangerEntity.WId = string.Empty;
        wxMangerEntity.WcId = string.Empty;
        wxMangerEntity.WxStatus = WxStatus.NotLogIn;
        await dbContext.SaveChangesAsync();

        logger.LogInformation($"登出操作完成 - 用户ID: {userId}");
    }

    public async Task setContactReplyOption(Guid userId, WxContactReplyOption option)
    {
        WxMangerEntity? wxMangerEntity =
            await dbContext.WxMangerEntities.FirstOrDefaultAsync(item => item.UserId == userId);
        if (wxMangerEntity is null)
        {
            throw new ArgumentException("微信未登录");
        }

        string replyOptionKey = string.Format(Keys.SWxContactReplyOption, wxMangerEntity.WcId);
        await cacheService.SetAsync(replyOptionKey, option);
    }

    public async Task<WxContactReplyOption> getContactReplyOption(Guid userId)
    {
        WxMangerEntity? wxMangerEntity =
            await dbContext.WxMangerEntities.FirstOrDefaultAsync(item => item.UserId == userId);
        if (wxMangerEntity is null)
        {
            throw new ArgumentException("微信未登录");
        }

        string replyOptionKey = string.Format(Keys.SWxContactReplyOption, wxMangerEntity.WcId);
        return await cacheService.GetAsync<WxContactReplyOption>(replyOptionKey);
    }

    public async Task SetContactAutoReplyStatus(Guid userId, Guid contactId, bool enabled)
    {
        // 验证用户是否已登录
        WxMangerEntity? wxMangerEntity =
            await dbContext.WxMangerEntities.FirstOrDefaultAsync(item =>
                item.UserId == userId);

        if (wxMangerEntity == null)
        {
            throw new ArgumentException("用户未登录微信");
        }

        // 查找联系人/群组
        var contact = await dbContext.WxContactEntities
            .FirstOrDefaultAsync(c => c.Id == contactId && c.WxManagerId == wxMangerEntity.Id);

        if (contact == null)
        {
            throw new ArgumentException("联系人/群组不存在");
        }

        // 更新自动回复状态
        contact.IsAiEnabled = enabled;
        contact.UpdatedAt = DateTime.UtcNow;
        await dbContext.SaveChangesAsync();

        // 更新缓存：1. 联系人/群组缓存
        string cacheKey = contact.ContactType == WxContactType.Contact
            ? string.Format(Keys.SWxContact, wxMangerEntity.Id, contact.WcId)
            : string.Format(Keys.SWxGroup, wxMangerEntity.Id, contact.WcId);
        await cacheService.SetAsync(cacheKey, contact, TimeSpan.FromDays(7));

        // 使用缓存失效服务清理相关缓存
        try
        {
            await cacheInvalidationService.InvalidateCacheAsync(cacheKey);
            await notificationService.PublishCacheInvalidationAsync($"ui_refresh:contact:{contactId}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "缓存失效处理失败");
        }

        // 缓存微信管理员ID的缓存
        string wxLoginWIdKey = string.Format(Keys.SWxLoginWIdKeyPrefix, userId);
        await cacheService.SetAsync(wxLoginWIdKey, wxMangerEntity.Id.ToString(), TimeSpan.FromDays(7));
        // 缓存微信管理员详细信息缓存
        await cacheService.SetAsync(string.Format(Keys.SWxManagerDetail, wxMangerEntity.Id), wxMangerEntity, TimeSpan.FromDays(7));

        logger.LogInformation($"已更新联系人/群组 {contactId} 的自动回复状态为: {enabled}，并清理缓存");
    }

    public async Task DeleteContact(Guid userId, Guid contactId)
    {
        // 验证用户是否已登录
        WxMangerEntity? wxMangerEntity =
            await dbContext.WxMangerEntities.FirstOrDefaultAsync(item =>
                item.UserId == userId);

        if (wxMangerEntity == null)
        {
            throw new ArgumentException("用户未登录微信");
        }

        // 查找联系人
        var contact = await dbContext.WxContactEntities
            .FirstOrDefaultAsync(c => c.Id == contactId && c.WxManagerId == wxMangerEntity.Id);

        if (contact == null)
        {
            throw new ArgumentException("联系人不存在");
        }

        try
        {
            // 删除联系人记录
            dbContext.WxContactEntities.Remove(contact);
            await dbContext.SaveChangesAsync();

            // 清理缓存
            string cacheKey = string.Format(Keys.SWxContact, wxMangerEntity.Id, contact.WcId);
            await cacheService.RemoveAsync(cacheKey);

            // 发送缓存失效通知
            await notificationService.PublishCacheInvalidationAsync(cacheKey);
            await notificationService.PublishCacheInvalidationAsync($"ui_refresh:contact:{contactId}");

            logger.LogInformation($"删除联系人成功 - ContactId: {contactId}, ContactName: {contact.NickName}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "删除联系人失败 - ContactId: {ContactId}, ContactName: {ContactName}", contactId, contact.NickName);
            throw new Exception($"删除联系人失败: {ex.Message}", ex);
        }
    }

    public async Task DeleteGroup(Guid userId, Guid groupId)
    {
        // 验证用户是否已登录
        WxMangerEntity? wxMangerEntity =
            await dbContext.WxMangerEntities.FirstOrDefaultAsync(item =>
                item.UserId == userId);

        if (wxMangerEntity == null)
        {
            throw new ArgumentException("用户未登录微信");
        }

        // 查找群组
        var group = await dbContext.WxGroupEntities
            .FirstOrDefaultAsync(g => g.Id == groupId && g.WxManagerId == wxMangerEntity.Id);

        if (group == null)
        {
            throw new ArgumentException("群组不存在");
        }

        try
        {
            // 删除群组记录
            dbContext.WxGroupEntities.Remove(group);
            await dbContext.SaveChangesAsync();

            // 清理缓存
            string cacheKey = string.Format(Keys.SWxGroup, wxMangerEntity.Id, group.ChatRoomId);
            await cacheService.RemoveAsync(cacheKey);

            // 发送缓存失效通知
            await notificationService.PublishCacheInvalidationAsync(cacheKey);
            await notificationService.PublishCacheInvalidationAsync($"ui_refresh:group:{groupId}");

            logger.LogInformation($"删除群组成功 - GroupId: {groupId}, GroupName: {group.NickName}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "删除群组失败 - GroupId: {GroupId}, GroupName: {GroupName}", groupId, group.NickName);
            throw new Exception($"删除群组失败: {ex.Message}", ex);
        }
    }

    public async Task<bool> IsContactExistsInDatabaseAsync(Guid wxManagerId, Guid contactId)
    {
        try
        {
            var exists = await dbContext.WxContactEntities
                .AnyAsync(c => c.Id == contactId && c.WxManagerId == wxManagerId);

            logger.LogDebug("联系人数据库存在性查询完成 - ContactId: {ContactId}, Exists: {Exists}", contactId, exists);
            return exists;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "联系人数据库存在性查询失败 - ContactId: {ContactId}", contactId);
            return true; // 查询失败时保守返回存在
        }
    }

    public async Task<bool> IsGroupExistsInDatabaseAsync(Guid wxManagerId, Guid groupId)
    {
        try
        {
            var exists = await dbContext.WxGroupEntities
                .AnyAsync(g => g.Id == groupId && g.WxManagerId == wxManagerId);

            logger.LogDebug("群组数据库存在性查询完成 - GroupId: {GroupId}, Exists: {Exists}", groupId, exists);
            return exists;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "群组数据库存在性查询失败 - GroupId: {GroupId}", groupId);
            return true; // 查询失败时保守返回存在
        }
    }

    public async Task<WxContactListSyncResultDto> InitAndSyncAddressList(Guid userId, WxInitAndSyncAddressListCommand initAndSyncAddressListCommand)
    {
        WxMangerEntity? wxMangerEntity =
            await dbContext.WxMangerEntities.FirstOrDefaultAsync(item =>
                item.UserId == userId);
        if (wxMangerEntity is null)
        {
            throw new ArgumentException("微信未登录");
        }

        // 直接从数据库获取wId，不从Redis获取
        if (string.IsNullOrEmpty(wxMangerEntity.WId))
        {
            throw new InvalidOperationException("微信未登录或会话已过期，请重新登录");
        }
        
        var wId = wxMangerEntity.WId;
        
        var wxLoginWIdKey = string.Format(Keys.SWxLoginWIdKeyPrefix, userId);
        // 更新缓存，缓存七天
        await cacheService.SetAsync(wxLoginWIdKey, wxMangerEntity.Id.ToString(), TimeSpan.FromDays(7));
        await cacheService.SetAsync(string.Format(Keys.SWxManagerDetail, wxMangerEntity.Id), wxMangerEntity, TimeSpan.FromDays(7));

        try
        {
            logger.LogInformation($"开始初始化并同步通讯录 - 微信管理器ID: {wxMangerEntity.Id}");
            
            // 步骤1：初始化通讯录
            await eYunWrapper.InitAddressListAsync(new EYunInitAddressListRequest() { WId = wId });
            logger.LogInformation("通讯录初始化完成");
            
            // 步骤2：获取通讯录列表数据
            var addressListData = await eYunWrapper.GetAddressListAsync(new EYunGetAddressListRequest() { WId = wId });
            logger.LogInformation($"获取通讯录列表完成 - 好友: {addressListData.Friends.Count}, 群聊: {addressListData.Chatrooms.Count}");
            
            // 步骤2.5：获取企业微信联系人列表数据（可选）
            List<string> enterpriseContactIds = new List<string>();
            try 
            {
                var imAddressListData = await eYunWrapper.GetImAddressListAsync(new EYunGetImAddressListRequest() { WId = wId });
                enterpriseContactIds = imAddressListData.Select(contact => contact.UserName).ToList();
                logger.LogInformation($"获取企业微信联系人列表完成 - 企业微信: {enterpriseContactIds.Count}");
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "获取企业微信联系人列表失败，继续处理其他联系人");
                // 企业微信联系人获取失败不影响整体同步流程
            }
            
            // 步骤3：同步通讯录列表数据到本地并收集统计信息
            var syncResult = await SyncAddressListAndCollectStats(wxMangerEntity.Id, addressListData, enterpriseContactIds);
            
            // 步骤4：更新通讯录列表缓存
            await UpdateContactListCache(wxMangerEntity.Id, addressListData, enterpriseContactIds);
            
            // 步骤5：清理联系人查询缓存（因为数据已更新）
            await ClearContactQueryCacheAsync(wxMangerEntity.Id);
            
            logger.LogInformation($"通讯录列表同步完成 - 微信管理器ID: {wxMangerEntity.Id}");
            
            // 返回统计结果
            return new WxContactListSyncResultDto
            {
                TotalFriends = addressListData.Friends.Count,
                TotalChatrooms = addressListData.Chatrooms.Count,
                TotalEnterprise = enterpriseContactIds.Count,
                NewFriends = syncResult.NewFriends,
                NewChatrooms = syncResult.NewChatrooms,
                NewEnterprise = syncResult.NewEnterprise,
                RemovedFriends = syncResult.RemovedFriends,
                RemovedChatrooms = syncResult.RemovedChatrooms,
                RemovedEnterprise = syncResult.RemovedEnterprise,
                SyncTime = DateTime.UtcNow,
                IsSuccess = true
            };
        }
        catch (HappyWechat.Infrastructure.EYun.Exceptions.EYunException ex)
        {
            // 检测是否是因为wId不存在或已失效引起的错误
            if (ex.Message.Contains("wId已注销") || ex.Message.Contains("二维码失效"))
            {
                logger.LogWarning($"微信wId已失效，将用户 {userId} 的微信状态设置为未登录");
                
                // 备份 WcId 到缓存，供重登录时使用
                if (!string.IsNullOrEmpty(wxMangerEntity.WcId))
                {
                    var accountWIdBackupKey = string.Format(Keys.SWxAccountWId, wxMangerEntity.Id);
                    await cacheService.SetAsync(accountWIdBackupKey, wxMangerEntity.WcId.Trim(), TimeSpan.FromDays(90)); // 备份90天
                    logger.LogInformation($"同步通讯录时wId失效，备份 WcId - 账户ID: {wxMangerEntity.Id}, WcId: {wxMangerEntity.WcId}");
                }
                
                // 清除缓存
                await cacheService.RemoveAsync(wxLoginWIdKey);
                
                // 设置微信状态为未登录
                wxMangerEntity.WxStatus = WxStatus.NotLogIn;
                wxMangerEntity.WId = string.Empty;
                wxMangerEntity.WcId = string.Empty; // 添加清空 WcId，保持一致性
                await dbContext.SaveChangesAsync();
                
                // 抛出异常以便上层处理
                throw new InvalidOperationException("微信已注销或二维码失效，请重新登录");
            }
            
            // 其他类型的EYun异常，直接向上传递
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"同步通讯录列表失败 - 微信管理器ID: {wxMangerEntity.Id}");
            
            return new WxContactListSyncResultDto
            {
                SyncTime = DateTime.UtcNow,
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<WxQrDto> GetFirstTimeLoginQrCodeAsync(Guid userId, WxFirstTimeLoginCommand command)
    {
        logger.LogInformation($"首次登录获取二维码 - 用户ID: {userId}");

        // 【修复】参数验证
        if (userId == Guid.Empty)
        {
            throw new ArgumentException("用户ID不能为空", nameof(userId));
        }
        
        if (command == null)
        {
            throw new ArgumentNullException(nameof(command), "登录命令不能为空");
        }

        try
        {
            // 检查用户是否已有登录记录（防止重复创建）
            var existingAccount = await dbContext.WxMangerEntities
                .FirstOrDefaultAsync(w => w.UserId == userId && w.WxStatus == WxStatus.AlreadyLogIn);
            
            if (existingAccount != null)
            {
                logger.LogWarning($"用户已有登录账户，不应进行首次登录 - 用户ID: {userId}, 账户ID: {existingAccount.Id}");
            }

            // 首次登录，wcId设置为空，但需要传递Proxy、DeviceType等参数
            var qrCodeQuery = new WxGetQrCodeQuery
            {
                WcId = string.Empty,
                Proxy = command.Proxy,
                DeviceType = command.DeviceType ?? "ipad", // 默认设备类型
                ProxyIp = command.ProxyIp,
                ProxyUser = command.ProxyUser,
                ProxyPassword = command.ProxyPassword
            };

            logger.LogInformation($"首次登录参数 - 代理: {command.Proxy}, 设备类型: {qrCodeQuery.DeviceType}");
            return await GetQrCodeAsync(userId.ToString(), qrCodeQuery);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"首次登录获取二维码失败 - 用户ID: {userId}");
            throw new InvalidOperationException($"获取首次登录二维码失败: {ex.Message}", ex);
        }
    }

    public async Task<WxQrDto> GetAccountLoginQrCodeAsync(Guid userId, WxAccountLoginCommand command)
    {
        logger.LogInformation($"账户重登录获取二维码 - 用户ID: {userId}, 账户ID: {command.AccountId}");

        // 【修复】参数验证
        if (userId == Guid.Empty)
        {
            throw new ArgumentException("用户ID不能为空", nameof(userId));
        }
        
        if (command.AccountId == Guid.Empty)
        {
            throw new ArgumentException("账户ID不能为空", nameof(command));
        }

        try
        {
            // 验证账户存在
            var wxAccount = await dbContext.WxMangerEntities
                .FirstOrDefaultAsync(w => w.Id == command.AccountId && w.UserId == userId);

            if (wxAccount == null)
            {
                logger.LogWarning($"账户不存在 - 用户ID: {userId}, 账户ID: {command.AccountId}");
                throw new ArgumentException($"账户不存在或不属于当前用户 - 账户ID: {command.AccountId}");
            }

            // 检查账户状态
            if (wxAccount.WxStatus == WxStatus.AlreadyLogIn)
            {
                logger.LogInformation($"账户已登录，无需重新登录 - 账户ID: {command.AccountId}");
            }

            // 【修复】优先从缓存获取WcId，如果缓存不存在则使用数据库中的WcId，并清理格式
            var wxLoginWIdKey = string.Format(Keys.SWxAccountWId, command.AccountId);
            var cachedWcId = await cacheService.GetAsync<string>(wxLoginWIdKey);
            var dbWcId = wxAccount.WcId?.Trim(); // 清理数据库中WcId的格式
            var wcId = !string.IsNullOrEmpty(cachedWcId?.Trim()) ? cachedWcId.Trim() : (dbWcId ?? string.Empty);

            logger.LogInformation($"账户重登录 - AccountId: {command.AccountId}, 缓存WcId: {cachedWcId?.Trim()}, 数据库WcId: {dbWcId}, 最终使用WcId: {wcId}");

            var qrCodeQuery = new WxGetQrCodeQuery
            {
                WcId = wcId,
                Proxy = command.Proxy,
                DeviceType = command.DeviceType,
                ProxyIp = command.ProxyIp,
                ProxyUser = command.ProxyUser,
                ProxyPassword = command.ProxyPassword
            };

            return await GetQrCodeAsync(userId.ToString(), qrCodeQuery);
        }
        catch (ArgumentException)
        {
            // 参数错误直接抛出
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"账户重登录获取二维码失败 - 用户ID: {userId}, 账户ID: {command.AccountId}");
            throw new InvalidOperationException($"获取账户登录二维码失败: {ex.Message}", ex);
        }
    }

    public async Task LogoutAccountAsync(Guid userId, Guid accountId)
    {
        logger.LogInformation($"开始账户登出操作 - 用户ID: {userId}, 账户ID: {accountId}");

        var wxAccount = await dbContext.WxMangerEntities
            .FirstOrDefaultAsync(w => w.Id == accountId && w.UserId == userId);

        if (wxAccount == null)
        {
            throw new ArgumentException("账户不存在");
        }

        // 1. 先调用EYun API进行真实登出（如果WcId存在）
        if (!string.IsNullOrEmpty(wxAccount.WcId))
        {
            try
            {
                logger.LogInformation($"调用EYun API单独下线微信账户 - WcId: {wxAccount.WcId}");
                
                var offlineRequest = new EYunOfflineRequest
                {
                    WcId = wxAccount.WcId
                };
                
                await eYunWrapper.OfflineAsync(offlineRequest);
                logger.LogInformation($"EYun API单独下线成功 - WcId: {wxAccount.WcId}");
            }
            catch (Exception ex)
            {
                // EYun API调用失败不应该阻止本地登出，但要记录错误
                logger.LogWarning(ex, $"EYun API登出失败，继续执行本地登出 - WcId: {wxAccount.WcId}, 错误: {ex.Message}");
                // 不抛出异常，继续执行本地清理
            }
        }
        else
        {
            logger.LogInformation($"WcId为空，跳过EYun API调用 - 账户ID: {accountId}");
        }

        // 【修复】更新账户状态，但保留WcId不清空
        wxAccount.WxStatus = WxStatus.NotLogIn;
        wxAccount.WId = string.Empty; // 只清空WId（会话标识符）
        // 不清空WcId，保留在数据库中供重登录使用
        logger.LogInformation($"登出时保留WcId在数据库中 - 账户ID: {accountId}, WcId: {wxAccount.WcId}");
        
        // 同时更新缓存中的WcId以保持一致性
        if (!string.IsNullOrEmpty(wxAccount.WcId))
        {
            var accountWIdKey = string.Format(Keys.SWxAccountWId, accountId);
            await cacheService.SetAsync(accountWIdKey, wxAccount.WcId.Trim(), TimeSpan.FromDays(90));
        }
        wxAccount.UpdatedTime = DateTime.UtcNow;
        await dbContext.SaveChangesAsync();

        // 清除相关缓存（但保留WcId缓存）
        var accountLoginStatusKey = string.Format(Keys.SWxAccountLoginStatus, accountId);
        var userLoginWIdKey = string.Format(Keys.SWxLoginWIdKeyPrefix, userId);
        
        // 注意：不清除 accountWIdKey 缓存，因为它包含了用于重登录的 WcId 备份
        await cacheService.RemoveAsync(accountLoginStatusKey);
        await cacheService.RemoveAsync(userLoginWIdKey);

        // 4. 清除联系人相关缓存
        try
        {
            await ClearContactQueryCacheAsync(accountId);
            logger.LogInformation($"联系人缓存清理完成 - 账户ID: {accountId}");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, $"联系人缓存清理失败 - 账户ID: {accountId}");
        }

        logger.LogInformation($"账户登出操作完成 - 账户ID: {accountId}");
    }

    public async Task DeleteAccountAsync(Guid userId, Guid accountId)
    {
        logger.LogInformation($"开始删除账户操作 - 用户ID: {userId}, 账户ID: {accountId}");

        var wxAccount = await dbContext.WxMangerEntities
            .FirstOrDefaultAsync(w => w.Id == accountId && w.UserId == userId);

        if (wxAccount == null)
        {
            throw new ArgumentException("账户不存在");
        }

        try
        {
            // 删除微信账户记录
            dbContext.WxMangerEntities.Remove(wxAccount);
            await dbContext.SaveChangesAsync();

            // 清除相关缓存
            var accountWIdKey = string.Format(Keys.SWxAccountWId, accountId);
            var accountLoginStatusKey = string.Format(Keys.SWxAccountLoginStatus, accountId);
            var managerDetailKey = string.Format(Keys.SWxManagerDetail, accountId);
            var userAccountListKey = string.Format(Keys.SWxUserAccountList, userId);

            await cacheService.RemoveAsync(accountWIdKey);
            await cacheService.RemoveAsync(accountLoginStatusKey);
            await cacheService.RemoveAsync(managerDetailKey);
            await cacheService.RemoveAsync(userAccountListKey);

            logger.LogInformation($"账户删除操作完成 - 账户ID: {accountId}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "删除账户失败 - 账户ID: {AccountId}", accountId);
            throw new Exception($"删除账户失败: {ex.Message}", ex);
        }
    }

    private async Task<SyncStatistics> SyncAddressListAndCollectStats(Guid wxManagerId, EYunGetAddressListData addressListData, List<string> enterpriseContactIds)
    {
        var stats = new SyncStatistics();
        
        try
        {
            // 同步好友列表
            var friendsSyncResult = await contactListRepository.SyncContactListAsync(wxManagerId, WxContactListType.Friends, addressListData.Friends);
            stats.NewFriends = friendsSyncResult.NewCount;
            stats.RemovedFriends = friendsSyncResult.RemovedCount;
            
            // 同步群聊列表
            var chatroomsSyncResult = await contactListRepository.SyncContactListAsync(wxManagerId, WxContactListType.Chatrooms, addressListData.Chatrooms);
            stats.NewChatrooms = chatroomsSyncResult.NewCount;
            stats.RemovedChatrooms = chatroomsSyncResult.RemovedCount;
            
            // 同步企业微信联系人列表
            var enterpriseSyncResult = await contactListRepository.SyncContactListAsync(wxManagerId, WxContactListType.Enterprise, enterpriseContactIds);
            stats.NewEnterprise = enterpriseSyncResult.NewCount;
            stats.RemovedEnterprise = enterpriseSyncResult.RemovedCount;
            
            logger.LogInformation($"通讯录列表同步完成 - 微信管理器ID: {wxManagerId}, 统计: {System.Text.Json.JsonSerializer.Serialize(stats)}");
            
            return stats;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"同步通讯录列表失败 - 微信管理器ID: {wxManagerId}");
            throw;
        }
    }
    
    private async Task UpdateContactListCache(Guid wxManagerId, EYunGetAddressListData addressListData, List<string> enterpriseContactIds)
    {
        try
        {
            // 缓存各个类型的联系人列表
            await cacheService.SetAsync(string.Format(Keys.SWxContactList, wxManagerId, (int)WxContactListType.Friends),
                addressListData.Friends, TimeSpan.FromDays(7));
            await cacheService.SetAsync(string.Format(Keys.SWxContactList, wxManagerId, (int)WxContactListType.Chatrooms),
                addressListData.Chatrooms, TimeSpan.FromDays(7));
            await cacheService.SetAsync(string.Format(Keys.SWxContactList, wxManagerId, (int)WxContactListType.Enterprise),
                enterpriseContactIds, TimeSpan.FromDays(7));

            // 缓存统计信息
            var counts = new Dictionary<WxContactListType, int>
            {
                { WxContactListType.Friends, addressListData.Friends.Count },
                { WxContactListType.Chatrooms, addressListData.Chatrooms.Count },
                { WxContactListType.Enterprise, enterpriseContactIds.Count }
            };
            await cacheService.SetAsync(string.Format(Keys.SWxContactListCounts, wxManagerId), counts, TimeSpan.FromDays(7));
            
            logger.LogInformation($"通讯录列表缓存更新完成 - 微信管理器ID: {wxManagerId}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"更新通讯录列表缓存失败 - 微信管理器ID: {wxManagerId}");
        }
    }

}

public class SyncStatistics
{
    public int NewFriends { get; set; }
    public int RemovedFriends { get; set; }
    public int NewChatrooms { get; set; }
    public int RemovedChatrooms { get; set; }
    public int NewEnterprise { get; set; }
    public int RemovedEnterprise { get; set; }
}
