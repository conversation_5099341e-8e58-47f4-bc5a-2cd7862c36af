using System.Text.Json;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers;

/// <summary>
/// 简化延时消息处理器 - 处理延时队列中到期的消息
/// 将到期的延时消息重新放入对应的普通队列中
/// </summary>
public class SimplifiedDelayedMessageProcessor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SimplifiedDelayedMessageProcessor> _logger;
    private readonly int _checkIntervalMs;
    private readonly int _batchSize;

    public SimplifiedDelayedMessageProcessor(
        IServiceProvider serviceProvider,
        ILogger<SimplifiedDelayedMessageProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _checkIntervalMs = 5000; // 每5秒检查一次
        _batchSize = 50; // 每批处理50条消息
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // 🔧 注释冗余的延时消息处理器启动日志 - 减少日志噪音
        // _logger.LogInformation("⏰ 延时消息处理器已启动");

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await ProcessDelayedMessagesAsync(stoppingToken);
                await Task.Delay(_checkIntervalMs, stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("🛑 延时消息处理器已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 延时消息处理器异常");
        }
    }

    /// <summary>
    /// 处理所有延时消息
    /// </summary>
    private async Task ProcessDelayedMessagesAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var queueService = scope.ServiceProvider.GetRequiredService<ISimplifiedQueueService>();
            
            // 获取所有延时队列
            var allQueueNames = await queueService.GetAllQueueNamesAsync(cancellationToken);
            var delayedQueues = allQueueNames
                .Where(name => name.Contains(":delayed:"))
                .ToList();

            if (delayedQueues.Count == 0)
            {
                return;
            }

            _logger.LogDebug("🔍 检查 {Count} 个延时队列", delayedQueues.Count);

            // 并发处理所有延时队列
            var tasks = delayedQueues.Select(queueName => ProcessSingleDelayedQueueAsync(queueName, queueService, cancellationToken));
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理延时消息失败");
        }
    }

    /// <summary>
    /// 处理单个延时队列
    /// </summary>
    private async Task ProcessSingleDelayedQueueAsync(string delayedQueueName, ISimplifiedQueueService queueService, CancellationToken cancellationToken)
    {
        try
        {
            // 从队列名称中提取 WxManagerId
            var parts = delayedQueueName.Split(':');
            if (parts.Length < 3 || !Guid.TryParse(parts[2], out var wxManagerId))
            {
                return;
            }

            var database = queueService.GetDatabase();
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            // 获取所有到期的延时消息 (score <= currentTime)
            var expiredMessages = await database.SortedSetRangeByScoreAsync(
                delayedQueueName, 
                0, 
                currentTime, 
                take: _batchSize);

            if (expiredMessages.Length == 0)
            {
                return;
            }

            _logger.LogDebug("⏰ 发现 {Count} 条到期延时消息 - WxManagerId: {WxManagerId}", 
                expiredMessages.Length, wxManagerId);

            // 处理每条到期的消息
            foreach (var messageJson in expiredMessages)
            {
                try
                {
                    await ProcessExpiredMessageAsync(messageJson, wxManagerId, queueService, delayedQueueName, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 处理到期消息失败 - Message: {Message}", messageJson.ToString().Substring(0, Math.Min(100, messageJson.ToString().Length)));
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理延时队列失败 - QueueName: {QueueName}", delayedQueueName);
        }
    }

    /// <summary>
    /// 处理单条到期的延时消息
    /// </summary>
    private async Task ProcessExpiredMessageAsync(string messageJson, Guid wxManagerId, ISimplifiedQueueService queueService, string delayedQueueName, CancellationToken cancellationToken)
    {
        try
        {
            // 尝试解析为通用消息格式
            using var jsonDoc = JsonDocument.Parse(messageJson);
            var root = jsonDoc.RootElement;
            
            if (!root.TryGetProperty("queueType", out var queueTypeElement))
            {
                _logger.LogWarning("⚠️ 延时消息缺少queueType属性");
                return;
            }

            var queueType = queueTypeElement.GetString();
            if (string.IsNullOrEmpty(queueType))
            {
                _logger.LogWarning("⚠️ 延时消息queueType为空");
                return;
            }

            var messageId = root.TryGetProperty("id", out var idElement) ? idElement.GetString() : "unknown";
            
            _logger.LogDebug("⏰ 处理到期延时消息 - MessageId: {MessageId}, QueueType: {QueueType}", messageId, queueType);

            // 将消息移动到对应的普通队列
            var targetQueueName = queueService.GenerateQueueName(wxManagerId, queueType);
            var database = queueService.GetDatabase();
            
            // 使用事务确保原子性操作
            var transaction = database.CreateTransaction();
            
            // 从延时队列中移除消息
            var removeTask = transaction.SortedSetRemoveAsync(delayedQueueName, messageJson);
            
            // 将消息添加到目标队列（根据优先级决定位置）
            var priority = root.TryGetProperty("priority", out var priorityElement) ? priorityElement.GetInt32() : 0;
            Task addTask;
            
            if (priority > 0)
            {
                addTask = transaction.ListLeftPushAsync(targetQueueName, messageJson); // 高优先级从左边入队
            }
            else
            {
                addTask = transaction.ListRightPushAsync(targetQueueName, messageJson); // 普通优先级从右边入队
            }
            
            // 执行事务
            var success = await transaction.ExecuteAsync();
            
            if (success)
            {
                _logger.LogDebug("✅ 延时消息已移动到目标队列 - MessageId: {MessageId}, QueueType: {QueueType}", messageId, queueType);
            }
            else
            {
                _logger.LogWarning("⚠️ 延时消息移动事务失败 - MessageId: {MessageId}", messageId);
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "❌ 解析延时消息JSON失败");
            
            // 移除无效的消息
            var database = queueService.GetDatabase();
            await database.SortedSetRemoveAsync(delayedQueueName, messageJson);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理到期延时消息异常");
        }
    }
}