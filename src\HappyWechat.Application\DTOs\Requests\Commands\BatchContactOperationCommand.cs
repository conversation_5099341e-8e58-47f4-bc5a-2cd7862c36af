using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 批量联系人操作命令
/// </summary>
public class BatchContactOperationCommand
{
    /// <summary>
    /// 联系人ID列表
    /// </summary>
    public List<Guid> ContactIds { get; set; } = new();
    
    /// <summary>
    /// 操作类型
    /// </summary>
    public BatchOperationType OperationType { get; set; }
    
    /// <summary>
    /// 操作值（用于设置自动回复状态等）
    /// </summary>
    public object? OperationValue { get; set; }
    
    /// <summary>
    /// 操作参数（用于复杂操作）
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 批量操作类型
/// </summary>
public enum BatchOperationType
{
    /// <summary>
    /// 设置自动回复状态
    /// </summary>
    SetAutoReply,
    
    /// <summary>
    /// 批量删除
    /// </summary>
    Delete,
    
    /// <summary>
    /// 批量更新备注
    /// </summary>
    UpdateRemark,
    
    /// <summary>
    /// 批量添加标签
    /// </summary>
    AddTags,
    
    /// <summary>
    /// 批量移除标签
    /// </summary>
    RemoveTags,
    
    /// <summary>
    /// 批量导出
    /// </summary>
    Export,
    
    /// <summary>
    /// 批量同步
    /// </summary>
    Sync
}

/// <summary>
/// 批量操作结果
/// </summary>
public class BatchOperationResult
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }
    
    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }
    
    /// <summary>
    /// 失败详情
    /// </summary>
    public List<BatchOperationError> Errors { get; set; } = new();
    
    /// <summary>
    /// 操作结果数据
    /// </summary>
    public object? ResultData { get; set; }
}

/// <summary>
/// 批量操作错误
/// </summary>
public class BatchOperationError
{
    /// <summary>
    /// 联系人ID
    /// </summary>
    public Guid ContactId { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }
}
