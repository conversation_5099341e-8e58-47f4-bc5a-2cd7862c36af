using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Requests.Commands;

/// <summary>
/// 导入联系人命令
/// </summary>
public class ImportContactsCommand
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 导入文件路径或内容
    /// </summary>
    public string FileContent { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件类型
    /// </summary>
    public ImportFileType FileType { get; set; }
    
    /// <summary>
    /// 是否覆盖现有联系人
    /// </summary>
    public bool OverwriteExisting { get; set; } = false;
    
    /// <summary>
    /// 导入选项
    /// </summary>
    public ImportOptions Options { get; set; } = new();
}

/// <summary>
/// 导出联系人命令
/// </summary>
public class ExportContactsCommand
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }
    
    /// <summary>
    /// 联系人ID列表（为空则导出全部）
    /// </summary>
    public List<Guid> ContactIds { get; set; } = new();
    
    /// <summary>
    /// 导出格式
    /// </summary>
    public ExportFormat Format { get; set; }
    
    /// <summary>
    /// 导出字段
    /// </summary>
    public List<string> Fields { get; set; } = new();
    
    /// <summary>
    /// 筛选条件
    /// </summary>
    public ExportFilter Filter { get; set; } = new();
}

/// <summary>
/// 导入文件类型
/// </summary>
public enum ImportFileType
{
    /// <summary>
    /// CSV文件
    /// </summary>
    Csv,
    
    /// <summary>
    /// Excel文件
    /// </summary>
    Excel,
    
    /// <summary>
    /// JSON文件
    /// </summary>
    Json,
    
    /// <summary>
    /// 文本文件
    /// </summary>
    Text
}

/// <summary>
/// 导出格式
/// </summary>
public enum ExportFormat
{
    /// <summary>
    /// CSV格式
    /// </summary>
    Csv,
    
    /// <summary>
    /// Excel格式
    /// </summary>
    Excel,
    
    /// <summary>
    /// JSON格式
    /// </summary>
    Json,
    
    /// <summary>
    /// PDF格式
    /// </summary>
    Pdf
}

/// <summary>
/// 导入选项
/// </summary>
public class ImportOptions
{
    /// <summary>
    /// 跳过重复项
    /// </summary>
    public bool SkipDuplicates { get; set; } = true;
    
    /// <summary>
    /// 验证数据格式
    /// </summary>
    public bool ValidateData { get; set; } = true;
    
    /// <summary>
    /// 批量大小
    /// </summary>
    public int BatchSize { get; set; } = 100;
    
    /// <summary>
    /// 字段映射
    /// </summary>
    public Dictionary<string, string> FieldMapping { get; set; } = new();
}

/// <summary>
/// 导出筛选条件
/// </summary>
public class ExportFilter
{
    /// <summary>
    /// 联系人类型
    /// </summary>
    public List<WxContactListType> ContactTypes { get; set; } = new();
    
    /// <summary>
    /// 是否启用自动回复
    /// </summary>
    public bool? AutoReplyEnabled { get; set; }
    
    /// <summary>
    /// 创建时间范围
    /// </summary>
    public DateTimeRange? CreatedDateRange { get; set; }
    
    /// <summary>
    /// 更新时间范围
    /// </summary>
    public DateTimeRange? UpdatedDateRange { get; set; }
    
    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string? SearchKeyword { get; set; }
}

/// <summary>
/// 时间范围
/// </summary>
public class DateTimeRange
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartDate { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndDate { get; set; }
}

/// <summary>
/// 导入结果
/// </summary>
public class ImportResult
{
    /// <summary>
    /// 总行数
    /// </summary>
    public int TotalRows { get; set; }
    
    /// <summary>
    /// 成功导入数量
    /// </summary>
    public int SuccessCount { get; set; }
    
    /// <summary>
    /// 跳过数量
    /// </summary>
    public int SkippedCount { get; set; }
    
    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }
    
    /// <summary>
    /// 错误详情
    /// </summary>
    public List<ImportError> Errors { get; set; } = new();
}

/// <summary>
/// 导入错误
/// </summary>
public class ImportError
{
    /// <summary>
    /// 行号
    /// </summary>
    public int RowNumber { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 原始数据
    /// </summary>
    public string? RawData { get; set; }
}

/// <summary>
/// 导出结果
/// </summary>
public class ExportResult
{
    /// <summary>
    /// 文件路径或内容
    /// </summary>
    public string FileContent { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;
    
    /// <summary>
    /// 内容类型
    /// </summary>
    public string ContentType { get; set; } = string.Empty;
    
    /// <summary>
    /// 导出数量
    /// </summary>
    public int ExportedCount { get; set; }
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }
}
