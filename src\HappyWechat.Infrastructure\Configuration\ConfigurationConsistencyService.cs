using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.Notifications;
using HappyWechat.Infrastructure.Notifications.Models;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.Configuration;

/// <summary>
/// 配置一致性保证服务 - 确保配置变更的强一致性
/// </summary>
public interface IConfigurationConsistencyService
{
    /// <summary>
    /// 确保配置变更的强一致性
    /// </summary>
    Task<bool> EnsureConfigurationConsistencyAsync(string configType, string configKey, object newValue, List<string>? affectedWcIds = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证配置一致性
    /// </summary>
    Task<bool> ValidateConfigurationConsistencyAsync(string configType, string configKey, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取配置版本
    /// </summary>
    Task<long> GetConfigurationVersionAsync(string configType, string configKey);
}

/// <summary>
/// 配置一致性保证服务实现
/// </summary>
public class ConfigurationConsistencyService : IConfigurationConsistencyService
{
    private readonly ILogger<ConfigurationConsistencyService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IUnifiedRedisNotificationService _notificationService;
    private readonly ConcurrentDictionary<string, long> _configVersions = new();
    private readonly ConcurrentDictionary<string, DateTime> _lastUpdateTimes = new();
    
    // 配置一致性检查超时时间
    private static readonly TimeSpan CONSISTENCY_CHECK_TIMEOUT = TimeSpan.FromSeconds(15);
    private static readonly TimeSpan MAX_CONSISTENCY_WAIT_TIME = TimeSpan.FromSeconds(20);

    public ConfigurationConsistencyService(
        ILogger<ConfigurationConsistencyService> logger,
        IServiceProvider serviceProvider,
        IUnifiedRedisNotificationService notificationService)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _notificationService = notificationService;
    }

    public async Task<bool> EnsureConfigurationConsistencyAsync(
        string configType, 
        string configKey, 
        object newValue, 
        List<string>? affectedWcIds = null, 
        CancellationToken cancellationToken = default)
    {
        var consistencyId = Guid.NewGuid().ToString("N")[..8];
        var configId = $"{configType}.{configKey}";
        
        try
        {
            // 🔧 注释冗余的配置一致性开始日志 - 减少日志噪音，每次配置更新都会重复出现
            // _logger.LogInformation("🔒 [一致性:{ConsistencyId}] 开始配置一致性保证 - {ConfigId}",
            //     consistencyId, configId);

            // 1. 生成新的配置版本
            var newVersion = GenerateNewVersion(configId);
            
            // 2. 记录配置变更开始时间
            _lastUpdateTimes[configId] = DateTime.UtcNow;
            
            // 3. 发送配置变更通知（带版本信息）
            var configNotification = new ConfigNotificationMessage
            {
                ConfigType = configType,
                ConfigKey = configKey,
                NewValue = newValue?.ToString(),
                Operation = ConfigChangeOperation.Updated,
                AffectedWcIds = affectedWcIds,
                Priority = 9, // 最高优先级
                RequirePersistence = true,
                ExtendedData = new Dictionary<string, object>
                {
                    ["ConfigVersion"] = newVersion,
                    ["ConsistencyId"] = consistencyId,
                    ["RequireAcknowledgment"] = true
                }
            };

            await _notificationService.PublishRealtimeNotificationAsync("config_changes", "ConfigurationChanged", configNotification);
            
            // 4. 等待配置生效确认（最多20秒）
            var isConsistent = await WaitForConsistencyConfirmationAsync(configId, newVersion, cancellationToken);
            
            if (isConsistent)
            {
                // 🔧 注释冗余的配置一致性成功日志 - 减少日志噪音，每次配置更新都会重复出现
                // _logger.LogInformation("✅ [一致性:{ConsistencyId}] 配置一致性保证成功 - {ConfigId}, 版本: {Version}",
                //     consistencyId, configId, newVersion);
                return true;
            }
            else
            {
                _logger.LogWarning("⚠️ [一致性:{ConsistencyId}] 配置一致性保证超时 - {ConfigId}, 版本: {Version}", 
                    consistencyId, configId, newVersion);
                
                // 超时但不失败，因为配置最终会生效
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ [一致性:{ConsistencyId}] 配置一致性保证失败 - {ConfigId}", 
                consistencyId, configId);
            return false;
        }
    }

    public async Task<bool> ValidateConfigurationConsistencyAsync(string configType, string configKey, CancellationToken cancellationToken = default)
    {
        var configId = $"{configType}.{configKey}";
        
        try
        {
            // 检查配置是否在合理时间内更新
            if (_lastUpdateTimes.TryGetValue(configId, out var lastUpdate))
            {
                var timeSinceUpdate = DateTime.UtcNow - lastUpdate;
                if (timeSinceUpdate > MAX_CONSISTENCY_WAIT_TIME)
                {
                    _logger.LogWarning("⚠️ 配置一致性验证：配置更新时间过长 - {ConfigId}, 耗时: {ElapsedMs}ms", 
                        configId, timeSinceUpdate.TotalMilliseconds);
                    return false;
                }
            }

            // 这里可以添加更多的一致性检查逻辑
            // 例如：检查缓存与数据库的一致性、检查Actor系统的配置状态等
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 配置一致性验证失败 - {ConfigId}", configId);
            return false;
        }
    }

    public async Task<long> GetConfigurationVersionAsync(string configType, string configKey)
    {
        var configId = $"{configType}.{configKey}";
        return _configVersions.GetOrAdd(configId, _ => DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
    }

    /// <summary>
    /// 生成新的配置版本号
    /// </summary>
    private long GenerateNewVersion(string configId)
    {
        var newVersion = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        _configVersions.AddOrUpdate(configId, newVersion, (_, _) => newVersion);
        return newVersion;
    }

    /// <summary>
    /// 等待配置一致性确认
    /// </summary>
    private async Task<bool> WaitForConsistencyConfirmationAsync(string configId, long expectedVersion, CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        var checkInterval = TimeSpan.FromMilliseconds(500); // 每500ms检查一次
        
        while (DateTime.UtcNow - startTime < CONSISTENCY_CHECK_TIMEOUT)
        {
            try
            {
                // 检查配置是否已经生效
                // 这里可以通过多种方式验证：
                // 1. 检查缓存是否已更新
                // 2. 检查Actor系统是否已收到通知
                // 3. 检查消息处理器是否使用了新配置
                
                var currentVersion = await GetConfigurationVersionAsync(configId.Split('.')[0], configId.Split('.')[1]);
                if (currentVersion >= expectedVersion)
                {
                    _logger.LogDebug("✅ 配置一致性确认成功 - {ConfigId}, 版本: {Version}", configId, expectedVersion);
                    return true;
                }
                
                await Task.Delay(checkInterval, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogDebug("🚫 配置一致性确认被取消 - {ConfigId}", configId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ 配置一致性确认检查异常 - {ConfigId}", configId);
                await Task.Delay(checkInterval, cancellationToken);
            }
        }
        
        _logger.LogWarning("⏰ 配置一致性确认超时 - {ConfigId}, 版本: {Version}", configId, expectedVersion);
        return false;
    }
}
