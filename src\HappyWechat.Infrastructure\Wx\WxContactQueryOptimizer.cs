using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.Commons;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Domain.Entities;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Database;
using HappyWechat.Infrastructure.Extensions;
using System.Linq.Expressions;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 联系人查询优化器 - 提供高性能的联系人查询和批量操作
/// </summary>
public interface IWxContactQueryOptimizer
{
    /// <summary>
    /// 优化的联系人列表查询
    /// </summary>
    Task<PageResponse<WxContactDto>> GetOptimizedContactListAsync(
        Guid wxManagerId,
        WxContactType contactType,
        string? searchNickName = null,
        string? searchRemarkName = null,
        int page = 1,
        int pageSize = 20);

    /// <summary>
    /// 优化的联系人列表查询 - 支持多个联系人类型
    /// </summary>
    Task<PageResponse<WxContactDto>> GetOptimizedContactListAsync(
        Guid wxManagerId,
        List<WxContactType> contactTypes,
        string? searchNickName = null,
        string? searchRemarkName = null,
        int page = 1,
        int pageSize = 20);

    /// <summary>
    /// 批量获取联系人AI配置
    /// </summary>
    Task<Dictionary<Guid, ContactAiConfigInfo>> GetBatchContactAiConfigsAsync(List<Guid> contactIds);

    /// <summary>
    /// 预加载联系人数据
    /// </summary>
    Task PreloadContactDataAsync(Guid wxManagerId, WxContactType contactType);

    /// <summary>
    /// 预加载联系人数据 - 支持多个联系人类型
    /// </summary>
    Task PreloadContactDataAsync(Guid wxManagerId, List<WxContactType> contactTypes);

    /// <summary>
    /// 获取联系人统计信息
    /// </summary>
    Task<ContactStatistics> GetContactStatisticsAsync(Guid wxManagerId);
}

public class ContactAiConfigInfo
{
    public Guid? AiAgentId { get; set; }
    public bool IsAiEnabled { get; set; }
    public string? AiAgentName { get; set; }
    public string? AiAgentDescription { get; set; }
}

public class ContactStatistics
{
    public int TotalContacts { get; set; }
    public int TotalGroups { get; set; }
    public int AiEnabledContacts { get; set; }
    public int AiEnabledGroups { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class WxContactQueryOptimizer : IWxContactQueryOptimizer
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<WxContactQueryOptimizer> _logger;
    private readonly IQueryOptimizer _queryOptimizer;

    // 🔥 添加并发控制，防止DbContext并发访问
    private readonly SemaphoreSlim _dbAccessSemaphore = new(1, 1);

    public WxContactQueryOptimizer(
        ApplicationDbContext dbContext,
        ILogger<WxContactQueryOptimizer> logger,
        IQueryOptimizer queryOptimizer)
    {
        _dbContext = dbContext;
        _logger = logger;
        _queryOptimizer = queryOptimizer;
    }

    public async Task<PageResponse<WxContactDto>> GetOptimizedContactListAsync(
        Guid wxManagerId,
        WxContactType contactType,
        string? searchNickName = null,
        string? searchRemarkName = null,
        int page = 1,
        int pageSize = 20)
    {
        // 调用多类型版本的方法，保持向后兼容
        return await GetOptimizedContactListAsync(wxManagerId, new List<WxContactType> { contactType }, searchNickName, searchRemarkName, page, pageSize);
    }

    public async Task<PageResponse<WxContactDto>> GetOptimizedContactListAsync(
        Guid wxManagerId,
        List<WxContactType> contactTypes,
        string? searchNickName = null,
        string? searchRemarkName = null,
        int page = 1,
        int pageSize = 20)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // 🔥 添加并发控制，防止DbContext并发访问
        await _dbAccessSemaphore.WaitAsync();
        try
        {
            // 🔧 注释冗余的联系人查询开始日志 - 降低日志噪音（保留性能监控）
            // _logger.LogInformation("开始联系人查询 - ManagerId: {ManagerId}, Types: {Types}, Page: {Page}, PageSize: {PageSize}",
            //     wxManagerId, string.Join(",", contactTypes), page, pageSize);

            // 构建统一查询对象 - 确保Count和数据查询使用相同的条件
            // 使用OR条件替代Contains，避免EF Core MySQL提供程序的限制
            var query = _dbContext.WxContactEntities
                .AsNoTracking() // 性能优化：不跟踪实体变化
                .Where(w => w.WxManagerId == wxManagerId);

            // 根据ContactType数量构建不同的查询条件
            if (contactTypes.Count == 1)
            {
                query = query.Where(w => w.ContactType == contactTypes[0]);
            }
            else if (contactTypes.Count == 2)
            {
                var type1 = contactTypes[0];
                var type2 = contactTypes[1];
                query = query.Where(w => w.ContactType == type1 || w.ContactType == type2);
            }
            else if (contactTypes.Count == 3)
            {
                var type1 = contactTypes[0];
                var type2 = contactTypes[1];
                var type3 = contactTypes[2];
                query = query.Where(w => w.ContactType == type1 || w.ContactType == type2 || w.ContactType == type3);
            }
            else if (contactTypes.Count == 4)
            {
                var type1 = contactTypes[0];
                var type2 = contactTypes[1];
                var type3 = contactTypes[2];
                var type4 = contactTypes[3];
                query = query.Where(w => w.ContactType == type1 || w.ContactType == type2 || w.ContactType == type3 || w.ContactType == type4);
            }
            else if (contactTypes.Count > 4)
            {
                // 对于超过4个类型的情况，使用客户端评估
                var contactTypeValues = contactTypes.Select(ct => (int)ct).ToList();
                query = query.Where(w => contactTypeValues.Contains((int)w.ContactType));
            }

            // 应用搜索条件
            if (!string.IsNullOrWhiteSpace(searchNickName))
            {
                query = query.Where(w => w.NickName != null && w.NickName.Contains(searchNickName));
            }

            if (!string.IsNullOrWhiteSpace(searchRemarkName))
            {
                query = query.Where(w => w.Remark != null && w.Remark.Contains(searchRemarkName));
            }

            // 添加排序以确保分页结果的一致性
            query = query.OrderBy(w => w.NickName).ThenBy(w => w.Id);

            _logger.LogDebug("查询条件构建完成 - ManagerId: {ManagerId}, 联系人类型: {ContactTypes}, 搜索昵称: {SearchNickName}, 搜索备注: {SearchRemarkName}",
                wxManagerId, string.Join(",", contactTypes), searchNickName ?? "无", searchRemarkName ?? "无");

            // 获取总数 - 使用相同的查询对象确保一致性
            var totalCount = await query.CountAsync();
            _logger.LogDebug("总数查询完成 - TotalCount: {TotalCount}", totalCount);

            // 获取分页数据 - 使用相同的查询对象
            // 🔧 修复：使用已排序的查询进行分页，确保结果一致性
            var includedQuery = query.Include(w => w.AiAgent); // AiAgent仅用于展示，不影响数据过滤
            var pagedQuery = includedQuery.ApplyPagination(page, pageSize);

            var contactEntities = await pagedQuery.ToListAsync();
            _logger.LogDebug("分页数据查询完成 - 实际返回: {ActualCount} 条记录", contactEntities.Count);

            // 转换为DTO
            var contacts = contactEntities.Select(w => new WxContactDto
            {
                Id = w.Id,
                WxManagerId = w.WxManagerId,
                WcId = w.WcId,
                ContactType = w.ContactType,
                UserName = w.UserName ?? string.Empty,
                NickName = w.NickName ?? string.Empty,
                Remark = w.Remark ?? string.Empty,
                Signature = w.Signature ?? string.Empty,
                Sex = w.Sex ?? 0,
                AliasName = w.AliasName ?? string.Empty,
                Country = w.Country ?? string.Empty,
                Province = w.Province ?? string.Empty,
                City = w.City ?? string.Empty,
                BigHead = w.BigHead ?? string.Empty,
                SmallHead = w.SmallHead ?? string.Empty,
                LabelList = w.LabelList ?? string.Empty,
                V1 = w.V1 ?? string.Empty,
                AutoReplyEnabled = w.IsAiEnabled
            }).ToList();

            // 计算分页信息
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            return new PageResponse<WxContactDto>
            {
                Items = contacts,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = totalPages,
                HasNextPage = page < totalPages,
                HasPreviousPage = page > 1
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "联系人查询失败 - ManagerId: {ManagerId}, Types: {Types}, Page: {Page}, PageSize: {PageSize}",
                wxManagerId, string.Join(",", contactTypes), page, pageSize);
            throw;
        }
        finally
        {
            // 🔥 释放并发控制信号量
            _dbAccessSemaphore.Release();
        }
    }

    public async Task<Dictionary<Guid, ContactAiConfigInfo>> GetBatchContactAiConfigsAsync(List<Guid> contactIds)
    {
        if (!contactIds.Any())
        {
            return new Dictionary<Guid, ContactAiConfigInfo>();
        }

        // 🔥 添加并发控制，防止DbContext并发访问
        await _dbAccessSemaphore.WaitAsync();
        try
        {
            _logger.LogInformation("批量获取联系人AI配置 - 联系人数量: {Count}", contactIds.Count);

            // 使用查询优化器进行批量查询
            var configs = await _queryOptimizer.BatchQueryAsync(
                _dbContext,
                contactIds,
                (WxContactEntity w) => w.Id,
                w => w.AiAgent);

            var result = configs.ToDictionary(
                w => w.Id,
                w => new ContactAiConfigInfo
                {
                    AiAgentId = w.AiAgentId,
                    IsAiEnabled = w.IsAiEnabled,
                    AiAgentName = w.AiAgent?.Name,
                    AiAgentDescription = w.AiAgent?.Description
                });

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量获取联系人AI配置失败");
            return new Dictionary<Guid, ContactAiConfigInfo>();
        }
        finally
        {
            // 🔥 释放并发控制信号量
            _dbAccessSemaphore.Release();
        }
    }

    public async Task PreloadContactDataAsync(Guid wxManagerId, WxContactType contactType)
    {
        // 调用多类型版本的方法，保持向后兼容
        await PreloadContactDataAsync(wxManagerId, new List<WxContactType> { contactType });
    }

    public async Task PreloadContactDataAsync(Guid wxManagerId, List<WxContactType> contactTypes)
    {
        // 🔥 添加并发控制，防止DbContext并发访问
        await _dbAccessSemaphore.WaitAsync();
        try
        {
            _logger.LogInformation("开始预加载联系人数据 - ManagerId: {ManagerId}, Types: {Types}", wxManagerId, string.Join(",", contactTypes));

            // 预加载基础联系人数据
            var contacts = await _dbContext.WxContactEntities
                .Where(w => w.WxManagerId == wxManagerId && contactTypes.Contains(w.ContactType))
                .Include(w => w.AiAgent)
                .ToListAsync();

            _logger.LogInformation("联系人数据预加载完成 - 加载 {Count} 条记录", contacts.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预加载联系人数据失败 - ManagerId: {ManagerId}, Types: {Types}", wxManagerId, string.Join(",", contactTypes));
        }
        finally
        {
            // 🔥 释放并发控制信号量
            _dbAccessSemaphore.Release();
        }
    }

    public async Task<ContactStatistics> GetContactStatisticsAsync(Guid wxManagerId)
    {
        // 🔥 添加并发控制，防止DbContext并发访问
        await _dbAccessSemaphore.WaitAsync();
        try
        {
            _logger.LogInformation("获取联系人统计信息 - ManagerId: {ManagerId}", wxManagerId);

            var stats = await _dbContext.WxContactEntities
                .Where(w => w.WxManagerId == wxManagerId)
                .GroupBy(w => w.ContactType)
                .Select(g => new
                {
                    ContactType = g.Key,
                    TotalCount = g.Count(),
                    AiEnabledCount = g.Count(w => w.IsAiEnabled)
                })
                .ToListAsync();

            var contactStats = stats.FirstOrDefault(s => s.ContactType == WxContactType.Contact);
            var groupStats = stats.FirstOrDefault(s => s.ContactType == WxContactType.Group);

            return new ContactStatistics
            {
                TotalContacts = contactStats?.TotalCount ?? 0,
                TotalGroups = groupStats?.TotalCount ?? 0,
                AiEnabledContacts = contactStats?.AiEnabledCount ?? 0,
                AiEnabledGroups = groupStats?.AiEnabledCount ?? 0,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取联系人统计信息失败 - ManagerId: {ManagerId}", wxManagerId);
            return new ContactStatistics { LastUpdated = DateTime.UtcNow };
        }
        finally
        {
            // 🔥 释放并发控制信号量
            _dbAccessSemaphore.Release();
        }
    }
}
